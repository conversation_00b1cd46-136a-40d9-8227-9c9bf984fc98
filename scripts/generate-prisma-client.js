#!/usr/bin/env node

/**
 * 生成Prisma客户端的脚本
 * 用于在数据库表创建后重新生成Prisma客户端
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 开始生成Prisma客户端...');

// 检查Prisma目录
const prismaDir = path.join(process.cwd(), 'server', 'apps', 'master');
if (!fs.existsSync(prismaDir)) {
    console.error('❌ Prisma目录不存在:', prismaDir);
    process.exit(1);
}

// 检查schema文件
const schemaFile = path.join(prismaDir, 'prisma', 'schema.prisma');
if (!fs.existsSync(schemaFile)) {
    console.error('❌ Prisma schema文件不存在:', schemaFile);
    process.exit(1);
}

console.log('✅ 找到Prisma配置目录:', prismaDir);

try {
    // 切换到Prisma目录
    process.chdir(prismaDir);
    console.log('📂 切换到目录:', prismaDir);
    
    // 检查是否安装了Prisma
    try {
        execSync('npx prisma --version', { stdio: 'pipe' });
        console.log('✅ Prisma CLI 可用');
    } catch (error) {
        console.error('❌ Prisma CLI 不可用，请先安装 @prisma/client');
        process.exit(1);
    }
    
    // 生成Prisma客户端
    console.log('🔄 正在生成Prisma客户端...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma客户端生成成功');
    
    // 验证生成的客户端
    const clientPath = path.join(prismaDir, 'node_modules', '.prisma', 'client');
    if (fs.existsSync(clientPath)) {
        console.log('✅ 客户端文件已生成:', clientPath);
        
        // 检查是否包含我们的模型
        const indexFile = path.join(clientPath, 'index.d.ts');
        if (fs.existsSync(indexFile)) {
            const content = fs.readFileSync(indexFile, 'utf8');
            if (content.includes('csm_delivery_method') && content.includes('csm_shipping_info')) {
                console.log('✅ 发货模块模型已包含在客户端中');
            } else {
                console.log('⚠️  发货模块模型可能未正确包含，请检查schema.prisma');
            }
        }
    } else {
        console.log('⚠️  客户端文件路径不存在，但生成可能成功');
    }
    
    console.log('');
    console.log('🎉 Prisma客户端生成完成！');
    console.log('');
    console.log('📝 下一步：');
    console.log('  1. 重启应用服务器');
    console.log('  2. 测试API接口');
    console.log('');
    console.log('🧪 测试命令：');
    console.log('  curl http://localhost:4000/api/v1/master/csm/shipping/delivery-methods/PO20250708312886');
    
} catch (error) {
    console.error('❌ 生成Prisma客户端失败:', error.message);
    process.exit(1);
}
