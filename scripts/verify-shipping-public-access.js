#!/usr/bin/env node

/**
 * 验证发货接口公开访问配置脚本
 * 用于快速检查发货接口是否可以无token访问
 */

const axios = require('axios');
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 默认配置
const DEFAULT_BASE_URL = 'http://localhost:4000/api/v1';
const DEFAULT_ORDER_NO = '313300020216';

console.log('🔧 发货接口公开访问验证工具');
console.log('=====================================');

// 询问用户配置
function askQuestion(question, defaultValue) {
  return new Promise((resolve) => {
    rl.question(`${question} (默认: ${defaultValue}): `, (answer) => {
      resolve(answer.trim() || defaultValue);
    });
  });
}

// 测试接口访问
async function testApiAccess(baseUrl, orderNo) {
  console.log('\n🚀 开始测试...');
  console.log(`API地址: ${baseUrl}`);
  console.log(`测试订单号: ${orderNo}`);
  
  const api = axios.create({
    baseURL: baseUrl,
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 10000
  });

  const tests = [
    {
      name: '获取配送方式（默认模式）',
      url: `/master/csm/shipping/delivery-methods/${orderNo}`,
      method: 'get'
    },
    {
      name: '获取配送方式（强制匹配模式）',
      url: `/master/csm/shipping/delivery-methods/${orderNo}?forceMatch=true`,
      method: 'get'
    }
  ];

  let successCount = 0;
  
  for (const test of tests) {
    console.log(`\n📋 测试: ${test.name}`);
    console.log(`   URL: ${test.url}`);
    
    try {
      const response = await api[test.method](test.url);
      
      if (response.status === 200 && response.data.code === 200) {
        console.log('   ✅ 成功');
        console.log(`   📊 返回数据: 订单号=${response.data.data?.orderNo}, 配送方式数量=${response.data.data?.deliveryMethods?.length || 0}`);
        successCount++;
      } else {
        console.log('   ⚠️  响应异常');
        console.log(`   📊 状态码: ${response.status}, 业务码: ${response.data.code}`);
        console.log(`   📊 消息: ${response.data.message}`);
      }
    } catch (error) {
      console.log('   ❌ 失败');
      
      if (error.response) {
        console.log(`   📊 HTTP状态码: ${error.response.status}`);
        console.log(`   📊 错误信息: ${error.response.data?.message || error.message}`);
        
        if (error.response.status === 401) {
          console.log('   💡 提示: 401错误表示需要认证，请检查auth.config.js配置');
        }
      } else if (error.code === 'ECONNREFUSED') {
        console.log('   💡 提示: 连接被拒绝，请确认服务器是否启动');
      } else {
        console.log(`   📊 错误: ${error.message}`);
      }
    }
  }

  console.log('\n📈 测试结果汇总');
  console.log('=====================================');
  console.log(`成功: ${successCount}/${tests.length}`);
  console.log(`失败: ${tests.length - successCount}/${tests.length}`);
  
  if (successCount === tests.length) {
    console.log('🎉 所有测试通过！发货接口公开访问配置正确。');
  } else if (successCount === 0) {
    console.log('❌ 所有测试失败！请检查以下配置：');
    console.log('   1. 服务器是否启动 (npm run dev 或 node server.js)');
    console.log('   2. auth.config.js 中是否添加了发货路径');
    console.log('   3. 服务器是否重启以加载新配置');
  } else {
    console.log('⚠️  部分测试失败，请检查配置和网络连接。');
  }
  
  return successCount === tests.length;
}

// 主函数
async function main() {
  try {
    // 获取用户输入
    const baseUrl = await askQuestion('请输入API基础地址', DEFAULT_BASE_URL);
    const orderNo = await askQuestion('请输入测试订单号', DEFAULT_ORDER_NO);
    
    // 执行测试
    const success = await testApiAccess(baseUrl, orderNo);
    
    if (!success) {
      console.log('\n🔧 故障排除建议:');
      console.log('1. 检查 server/config/auth.config.js 文件');
      console.log('2. 确认 publicPaths 数组中包含: \'/api/v1/master/csm/shipping/*\'');
      console.log('3. 重启服务器以加载新配置');
      console.log('4. 确认数据库中有配送方式数据');
    }
    
  } catch (error) {
    console.error('❌ 执行过程中发生错误:', error.message);
  } finally {
    rl.close();
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { testApiAccess };
