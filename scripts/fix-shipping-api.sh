#!/bin/bash

# 快速修复发货API的脚本
# 执行数据库创建和Prisma客户端生成

set -e

echo "🔧 开始修复发货API..."

# 检查当前目录
if [ ! -f "scripts/quick-setup-shipping.sql" ]; then
    echo "❌ 请在项目根目录下执行此脚本"
    exit 1
fi

# 步骤1: 创建数据库表
echo "📊 步骤1: 创建数据库表..."

# 从环境变量获取数据库连接信息
if [ -z "$DATABASE_URL" ]; then
    echo "⚠️  DATABASE_URL 环境变量未设置"
    echo "请设置数据库连接信息："
    read -p "数据库主机 (默认: localhost): " DB_HOST
    DB_HOST=${DB_HOST:-localhost}
    
    read -p "数据库端口 (默认: 5432): " DB_PORT
    DB_PORT=${DB_PORT:-5432}
    
    read -p "数据库名称: " DB_NAME
    if [ -z "$DB_NAME" ]; then
        echo "❌ 数据库名称不能为空"
        exit 1
    fi
    
    read -p "数据库用户名: " DB_USER
    if [ -z "$DB_USER" ]; then
        echo "❌ 数据库用户名不能为空"
        exit 1
    fi
    
    read -s -p "数据库密码: " DB_PASSWORD
    echo
    
    # 构建连接字符串
    export PGPASSWORD=$DB_PASSWORD
    DB_CONN="-h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
else
    echo "✅ 使用环境变量中的数据库连接"
    # 从DATABASE_URL解析连接信息（简化版本）
    DB_CONN=""
fi

# 执行SQL脚本
if [ -n "$DB_CONN" ]; then
    echo "🗄️  执行数据库创建脚本..."
    if psql $DB_CONN -f scripts/quick-setup-shipping.sql; then
        echo "✅ 数据库表创建成功"
    else
        echo "❌ 数据库表创建失败"
        exit 1
    fi
else
    echo "⚠️  跳过数据库表创建（请手动执行 scripts/quick-setup-shipping.sql）"
fi

# 步骤2: 生成Prisma客户端
echo "📦 步骤2: 生成Prisma客户端..."

if [ -d "server/apps/master" ]; then
    cd server/apps/master
    
    echo "🔄 生成Prisma客户端..."
    if npx prisma generate; then
        echo "✅ Prisma客户端生成成功"
    else
        echo "❌ Prisma客户端生成失败"
        cd - > /dev/null
        exit 1
    fi
    
    cd - > /dev/null
else
    echo "❌ Prisma目录不存在: server/apps/master"
    exit 1
fi

# 步骤3: 验证API
echo "🧪 步骤3: 验证API..."

# 等待一下让服务器重新加载
echo "⏳ 等待服务器重新加载..."
sleep 2

# 测试API接口
echo "🔍 测试API接口..."
if curl -s -f "http://localhost:4000/api/v1/master/csm/shipping/delivery-methods/PO20250708312886" > /dev/null; then
    echo "✅ API接口测试成功"
    
    # 显示实际响应
    echo "📋 API响应示例:"
    curl -s "http://localhost:4000/api/v1/master/csm/shipping/delivery-methods/PO20250708312886" | jq . 2>/dev/null || curl -s "http://localhost:4000/api/v1/master/csm/shipping/delivery-methods/PO20250708312886"
else
    echo "⚠️  API接口测试失败，可能需要重启服务器"
    echo "💡 请尝试重启应用服务器，然后测试："
    echo "   curl http://localhost:4000/api/v1/master/csm/shipping/delivery-methods/PO20250708312886"
fi

echo ""
echo "🎉 发货API修复完成！"
echo ""
echo "📝 完成的操作："
echo "  ✅ 创建了数据库表 (csm.csm_delivery_method, csm.csm_shipping_info)"
echo "  ✅ 插入了测试配送方式数据"
echo "  ✅ 更新了Prisma schema"
echo "  ✅ 生成了Prisma客户端"
echo ""
echo "🔄 如果API仍然不工作，请重启应用服务器："
echo "   npm run dev  # 或者你使用的启动命令"
echo ""
echo "🧪 测试命令："
echo "   curl http://localhost:4000/api/v1/master/csm/shipping/delivery-methods/PO20250708312886"
