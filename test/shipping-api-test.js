/**
 * 发货模块API测试脚本
 * 用于测试新增的发货API接口是否正常工作
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://127.0.0.1:4000/api/v1';
const TEST_TOKEN = 'your-test-token-here'; // 需要替换为实际的测试token

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// 测试函数
const tests = {
  // 测试根据订单号获取配送方式（默认模式）
  async testGetDeliveryMethods() {
    console.log('\n=== 测试获取配送方式（默认模式） ===');
    const testOrderNo = '313300020216'; // 测试订单号

    try {
      const response = await api.get(`/master/csm/shipping/delivery-methods/${testOrderNo}`);
      console.log('✅ 成功:', response.data);
      return response.data;
    } catch (error) {
      console.log('❌ 失败:', error.response?.data || error.message);
      return null;
    }
  },

  // 测试根据订单号获取配送方式（强制匹配模式）
  async testGetDeliveryMethodsForceMatch() {
    console.log('\n=== 测试获取配送方式（强制匹配模式） ===');
    const testOrderNo = '313300020216'; // 测试订单号

    try {
      const response = await api.get(`/master/csm/shipping/delivery-methods/${testOrderNo}`, {
        params: { forceMatch: true }
      });
      console.log('✅ 成功:', response.data);
      return response.data;
    } catch (error) {
      console.log('❌ 失败:', error.response?.data || error.message);
      return null;
    }
  },

  // 测试创建发货信息
  async testCreateShippingInfo() {
    console.log('\n=== 测试创建发货信息 ===');
    
    const testData = {
      orderNo: '313300020216',
      orderType: 1,
      deliveryMethodId: 1,
      expressCompanyName: '顺丰速运',
      expressCompanyId: 'SF',
      trackingNo: 'SF1234567890',
      shippingLocation: '广东省深圳市南山区科技园',
      businessContact: '张三',
      businessPhone: '***********',
      remarks: '测试发货信息'
    };

    try {
      const response = await api.post('/master/csm/shipping/shipping-info', testData);
      console.log('✅ 成功:', response.data);
      return response.data;
    } catch (error) {
      console.log('❌ 失败:', error.response?.data || error.message);
      return null;
    }
  }
};

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始测试发货模块API...');
  console.log('API Base URL:', BASE_URL);
  console.log('');

  // 测试获取配送方式（默认模式）
  await tests.testGetDeliveryMethods();

  // 测试获取配送方式（强制匹配模式）
  await tests.testGetDeliveryMethodsForceMatch();

  // 测试创建发货信息
  await tests.testCreateShippingInfo();

  console.log('\n✨ 测试完成');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = tests;
