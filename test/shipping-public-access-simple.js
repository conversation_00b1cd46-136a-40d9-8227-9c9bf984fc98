/**
 * 简单的发货接口公开访问测试
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:4000/api/v1';

console.log('🔓 测试发货接口公开访问...');
console.log('API Base URL:', BASE_URL);

// 创建不带认证的axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 测试获取配送方式
async function testGetDeliveryMethods() {
  console.log('\n=== 测试获取配送方式（无token） ===');
  const testOrderNo = '313300020216';
  
  try {
    const response = await api.get(`/master/csm/shipping/delivery-methods/${testOrderNo}`);
    console.log('✅ 成功:', {
      status: response.status,
      code: response.data.code,
      message: response.data.message,
      orderNo: response.data.data?.orderNo,
      channelId: response.data.data?.channelId,
      methodCount: response.data.data?.deliveryMethods?.length || 0
    });
    return true;
  } catch (error) {
    console.log('❌ 失败:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    return false;
  }
}

// 测试强制匹配模式
async function testForceMatch() {
  console.log('\n=== 测试强制匹配模式（无token） ===');
  const testOrderNo = '313300020216';
  
  try {
    const response = await api.get(`/master/csm/shipping/delivery-methods/${testOrderNo}?forceMatch=true`);
    console.log('✅ 成功:', {
      status: response.status,
      code: response.data.code,
      message: response.data.message,
      forceMatch: response.data.data?.forceMatch,
      methodCount: response.data.data?.deliveryMethods?.length || 0
    });
    return true;
  } catch (error) {
    console.log('❌ 失败:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    return false;
  }
}

// 运行测试
async function runTests() {
  const result1 = await testGetDeliveryMethods();
  const result2 = await testForceMatch();
  
  console.log('\n📊 测试结果:');
  console.log(`获取配送方式: ${result1 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`强制匹配模式: ${result2 ? '✅ 通过' : '❌ 失败'}`);
  
  if (result1 && result2) {
    console.log('🎉 所有测试通过！发货接口可以公开访问。');
  } else {
    console.log('⚠️  部分测试失败，请检查配置。');
  }
}

// 运行测试
runTests().catch(console.error);
