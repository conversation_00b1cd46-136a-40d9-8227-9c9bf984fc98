/**
 * 发货模块公开访问测试脚本
 * 验证发货接口可以在没有token的情况下正常访问
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://127.0.0.1:4000/api/v1';

console.log('🔓 测试发货接口公开访问功能...');
console.log('API Base URL:', BASE_URL);
console.log('');

// 创建不带认证的axios实例
const publicApi = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 创建带错误token的axios实例（用于对比测试）
const invalidTokenApi = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': 'Bearer invalid-token-12345',
    'Content-Type': 'application/json'
  }
});

// 测试函数
const tests = {
  // 测试无token访问获取配送方式
  async testPublicAccessDeliveryMethods() {
    console.log('\n=== 测试无token访问获取配送方式 ===');
    const testOrderNo = '313300020216';
    
    try {
      const response = await publicApi.get(`/master/csm/shipping/delivery-methods/${testOrderNo}`);
      console.log('✅ 无token访问成功:', {
        code: response.data.code,
        message: response.data.message,
        orderNo: response.data.data?.orderNo,
        methodCount: response.data.data?.deliveryMethods?.length || 0
      });
      return true;
    } catch (error) {
      console.log('❌ 无token访问失败:', error.response?.data || error.message);
      return false;
    }
  },

  // 测试错误token访问获取配送方式（应该也能成功，因为接口不验证token）
  async testInvalidTokenAccessDeliveryMethods() {
    console.log('\n=== 测试错误token访问获取配送方式 ===');
    const testOrderNo = '313300020216';
    
    try {
      const response = await invalidTokenApi.get(`/master/csm/shipping/delivery-methods/${testOrderNo}`);
      console.log('✅ 错误token访问成功（符合预期，接口不验证token）:', {
        code: response.data.code,
        message: response.data.message,
        orderNo: response.data.data?.orderNo,
        methodCount: response.data.data?.deliveryMethods?.length || 0
      });
      return true;
    } catch (error) {
      console.log('❌ 错误token访问失败:', error.response?.data || error.message);
      return false;
    }
  },

  // 测试强制匹配模式的公开访问
  async testPublicAccessForceMatch() {
    console.log('\n=== 测试强制匹配模式的公开访问 ===');
    const testOrderNo = '313300020216';
    
    try {
      const response = await publicApi.get(`/master/csm/shipping/delivery-methods/${testOrderNo}`, {
        params: { forceMatch: true }
      });
      console.log('✅ 强制匹配模式无token访问成功:', {
        code: response.data.code,
        message: response.data.message,
        orderNo: response.data.data?.orderNo,
        forceMatch: response.data.data?.forceMatch,
        methodCount: response.data.data?.deliveryMethods?.length || 0
      });
      return true;
    } catch (error) {
      console.log('❌ 强制匹配模式无token访问失败:', error.response?.data || error.message);
      return false;
    }
  },

  // 测试无token创建发货信息
  async testPublicAccessCreateShipping() {
    console.log('\n=== 测试无token创建发货信息 ===');
    
    const testData = {
      orderNo: '313300020216',
      orderType: 1,
      deliveryMethodId: 1,
      expressCompanyName: '顺丰速运',
      expressCompanyId: 'SF',
      trackingNo: 'SF' + Date.now(), // 使用时间戳避免重复
      shippingLocation: '广东省深圳市南山区科技园',
      businessContact: '测试联系人',
      businessPhone: '***********',
      remarks: '公开访问测试'
    };

    try {
      const response = await publicApi.post('/master/csm/shipping/shipping-info', testData);
      console.log('✅ 无token创建发货信息成功:', {
        code: response.data.code,
        message: response.data.message,
        shippingId: response.data.data?.id
      });
      return true;
    } catch (error) {
      console.log('❌ 无token创建发货信息失败:', error.response?.data || error.message);
      return false;
    }
  }
};

// 运行所有测试
async function runAllTests() {
  const results = [];
  
  results.push(await tests.testPublicAccessDeliveryMethods());
  results.push(await tests.testInvalidTokenAccessDeliveryMethods());
  results.push(await tests.testPublicAccessForceMatch());
  results.push(await tests.testPublicAccessCreateShipping());

  const successCount = results.filter(r => r).length;
  const totalCount = results.length;

  console.log('\n📊 测试结果汇总:');
  console.log(`成功: ${successCount}/${totalCount}`);
  console.log(`失败: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount === totalCount) {
    console.log('🎉 所有测试通过！发货接口公开访问功能正常。');
  } else {
    console.log('⚠️  部分测试失败，请检查接口配置。');
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = tests;
