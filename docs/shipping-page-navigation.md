# 发货页面跳转逻辑说明

## 概述

本文档说明了从HavedShipped页面跳转到SendGoods页面时的参数传递和处理逻辑。

## 跳转流程

### 1. HavedShipped页面 → SendGoods页面

**触发条件：** 用户在HavedShipped页面点击"去发货"按钮

**跳转逻辑：**
```javascript
// 在 pages/h5/SupplyChain/HavedShipped/[id].vue 中
const jump = () => {
  if (!shippingData.value) {
    console.error('发货数据不存在，无法跳转')
    return
  }

  // 构建跳转参数
  const query = {}
  
  // 添加原订单号
  if (shippingData.value.originalOrderNumber) {
    query.originalOrderNumber = shippingData.value.originalOrderNumber
  }
  
  // 添加采购订单号
  if (shippingData.value.purchaseOrderNumber) {
    query.purchaseOrderNumber = shippingData.value.purchaseOrderNumber
  }
  
  // 添加拆分订单号
  if (shippingData.value.splitOrderNumber) {
    query.splitOrderNumber = shippingData.value.splitOrderNumber
  }

  router.push({ 
    path: `/h5/SupplyChain/SendGoods`,
    query: query
  })
}
```

### 2. SendGoods页面接收参数

**参数接收逻辑：**
```javascript
// 在 pages/h5/SupplyChain/SendGoods.vue 中
const orderData = ref({
  orderNo: getOrderNumber(),
  originalOrderNumber: route.query.originalOrderNumber || null,
  purchaseOrderNumber: route.query.purchaseOrderNumber || null,
  splitOrderNumber: route.query.splitOrderNumber || null
})

// 获取订单号的优先级逻辑
function getOrderNumber() {
  // 优先级：拆分订单号 > 采购订单号 > 原订单号 > URL参数 > 默认值
  if (route.query.splitOrderNumber) {
    return route.query.splitOrderNumber
  }
  if (route.query.purchaseOrderNumber) {
    return route.query.purchaseOrderNumber
  }
  if (route.query.originalOrderNumber) {
    return route.query.originalOrderNumber
  }
  if (route.params.orderid) {
    return route.params.orderid
  }
  return '313300020216' // 默认值
}
```

## 参数说明

### 传递的参数

| 参数名 | 类型 | 说明 | 来源 |
|--------|------|------|------|
| `originalOrderNumber` | String | 原订单号 | shippingData.originalOrderNumber |
| `purchaseOrderNumber` | String | 采购订单号 | shippingData.purchaseOrderNumber |
| `splitOrderNumber` | String | 拆分订单号 | shippingData.splitOrderNumber |

### 订单号选择优先级

SendGoods页面会根据以下优先级选择用于获取配送方式的订单号：

1. **拆分订单号** (`splitOrderNumber`) - 最高优先级
2. **采购订单号** (`purchaseOrderNumber`) - 中等优先级
3. **原订单号** (`originalOrderNumber`) - 较低优先级
4. **URL路径参数** (`route.params.orderid`) - 兼容性支持
5. **默认值** (`313300020216`) - 最低优先级

## URL示例

### 不带参数的跳转
```
/h5/SupplyChain/SendGoods
```

### 带参数的跳转
```
/h5/SupplyChain/SendGoods?originalOrderNumber=ORD123456&purchaseOrderNumber=PO789012&splitOrderNumber=SPL345678
```

在这个例子中，SendGoods页面会使用 `SPL345678` 作为订单号来获取配送方式。

## 页面显示

### SendGoods页面订单信息显示

页面会显示当前使用的订单号以及相关的订单信息：

```
当前发货订单: SPL345678

原订单号: ORD123456
采购订单号: PO789012
拆分订单号: SPL345678
```

## 调试信息

在开发环境中，SendGoods页面会在控制台输出详细的调试信息：

```javascript
console.log('SendGoods组件挂载')
console.log('路由参数:', route.params)
console.log('查询参数:', route.query)
console.log('订单数据:', orderData.value)
console.log('使用的订单号:', orderData.value.orderNo)
```

## 注意事项

1. **数据完整性**：确保HavedShipped页面的shippingData包含必要的订单号信息
2. **参数验证**：SendGoods页面会验证接收到的参数，如果都为空则使用默认值
3. **向后兼容**：保持对原有URL路径参数的支持
4. **用户体验**：页面会清晰显示当前正在处理的订单信息

## 测试场景

### 场景1：完整参数跳转
- HavedShipped页面包含所有三种订单号
- 跳转时传递所有参数
- SendGoods页面使用拆分订单号

### 场景2：部分参数跳转
- HavedShipped页面只有原订单号和采购订单号
- 跳转时传递这两个参数
- SendGoods页面使用采购订单号

### 场景3：无参数跳转
- HavedShipped页面数据不完整
- 跳转时不传递参数
- SendGoods页面使用默认订单号

### 场景4：直接访问
- 用户直接访问SendGoods页面
- 没有查询参数
- 页面使用默认订单号或URL路径参数
