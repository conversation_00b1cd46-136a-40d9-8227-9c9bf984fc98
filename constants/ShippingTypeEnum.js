/**
 * 发货类型枚举 - 前端版本
 * 与后端 server/apps/master/constants/ShippingTypeEnum.js 保持一致
 */

export const ShippingTypeEnum = {
  /**
   * 快递物流
   */
  EXPRESS: 1,
  
  /**
   * 自定义物流
   */
  CUSTOM: 2,
  
  /**
   * 商家自送
   */
  MERCHANT_DELIVERY: 3,
  
  /**
   * 线下自取
   */
  OFFLINE_PICKUP: 4,
  
  /**
   * 无需物流
   */
  NO_LOGISTICS: 5,
  
  /**
   * 根据类型码获取类型名称
   * @param {number} typeCode - 类型码
   * @returns {string} - 类型名称
   */
  getTypeName(typeCode) {
    switch (parseInt(typeCode)) {
      case this.EXPRESS:
        return '快递物流';
      case this.CUSTOM:
        return '自定义物流';
      case this.MERCHANT_DELIVERY:
        return '商家自送';
      case this.OFFLINE_PICKUP:
        return '线下自取';
      case this.NO_LOGISTICS:
        return '无需物流';
      default:
        return '未知类型';
    }
  },
  
  /**
   * 获取所有发货类型选项
   * @returns {Array} - 发货类型选项数组
   */
  getAllOptions() {
    return [
      { label: this.getTypeName(this.EXPRESS), value: this.EXPRESS },
      { label: this.getTypeName(this.CUSTOM), value: this.CUSTOM },
      { label: this.getTypeName(this.MERCHANT_DELIVERY), value: this.MERCHANT_DELIVERY },
      { label: this.getTypeName(this.OFFLINE_PICKUP), value: this.OFFLINE_PICKUP },
      { label: this.getTypeName(this.NO_LOGISTICS), value: this.NO_LOGISTICS }
    ];
  },
  
  /**
   * 验证发货类型是否有效
   * @param {number} typeCode - 类型码
   * @returns {boolean} - 是否有效
   */
  isValidType(typeCode) {
    const validTypes = [this.EXPRESS, this.CUSTOM, this.MERCHANT_DELIVERY, this.OFFLINE_PICKUP, this.NO_LOGISTICS];
    return validTypes.includes(parseInt(typeCode));
  }
};

export default ShippingTypeEnum;
