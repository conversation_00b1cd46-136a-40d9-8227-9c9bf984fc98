// CRM申请认款相关模型

// 申请认款记录表
model CrmPaymentRecognitionApplication {
  // 主键
  id                  BigInt   @id /// 申请记录ID，雪花ID

  // 申请信息
  applicationSn       String   @unique @map("application_sn") @db.VarChar(64) /// 申请编号，自动生成，格式：CRM-PRA-YYYYMMDD-XXX
  applicantId         BigInt   @map("applicant_id") /// 申请人ID
  totalAmount         Decimal  @map("total_amount") @db.Decimal(13, 2) /// 申请认款总金额
  paymentDate         BigInt   @map("payment_date") /// 付款日期（毫秒时间戳）
  description         String?  @db.Text /// 备注说明

  // 状态信息
  status              Int      @default(0) /// 申请状态：0-待审核，1-审核通过，2-审核驳回
  auditorId           BigInt?  @map("auditor_id") /// 审核人ID
  auditTime           BigInt?  @map("audit_time") /// 审核时间（毫秒时间戳）
  auditRemark         String?  @map("audit_remark") @db.Text /// 审核备注

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  orders              CrmPaymentRecognitionOrder[]      /// 关联的订单
  vouchers            CrmPaymentRecognitionVoucher[]    /// 关联的付款凭证
  attachments         CrmPaymentRecognitionAttachment[] /// 关联的附件

  // 索引
  @@index([applicantId], map: "idx_crm_payment_recognition_application_applicant_id")
  @@index([status], map: "idx_crm_payment_recognition_application_status")
  @@index([paymentDate], map: "idx_crm_payment_recognition_application_payment_date")
  @@index([createdAt], map: "idx_crm_payment_recognition_application_created_at")
  @@index([deletedAt], map: "idx_crm_payment_recognition_application_deleted_at")
  @@map("payment_recognition_applications")
  @@schema("crm")
}

// 申请认款订单关联表
model CrmPaymentRecognitionOrder {
  // 主键
  id                  BigInt   @id /// 关联记录ID，雪花ID

  // 关联信息
  applicationId       BigInt   @map("application_id") /// 关联申请记录ID
  orderId             BigInt   @map("order_id") /// 订单ID
  orderAmount         Decimal  @map("order_amount") @db.Decimal(13, 2) /// 订单金额

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  application         CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  // 索引
  @@index([applicationId], map: "idx_crm_payment_recognition_order_application_id")
  @@index([orderId], map: "idx_crm_payment_recognition_order_order_id")
  @@index([deletedAt], map: "idx_crm_payment_recognition_order_deleted_at")
  @@map("payment_recognition_orders")
  @@schema("crm")
}

// 申请认款付款凭证关联表
model CrmPaymentRecognitionVoucher {
  // 主键
  id                  BigInt   @id /// 关联记录ID，雪花ID

  // 关联信息
  applicationId       BigInt   @map("application_id") /// 关联申请记录ID
  voucherId           BigInt   @map("voucher_id") /// 付款凭证ID

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  application         CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  // 索引
  @@index([applicationId], map: "idx_crm_payment_recognition_voucher_application_id")
  @@index([voucherId], map: "idx_crm_payment_recognition_voucher_voucher_id")
  @@index([deletedAt], map: "idx_crm_payment_recognition_voucher_deleted_at")
  @@map("payment_recognition_vouchers")
  @@schema("crm")
}

// 申请认款附件表
model CrmPaymentRecognitionAttachment {
  // 主键
  id                  BigInt   @id /// 附件记录ID，雪花ID

  // 关联信息
  applicationId       BigInt   @map("application_id") /// 关联申请记录ID

  // 附件信息
  fileName            String   @map("file_name") @db.VarChar(255) /// 文件名
  originalName        String   @map("original_name") @db.VarChar(255) /// 原始文件名
  fileUrl             String   @map("file_url") @db.Text /// 文件URL
  fileSize            BigInt   @map("file_size") /// 文件大小（字节）
  fileType            String   @map("file_type") @db.VarChar(100) /// 文件类型
  extension           String   @db.VarChar(10) /// 文件扩展名
  sortOrder           Int      @default(0) @map("sort_order") /// 排序顺序

  // 系统字段
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 软删除时间戳
  createdBy           BigInt?  @map("created_by")
  updatedBy           BigInt?  @map("updated_by")

  // 关联关系
  application         CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  // 索引
  @@index([applicationId], map: "idx_crm_payment_recognition_attachment_application_id")
  @@index([deletedAt], map: "idx_crm_payment_recognition_attachment_deleted_at")
  @@map("payment_recognition_attachments")
  @@schema("crm")
}
