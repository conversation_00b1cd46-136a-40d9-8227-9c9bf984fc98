generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["base", "master", "csm", "crm"]
}

// CRM 客户管理相关模型

// 客户基本信息表
model CrmCustomer {
  id              BigInt   @id // 雪花ID
  customerName    String   @map("customer_name") // 客户名称（必填）
  customerAlias   String?  @map("customer_alias") // 客户简称
  parentGroup     String?  @map("parent_group") // 所属集团
  source          Int      // 客户来源（必填）：0-主动获取，1-推荐介绍
  industry        Int?     // 所属行业：0-互联网，1-金融，2-教育，3-医疗，4-制造业，5-服务业
  status          Int      // 客户状态（必填）：0-在营，1-暂停营业，2-关闭
  companyScale    String?  @map("company_scale") // 企业规模
  provinceId      String?  @map("province_id") // 省份ID
  cityId          String?  @map("city_id") // 城市ID
  districtId      String?  @map("district_id") // 区县ID
  detailAddress   String?  @map("detail_address") // 详细地址
  remark          String?  // 客户备注

  // 业务员关联字段
  salesperson1Id  BigInt?  @map("salesperson_1_id") // 第一级业务员ID（必填）
  salesperson2Id  BigInt?  @map("salesperson_2_id") // 第二级业务员ID
  salesperson3Id  BigInt?  @map("salesperson_3_id") // 第三级业务员ID

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  financeInfo     CrmCustomerFinanceInfo?
  invoiceInfos    CrmCustomerInvoiceInfo[]
  contacts        CrmCustomerContact[]
  attachments     CrmCustomerAttachment[]
  orders          CrmCustomerOrder[]

  @@map("customers")
  @@schema("crm")
}

// 客户财务信息表
model CrmCustomerFinanceInfo {
  id                    BigInt   @id // 雪花ID
  customerId            BigInt   @unique @map("customer_id") // 客户ID
  accountHolderName     String?  @map("account_holder_name") // 开户人名称
  bankName              String?  @map("bank_name") // 开户行
  bankAccount           String?  @map("bank_account") // 银行账号
  accountPeriodBasis    Int?     @map("account_period_basis") // 账期依据：0-订单已发货，1-订单已收货，2-订单已开票
  settlementMethod      Int?     @map("settlement_method") // 结算方式：0-月结，1-固定账期，2-现销现结
  settlementDate        String?  @map("settlement_date") // 结算日期

  // 系统字段 - 使用毫秒时间戳
  createdAt             BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt             BigInt   @map("updated_at")
  deletedAt             BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy             BigInt?  @map("created_by") // 创建人
  updatedBy             BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer              CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_finance_info")
  @@schema("crm")
}

// 客户开票信息表（多个）
model CrmCustomerInvoiceInfo {
  id              BigInt   @id // 雪花ID
  customerId      BigInt   @map("customer_id") // 客户ID
  invoiceTitle    String   @map("invoice_title") // 发票抬头名称（必填）
  taxNumber       String?  @map("tax_number") // 税号
  companyAddress  String?  @map("company_address") // 单位地址
  companyPhone    String?  @map("company_phone") // 公司电话
  bankName        String?  @map("bank_name") // 开户银行
  bankAccount     String?  @map("bank_account") // 银行账号

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer        CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_invoice_info")
  @@schema("crm")
}

// 客户联系人信息表（多个）
model CrmCustomerContact {
  id              BigInt   @id // 雪花ID
  customerId      BigInt?  @map("customer_id") // 客户ID（可为空，支持独立联系人）
  contactName     String   @map("contact_name") // 联系人姓名（必填）
  contactPhone    String   @map("contact_phone") // 联系电话（必填）
  wechatId        String?  @map("wechat_id") // 微信号
  email           String?  // 电子邮箱
  department      String?  // 所属部门
  position        String?  // 公司职位
  birthday        BigInt?  // 生日日期（时间戳）
  provinceId      String?  @map("province_id") // 省份ID
  cityId          String?  @map("city_id") // 城市ID
  districtId      String?  @map("district_id") // 区县ID
  detailAddress   String?  @map("detail_address") // 详细地址
  isDefault       Boolean  @default(false) @map("is_default") // 默认联系人（必填）
  remark          String?  // 备注

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系（可选，支持独立联系人）
  customer        CrmCustomer? @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_contacts")
  @@schema("crm")
}

// 客户附件表（多图）
model CrmCustomerAttachment {
  id              BigInt   @id // 雪花ID
  customerId      BigInt   @map("customer_id") // 客户ID
  fileName        String?  @map("file_name") // 文件名（可选）
  fileUrl         String   @map("file_url") // 文件URL
  fileSize        Int?     @map("file_size") // 文件大小（字节）
  fileType        String?  @map("file_type") // 文件类型

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer        CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_attachments")
  @@schema("crm")
}

// 客户关联订单表
model CrmCustomerOrder {
  id              BigInt   @id // 雪花ID
  customerId      BigInt   @map("customer_id") // 客户ID
  orderId         BigInt   @map("order_id") // 订单ID
  orderNumber     String?  @map("order_number") // 订单号（冗余字段，便于查询）
  isTemporary     Int      @default(0) @map("is_temporary") // 是否为临时客户关联：0-正式客户 1-临时客户

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 软删除时间戳
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  customer        CrmCustomer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 添加唯一索引，确保一个订单只能关联一个客户
  @@unique([orderId])
  @@index([customerId])
  @@index([orderNumber])
  @@map("customer_orders")
  @@schema("crm")
}

// CRM联系人订单关联表
model CrmContactOrder {
  id              BigInt   @id // 雪花ID
  contactId       BigInt   @map("contact_id") // 联系人ID
  orderId         BigInt   @map("order_id") // 订单ID
  orderNumber     String?  @map("order_number") // 订单号（冗余字段）

  // 系统字段 - 使用毫秒时间戳
  createdAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("updated_at")
  deletedAt       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy       BigInt?  @map("created_by") // 创建人
  updatedBy       BigInt?  @map("updated_by") // 更新人

  // 添加索引
  @@unique([orderId])
  @@index([contactId])
  @@index([orderNumber])
  @@map("contact_orders")
  @@schema("crm")
}

// CRM订单分配表 - 用于直销模块订单分配给内部用户
model CrmOrderAssignment {
  // 主键
  id                  BigInt   @id @default(autoincrement()) /// 分配记录ID，自增长

  // 关联字段
  orderId             BigInt   @map("order_id") /// 订单ID，关联base.orders表
  orderReportId       BigInt?  @map("order_report_id") /// 报备信息ID（可选）
  assignedUserId      BigInt   @map("assigned_user_id") /// 被分配的内部用户ID，关联base.system_user表

  // 分配信息
  rate                Decimal  @db.Decimal(10, 4) /// 费率，小数格式
  assignmentAmount    Decimal? @db.Decimal(18, 2) /// 分配金额
  assignmentStatus    Int      @default(1) @map("assignment_status") /// 分配状态：1-已分配，2-已接受，3-已拒绝，4-已完成
  remark              String?  @db.Text /// 备注信息

  // 操作信息
  assignedBy          BigInt   @map("assigned_by") /// 分配人ID
  assignedAt          BigInt   @map("assigned_at") /// 分配时间戳（毫秒）
  acceptedAt          BigInt?  @map("accepted_at") /// 接受时间戳（毫秒）
  completedAt         BigInt?  @map("completed_at") /// 完成时间戳（毫秒）

  // 系统字段 - 使用毫秒时间戳
  createdAt           BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt           BigInt   @map("updated_at")
  deletedAt           BigInt?  @map("deleted_at") /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([orderId], map: "idx_crm_order_assignment_order_id")
  @@index([orderReportId], map: "idx_crm_order_assignment_order_report_id")
  @@index([assignedUserId], map: "idx_crm_order_assignment_assigned_user_id")
  @@index([assignmentStatus], map: "idx_crm_order_assignment_status")
  @@index([assignedAt], map: "idx_crm_order_assignment_assigned_at")
  @@index([deletedAt], map: "idx_crm_order_assignment_deleted_at")
  @@map("order_assignment")
  @@schema("crm")
}

// 直销模块申请认款记录表
model CrmPaymentRecognitionApplication {
  id                              BigInt   @id // 雪花ID，主键
  applicationSn                   String   @unique @map("application_sn") @db.VarChar(64) // 申请编号，自动生成，格式：PRA-YYYYMMDD-XXX

  // 申请信息
  applicantId                     BigInt   @map("applicant_id") // 申请人ID
  totalAmount                     Decimal  @map("total_amount") @db.Decimal(13,2) // 申请认款总金额
  paymentDate                     BigInt   @map("payment_date") // 付款日期（毫秒时间戳）
  description                     String?  @map("description") @db.Text // 备注说明

  // 认款凭证信息 - 移除单一凭证字段，改为关联表处理

  // 状态信息
  status                          Int      @default(0) @map("status") // 申请状态：0-待审核，1-审核通过，2-审核驳回
  auditorId                       BigInt?  @map("auditor_id") // 审核人ID
  auditTime                       BigInt?  @map("audit_time") // 审核时间（毫秒时间戳）
  auditRemark                     String?  @map("audit_remark") @db.Text // 审核备注

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  orders                          CrmPaymentRecognitionOrder[]
  vouchers                        CrmPaymentRecognitionVoucher[]
  attachments                     CrmPaymentRecognitionAttachment[]

  @@map("payment_recognition_applications")
  @@schema("crm")
  @@index([applicantId], map: "idx_crm_payment_recognition_application_applicant_id")
  @@index([status], map: "idx_crm_payment_recognition_application_status")
  @@index([paymentDate], map: "idx_crm_payment_recognition_application_payment_date")
  @@index([createdAt], map: "idx_crm_payment_recognition_application_created_at")
}

// 直销模块申请认款订单关联表
model CrmPaymentRecognitionOrder {
  id                              BigInt   @id // 雪花ID，主键
  applicationId                   BigInt   @map("application_id") // 关联申请记录ID
  orderId                         BigInt   @map("order_id") // 订单ID
  orderAmount                     Decimal  @map("order_amount") @db.Decimal(13,2) // 订单金额

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  application                     CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  @@map("payment_recognition_orders")
  @@schema("crm")
  @@index([applicationId], map: "idx_crm_payment_recognition_order_application_id")
  @@index([orderId], map: "idx_crm_payment_recognition_order_order_id")
}

// 直销模块申请认款付款凭证关联表
model CrmPaymentRecognitionVoucher {
  id                              BigInt   @id // 雪花ID，主键
  applicationId                   BigInt   @map("application_id") // 关联申请记录ID
  voucherId                       BigInt   @map("voucher_id") // 付款凭证ID

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  application                     CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  @@map("payment_recognition_vouchers")
  @@schema("crm")
  @@index([applicationId], map: "idx_crm_payment_recognition_voucher_application_id")
  @@index([voucherId], map: "idx_crm_payment_recognition_voucher_voucher_id")
}

// 直销模块申请认款附件表
model CrmPaymentRecognitionAttachment {
  id                              BigInt   @id // 雪花ID，主键
  applicationId                   BigInt   @map("application_id") // 关联申请记录ID
  fileName                        String   @map("file_name") @db.VarChar(255) // 文件名
  originalName                    String   @map("original_name") @db.VarChar(255) // 原始文件名
  fileUrl                         String   @map("file_url") @db.VarChar(500) // 文件URL
  fileSize                        BigInt   @map("file_size") // 文件大小（字节）
  fileType                        String   @map("file_type") @db.VarChar(100) // 文件类型
  extension                       String   @map("extension") @db.VarChar(20) // 文件扩展名
  sortOrder                       Int      @default(0) @map("sort_order") // 排序

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  application                     CrmPaymentRecognitionApplication @relation(fields: [applicationId], references: [id])

  @@map("payment_recognition_attachments")
  @@schema("crm")
  @@index([applicationId], map: "idx_crm_payment_recognition_attachment_application_id")
}

// ../models/invoice_headers.prisma
/// CRM发票抬头表，存储CRM模块用户的发票抬头信息
model CrmInvoiceHeader {
  // 主键
  id            BigInt    @id @default(autoincrement()) /// 发票抬头ID，主键

  // 关联字段
  user_id       BigInt                                  /// 用户ID，关联base.system_user表

  // 发票抬头基本信息
  name          String    @db.VarChar(100)              /// 发票抬头名称，必填
  tax_number    String?   @db.VarChar(50)               /// 纳税人识别号
  phone         String?   @db.VarChar(20)               /// 公司电话
  address       String?   @db.VarChar(200)              /// 公司地址

  // 银行信息
  bank_name     String?   @db.VarChar(100)              /// 开户银行名称
  bank_account  String?   @db.VarChar(30)               /// 银行账号

  // 状态信息
  is_default    Boolean   @default(false)               /// 是否为默认抬头
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用

  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt                                  /// 创建时间戳（毫秒）
  updated_at    BigInt                                  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）

  // 索引
  @@index([user_id], name: "idx_crm_invoice_headers_user_id")
  @@index([name], name: "idx_crm_invoice_headers_name")
  @@index([is_default], name: "idx_crm_invoice_headers_is_default")
  @@index([status], name: "idx_crm_invoice_headers_status")
  @@index([deleted_at], name: "idx_crm_invoice_headers_deleted_at")

  @@map("invoice_headers")
  @@schema("crm")
}
