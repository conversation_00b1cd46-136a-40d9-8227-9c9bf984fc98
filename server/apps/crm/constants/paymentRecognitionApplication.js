/**
 * 直销模块申请认款相关常量定义
 */

/**
 * 申请认款状态枚举
 */
const PaymentRecognitionApplicationStatusEnum = {
  /**
   * 待审核 - 申请已提交，等待审核
   */
  PENDING_AUDIT: 0,
  
  /**
   * 审核通过 - 申请审核通过
   */
  APPROVED: 1,
  
  /**
   * 审核驳回 - 申请审核驳回
   */
  REJECTED: 2,
  
  /**
   * 根据状态码获取状态名称
   * @param {number} statusCode - 状态码
   * @returns {string} - 状态名称
   */
  getStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.PENDING_AUDIT:
        return '待审核';
      case this.APPROVED:
        return '审核通过';
      case this.REJECTED:
        return '审核驳回';
      default:
        return '未知状态';
    }
  },

  /**
   * 获取所有状态选项（用于下拉框）
   * @returns {Array} - 状态选项数组
   */
  getAllOptions() {
    return [
      { label: '全部', value: '' },
      { label: this.getStatusName(this.PENDING_AUDIT), value: this.PENDING_AUDIT },
      { label: this.getStatusName(this.APPROVED), value: this.APPROVED },
      { label: this.getStatusName(this.REJECTED), value: this.REJECTED },
    ];
  }
};

/**
 * 认款凭证类型枚举
 */
const PaymentRecognitionVoucherTypeEnum = {
  /**
   * 付款凭证 - 银行转账等付款凭证
   */
  PAYMENT_VOUCHER: 0,
  
  /**
   * 选择已有付款凭证 - 从系统中选择已有的付款凭证
   */
  EXISTING_VOUCHER: 1,
  
  /**
   * 上传附件凭证 - 上传相关的付款凭证附件
   */
  UPLOAD_ATTACHMENT: 2,
  
  /**
   * 根据类型码获取类型名称
   * @param {number} typeCode - 类型码
   * @returns {string} - 类型名称
   */
  getTypeName(typeCode) {
    switch (parseInt(typeCode)) {
      case this.PAYMENT_VOUCHER:
        return '付款凭证';
      case this.EXISTING_VOUCHER:
        return '选择已有付款凭证';
      case this.UPLOAD_ATTACHMENT:
        return '上传附件凭证';
      default:
        return '未知类型';
    }
  },

  /**
   * 获取所有类型选项（用于下拉框）
   * @returns {Array} - 类型选项数组
   */
  getAllOptions() {
    return [
      { label: this.getTypeName(this.PAYMENT_VOUCHER), value: this.PAYMENT_VOUCHER },
      { label: this.getTypeName(this.EXISTING_VOUCHER), value: this.EXISTING_VOUCHER },
      { label: this.getTypeName(this.UPLOAD_ATTACHMENT), value: this.UPLOAD_ATTACHMENT },
    ];
  }
};

/**
 * 申请认款业务常量
 */
const PaymentRecognitionApplicationConstants = {
  /**
   * 申请编号前缀
   */
  APPLICATION_SN_PREFIX: 'CRM-PRA',
  
  /**
   * 最大订单数量限制
   */
  MAX_ORDER_COUNT: 50,
  
  /**
   * 最大附件数量限制
   */
  MAX_ATTACHMENT_COUNT: 10,
  
  /**
   * 支持的文件类型
   */
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  
  /**
   * 文件大小限制（字节）
   */
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  
  /**
   * 生成申请编号
   * @returns {string} - 申请编号
   */
  generateApplicationSn() {
    const now = new Date();
    const dateStr = now.getFullYear().toString() + 
                   (now.getMonth() + 1).toString().padStart(2, '0') + 
                   now.getDate().toString().padStart(2, '0');
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${this.APPLICATION_SN_PREFIX}-${dateStr}-${randomNum}`;
  }
};

module.exports = {
  PaymentRecognitionApplicationStatusEnum,
  PaymentRecognitionVoucherTypeEnum,
  PaymentRecognitionApplicationConstants
};
