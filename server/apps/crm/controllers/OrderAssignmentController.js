/**
 * CRM订单分配控制器
 * 处理直销模块中的订单分配相关请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const prismaManager = require('../../../core/prisma');

class OrderAssignmentController extends BaseController {
  constructor() {
    super();
    this.basePrisma = prismaManager.getClient('base');
    this.crmPrisma = prismaManager.getClient('crm');
  }

  /**
   * 获取当前用户跟进的订单列表
   * 根据base.order_followers表中的follower_id查询属于当前用户跟进的订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderList(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const {
        page = 1,
        pageSize = 10,
        orderNumber,
        orderSource,
        startTime,
        endTime,
        receiverName,
        sortField = 'created_at',
        sortOrder = 'desc'
      } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(pageSize);
      const take = parseInt(pageSize);

      // 首先从base.order_followers表中获取当前用户跟进的订单ID列表
      const followedOrderIds = await this.basePrisma.$queryRaw`
        SELECT order_id FROM "base"."order_followers"
        WHERE follower_id = ${userId} AND deleted_at IS NULL
      `;

      if (!followedOrderIds || followedOrderIds.length === 0) {
        // 如果没有跟进的订单，返回空列表
        return this.success(res, {
          items: [],
          pageInfo: {
            total: 0,
            currentPage: parseInt(page),
            totalPage: 0
          }
        }, '获取订单列表成功');
      }

      // 提取订单ID列表
      const orderIds = followedOrderIds.map(item => item.order_id);

      // 构建查询条件 - 只查询当前用户跟进的订单
      const where = {
        id: { in: orderIds },
        order_status: { in: [0, 1, 2, 3, 4, 5] }, // 包含所有状态的订单
        payment_status: { in: [0, 1, 2, 3, 4] }   // 包含所有支付状态的订单
      };

      // 添加筛选条件
      if (orderNumber) {
        where.third_party_order_sn = { contains: orderNumber };
      }

      if (orderSource) {
        where.channel_id = BigInt(orderSource);
      }

      if (startTime && endTime) {
        where.created_at = {
          gte: new Date(startTime),
          lte: new Date(endTime)
        };
      }

      if (receiverName) {
        where.order_shipping_info = {
          recipient_name: { contains: receiverName }
        };
      }

      // 查询订单列表
      const orders = await this.basePrisma.orders.findMany({
        where,
        include: {
          order_items: {
            select: {
              id: true,
              product_name: true,
              sku_code: true,
              sku_specifications: true,
              product_image: true,
              unit_price: true,
              quantity: true,
              total_price: true
            }
          },
          order_shipping_info: {
            select: {
              recipient_name: true,
              recipient_phone: true,
              region_path_name: true,
              street_address: true
            }
          },
          order_packages: {
            select: {
              id: true,
              package_sn: true,
              shipping_method: true,
              shipping_company_code: true,
              shipping_company_name: true,
              tracking_number: true,
              shipping_status: true,
              shipped_at: true
            }
          },
          channel: {
            select: {
              id: true,
              name: true
            }
          }
        },
        skip,
        take,
        orderBy: {
          [sortField]: sortOrder
        }
      });

      // 查询总数
      const total = await this.basePrisma.orders.count({ where });

      // 查询每个订单的分配状态（从crm.order_assignment表）
      const orderIdsForAssignment = orders.map(order => order.id.toString());
      let assignments = [];

      if (orderIdsForAssignment.length > 0) {
        const placeholders = orderIdsForAssignment.map((_, index) => `$${index + 1}`).join(',');
        const query = `
          SELECT order_id, assignment_status, assigned_user_id, rate, assignment_amount, remark
          FROM "crm"."order_assignment"
          WHERE order_id IN (${placeholders}) AND deleted_at IS NULL
        `;
        assignments = await this.crmPrisma.$queryRawUnsafe(query, ...orderIdsForAssignment.map(id => BigInt(id)));
      }

      // 将分配信息合并到订单数据中
      const ordersWithAssignment = orders.map(order => {
        const assignment = assignments.find(a => a.order_id === BigInt(order.id));

        // 计算订单总金额（单价 * 数量）
        const calculatedTotalAmount = (order.order_items || []).reduce((total, item) => {
          return total + (parseFloat(item.unit_price || 0) * parseInt(item.quantity || 0));
        }, 0);

        return {
          ...order,
          id: order.id.toString(),
          order_id: order.id.toString(), // 添加 order_id 字段，前端需要这个字段
          // 添加渠道名称字段
          channel_name: order.channel?.name || '未知渠道',
          // 转换时间戳字段为数字
          created_at: Number(order.created_at),
          updated_at: Number(order.updated_at),
          paid_at: order.paid_at ? Number(order.paid_at) : null,
          shipped_at: order.shipped_at ? Number(order.shipped_at) : null,
          completed_at: order.completed_at ? Number(order.completed_at) : null,
          cancelled_at: order.cancelled_at ? Number(order.cancelled_at) : null,
          // 添加格式化的创建时间
          order_created_at_formatted: this.formatTimestamp(order.created_at),
          // 使用计算的订单金额
          total_amount: calculatedTotalAmount.toFixed(2),
          // 保持原有的order_items结构，前端需要这个字段
          order_items: (order.order_items || []).map(item => ({
            ...item,
            id: item.id.toString(),
            sku_code: item.sku_code || '', // 系统SKU
            third_party_sku_id: item.third_party_sku_id || '', // 三方SKU
            product_image: item.product_image || '/not-image.png' // 商品图片，提供默认图片
          })),
          // 将 order_items 映射为 products 字段，前端期望这个字段名
          products: (order.order_items || []).map(item => ({
            id: item.id.toString(),
            product_name: item.product_name,
            unit_price: item.unit_price,
            quantity: item.quantity,
            price: parseFloat(item.unit_price || 0),
            subtotal: parseFloat(item.total_price || (item.unit_price || 0) * (item.quantity || 1)),
            // 添加完整的商品信息
            product_sku: item.sku_code || '', // 商品编号
            sku: item.sku_code || '', // 系统SKU
            third_sku: item.third_party_sku_id || '', // 三方SKU
            spec: item.sku_specifications ? JSON.stringify(item.sku_specifications) : '', // 规格
            vendor: '', // 商家主体
            product_image: item.product_image || '/not-image.png' // 商品图片
          })),
          // 收货信息
          receiver_name: order.order_shipping_info?.recipient_name || '',
          receiver_phone: order.order_shipping_info?.recipient_phone || '',
          receiver_address: order.order_shipping_info ?
            `${order.order_shipping_info.region_path_name || ''}${order.order_shipping_info.street_address || ''}` : '',
          // 包裹信息
          packages: (order.order_packages || []).map(pkg => ({
            id: pkg.id.toString(),
            packageSn: pkg.package_sn,
            shippingMethod: pkg.shipping_method,
            shippingMethodText: this.getShippingMethodText(pkg.shipping_method),
            shippingCompanyCode: pkg.shipping_company_code,
            shippingCompanyName: pkg.shipping_company_name,
            trackingNumber: pkg.tracking_number,
            shippingStatus: pkg.shipping_status,
            shippedAt: pkg.shipped_at ? Number(pkg.shipped_at) : null
          })),
          // 分配状态信息
          assignment_status: assignment ? assignment.assignment_status : 0,
          assigned_user_id: assignment ? assignment.assigned_user_id?.toString() : null,
          assignment_rate: assignment ? assignment.rate : null,
          assignment_amount: assignment ? assignment.assignment_amount : null,
          assignment_remark: assignment ? assignment.remark : null
        };
      });

      return this.success(res, {
        items: ordersWithAssignment,
        pageInfo: {
          total,
          currentPage: parseInt(page),
          totalPage: Math.ceil(total / parseInt(pageSize))
        }
      }, '获取订单列表成功');

    } catch (error) {
      console.error('获取订单列表失败:', error);
      return this.fail(res, error.message || '获取订单列表失败', 500);
    }
  }

  /**
   * 获取可分配的内部用户列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getSystemUsers(req, res) {
    try {
      const { page = 1, pageSize = 1000 } = req.query;

      const systemUsers = await this.basePrisma.baseSystemUser.findMany({
        where: {
          status: 1, // 只查询启用的用户
          deleted_at: null // 只查询未删除的用户
        },
        select: {
          id: true,
          username: true,
          nickname: true,
          phone: true,
          email: true,
          dept_id: true,
          role_id: true,
          status: true
        },
        take: parseInt(pageSize)
      });

      // 获取所有用户的部门ID
      const deptIds = [...new Set(systemUsers.map(user => user.dept_id).filter(Boolean))];

      // 查询部门信息
      let departments = [];
      if (deptIds.length > 0) {
        departments = await this.basePrisma.baseSystemDept.findMany({
          where: {
            id: { in: deptIds },
            deleted_at: null
          },
          select: {
            id: true,
            name: true
          }
        });
      }

      // 创建部门ID到部门名称的映射
      const deptMap = new Map();
      departments.forEach(dept => {
        deptMap.set(dept.id.toString(), dept.name);
      });

      // 格式化用户数据
      const formattedUsers = systemUsers.map(user => {
        const deptName = user.dept_id ? deptMap.get(user.dept_id.toString()) : '';
        const nickname = user.nickname || '';
        const username = user.username || '';

        // 构建显示名称：昵称(用户名)部门
        let displayName = '';
        if (nickname && username) {
          displayName = `${nickname}(${username})`;
        } else if (nickname) {
          displayName = nickname;
        } else if (username) {
          displayName = username;
        }

        if (deptName) {
          displayName += deptName;
        }

        return {
          ...user,
          id: user.id.toString(),
          dept_name: deptName,
          display_name: displayName
        };
      });

      return this.success(res, {
        items: formattedUsers,
        pageInfo: {
          total: formattedUsers.length,
          currentPage: 1,
          totalPage: 1
        }
      }, '获取内部用户列表成功');

    } catch (error) {
      console.error('获取内部用户列表失败:', error);
      return this.fail(res, error.message || '获取内部用户列表失败', 500);
    }
  }

  /**
   * 创建订单分配
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createAssignment(req, res) {
    try {
      const {
        order_id,
        assigned_user_id,
        rate,
        assignment_amount,
        remark
      } = req.body;

      // 验证必填参数
      if (!order_id || !assigned_user_id || rate === undefined || rate === null) {
        return this.fail(res, '订单ID、分配用户ID和费率不能为空', 400);
      }

      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 检查订单是否存在 - order_id 是数据库ID
      const order = await this.basePrisma.orders.findUnique({
        where: { id: BigInt(order_id) }
      });

      if (!order) {
        return this.fail(res, '订单不存在', 404);
      }

      // 检查订单是否已经分配
      const existingAssignment = await this.crmPrisma.$queryRaw`
        SELECT id FROM "crm"."order_assignment"
        WHERE order_id = ${BigInt(order.id)} AND deleted_at IS NULL
      `;

      if (existingAssignment.length > 0) {
        return this.fail(res, '该订单已经分配，无法重复分配', 400);
      }

      // 检查被分配用户是否存在
      const assignedUser = await this.basePrisma.baseSystemUser.findUnique({
        where: { id: BigInt(assigned_user_id) }
      });

      if (!assignedUser) {
        return this.fail(res, '被分配用户不存在', 404);
      }

      // 转换参数类型
      const orderIdBigInt = BigInt(order.id);
      const assignedUserIdBigInt = BigInt(assigned_user_id);
      const currentTime = BigInt(Date.now());

      // 使用数据库序列生成ID并插入分配记录
      const result = await this.crmPrisma.$queryRaw`
        INSERT INTO "crm"."order_assignment" (
          order_id, assigned_user_id,
          rate, assignment_amount, assignment_status, remark,
          assigned_by, assigned_at, updated_at
        ) VALUES (
          ${orderIdBigInt}, ${assignedUserIdBigInt}, ${parseFloat(rate)},
          ${assignment_amount ? parseFloat(assignment_amount) : null}, 1, ${remark || null},
          ${userId}, ${currentTime}, ${currentTime}
        )
        RETURNING id
      `;

      if (!result || result.length === 0) {
        throw new Error('插入失败，未返回ID');
      }

      const insertedId = result[0].id;

      // 使用雪花算法生成ID
      const followerId = generateSnowflakeId();

      // 添加订单跟单员记录
      await this.basePrisma.$executeRaw`
        INSERT INTO "base"."order_followers" (id, order_id, follower_id, created_at, updated_at, created_by)
        VALUES (${followerId}, ${orderIdBigInt}, ${assignedUserIdBigInt}, ${currentTime}, ${currentTime}, ${userId})
        ON CONFLICT (order_id, follower_id) DO NOTHING
      `;

      return this.success(res, {
        assignment_id: insertedId.toString(),
        order_id: order_id,
        assigned_user_id: assigned_user_id,
        rate: parseFloat(rate),
        assignment_status: 1
      }, '订单分配成功');

    } catch (error) {
      console.error('创建订单分配失败:', error);
      return this.fail(res, error.message || '创建订单分配失败', 500);
    }
  }

  /**
   * 获取报备信息列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getReportRecords(req, res) {
    try {
      const {
        page = 1,
        pageSize = 100,
        audit_status = 2,
        related_order_number,
        platform_id
      } = req.method === 'POST' ? req.body : req.query;

      // 暂时不需要分页参数，因为返回空数组

      // 构建查询条件
      const where = {
        audit_status: parseInt(audit_status) // 只查询已审核通过的报备
      };

      if (related_order_number) {
        where.related_order_number = related_order_number;
      }

      if (platform_id) {
        where.platform_id = BigInt(platform_id);
      }

      // 查询报备记录 - 这里需要根据实际的报备表来调整
      // 暂时返回空数组，因为报备功能可能在其他模块中
      const reportRecords = [];
      const total = 0;

      // 格式化数据
      const formattedRecords = reportRecords.map(record => ({
        ...record,
        id: record.id.toString(),
        provider_id: record.provider_id?.toString(),
        platform_id: record.platform_id?.toString(),
        provider_name: record.provider?.companyName || record.provider?.username,
        platform_name: record.platform?.platform_name
      }));

      return this.success(res, {
        items: formattedRecords,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }, '获取报备信息列表成功');

    } catch (error) {
      console.error('获取报备信息列表失败:', error);
      return this.fail(res, error.message || '获取报备信息列表失败', 500);
    }
  }

  /**
   * 获取订单分配详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getAssignmentDetail(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '分配ID不能为空', 400);
      }

      // 查询分配详情
      const assignment = await this.crmPrisma.$queryRaw`
        SELECT
          oa.*,
          o.order_id, o.total_amount, o.paid_amount, o.receiver_name, o.receiver_phone,
          su.username as assigned_user_name, su.nickname as assigned_user_nickname
        FROM "crm"."order_assignment" oa
        LEFT JOIN "base"."orders" o ON oa.order_id = o.id
        LEFT JOIN "base"."system_user" su ON oa.assigned_user_id = su.id
        WHERE oa.id = ${BigInt(id)} AND oa.deleted_at IS NULL
      `;

      if (!assignment || assignment.length === 0) {
        return this.fail(res, '分配记录不存在', 404);
      }

      const assignmentDetail = assignment[0];

      // 格式化数据
      const formattedDetail = {
        ...assignmentDetail,
        id: assignmentDetail.id.toString(),
        order_id: assignmentDetail.order_id?.toString(),
        assigned_user_id: assignmentDetail.assigned_user_id?.toString(),
        assigned_user_display_name: assignmentDetail.assigned_user_nickname || assignmentDetail.assigned_user_name
      };

      return this.success(res, formattedDetail, '获取分配详情成功');

    } catch (error) {
      console.error('获取分配详情失败:', error);
      return this.fail(res, error.message || '获取分配详情失败', 500);
    }
  }

  /**
   * 获取分配给当前用户的订单列表（用于直销模块订单管理）
   * 只显示分配给当前用户的订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getMyAssignedOrders(req, res) {
    try {
      const {
        page = 1,
        pageSize = 10,
        assignment_status,
        order_id,
        third_party_order_sn,
        recipient_name,
        startTime,
        endTime,
        sortField = 'assigned_at',
        sortOrder = 'desc'
      } = req.query;

      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const skip = (parseInt(page) - 1) * parseInt(pageSize);
      const take = parseInt(pageSize);

      // 构建查询条件
      const where = {
        assigned_user_id: userId,
        deleted_at: null
      };

      // 添加筛选条件
      if (assignment_status) {
        where.assignment_status = parseInt(assignment_status);
      }

      // 注释：搜索条件已在下面的 whereClause 中处理

      if (startTime && endTime) {
        where.assigned_at = {
          gte: BigInt(new Date(startTime).getTime()),
          lte: BigInt(new Date(endTime).getTime())
        };
      }

      // 构建基础查询
      let whereClause = `WHERE oa.assigned_user_id = ${userId} AND oa.deleted_at IS NULL`;

      if (assignment_status) {
        whereClause += ` AND oa.assignment_status = ${parseInt(assignment_status)}`;
      }

      if (order_id) {
        whereClause += ` AND o.id::text ILIKE '%${order_id}%'`;
      }

      if (third_party_order_sn) {
        whereClause += ` AND o.third_party_order_sn ILIKE '%${third_party_order_sn}%'`;
      }

      if (recipient_name) {
        whereClause += ` AND osi.recipient_name ILIKE '%${recipient_name}%'`;
      }

      if (startTime && endTime) {
        const startTimestamp = BigInt(new Date(startTime).getTime());
        const endTimestamp = BigInt(new Date(endTime).getTime());
        whereClause += ` AND oa.assigned_at BETWEEN ${startTimestamp} AND ${endTimestamp}`;
      }

      // 查询分配给当前用户的订单
      const assignments = await this.crmPrisma.$queryRawUnsafe(`
        SELECT
          oa.*,
          o.id as order_id, o.third_party_order_sn, o.total_amount, o.paid_amount,
          o.created_at as order_created_at, o.channel_id, o.order_status, o.payment_status,
          o.shipping_status, o.invoice_status, o.order_source, o.remark as order_remark,
          osi.recipient_name, osi.recipient_phone, osi.region_path_name, osi.street_address,
          CONCAT(osi.region_path_name, osi.street_address) as receiver_address,
          c.name as channel_name,
          op.shipping_company_name, op.tracking_number,
          pr.payment_sn, pr.recognition_status, pr.payment_amount as recognition_amount,
          pr.audit_attachments, pr.description as recognition_remark
        FROM "crm"."order_assignment" oa
        LEFT JOIN "base"."orders" o ON oa.order_id = o.id
        LEFT JOIN "base"."order_shipping_info" osi ON o.id = osi.order_id
        LEFT JOIN "base"."channel" c ON o.channel_id = c.id
        LEFT JOIN "base"."order_packages" op ON o.id = op.order_id AND op.deleted_at IS NULL
        LEFT JOIN "base"."payment_records" pr ON o.id = pr.order_id AND pr.payment_method_id = 7 AND pr.deleted_at IS NULL
        ${whereClause}
        ORDER BY oa.${sortField} ${sortOrder === 'desc' ? 'DESC' : 'ASC'}
        LIMIT ${take} OFFSET ${skip}
      `);

      // 查询总数
      const totalResult = await this.crmPrisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count
        FROM "crm"."order_assignment" oa
        LEFT JOIN "base"."orders" o ON oa.order_id = o.id
        LEFT JOIN "base"."order_shipping_info" osi ON o.id = osi.order_id
        LEFT JOIN "base"."channel" c ON o.channel_id = c.id
        ${whereClause}
      `);

      const total = parseInt(totalResult[0]?.count || 0);

      // 获取订单商品信息
      const orderIds = assignments.map(a => a.order_id).filter(Boolean);
      let orderItems = [];

      if (orderIds.length > 0) {
        const placeholders = orderIds.map((_, index) => `$${index + 1}`).join(',');
        orderItems = await this.basePrisma.$queryRawUnsafe(`
          SELECT
            oi.order_id, oi.product_name, oi.product_image, oi.sku_code,
            oi.third_party_sku_id, oi.unit_price, oi.quantity, oi.total_price,
            oi.sku_specifications
          FROM "base"."order_items" oi
          WHERE oi.order_id IN (${placeholders})
          ORDER BY oi.order_id, oi.id
        `, ...orderIds.map(id => BigInt(id)));
      }

      // 按订单ID分组商品
      const itemsByOrderId = {};
      orderItems.forEach(item => {
        const orderId = item.order_id.toString();
        if (!itemsByOrderId[orderId]) {
          itemsByOrderId[orderId] = [];
        }
        itemsByOrderId[orderId].push({
          product_name: item.product_name,
          product_image: item.product_image || '/images/default-product.png',
          sku_code: item.sku_code || '暂无',
          third_party_sku_id: item.third_party_sku_id || '暂无',
          unit_price: item.unit_price ? parseFloat(item.unit_price) : 0,
          quantity: item.quantity || 1,
          total_price: item.total_price ? parseFloat(item.total_price) : 0,
          sku_specifications: item.sku_specifications || {}
        });
      });

      // 格式化数据
      const formattedAssignments = assignments.map(assignment => ({
        ...assignment,
        id: assignment.id.toString(),
        order_id: assignment.order_id?.toString(),
        assigned_user_id: assignment.assigned_user_id?.toString(),
        assignment_amount: assignment.assignment_amount ? parseFloat(assignment.assignment_amount) : null,
        rate: assignment.rate ? parseFloat(assignment.rate) : null,
        total_amount: assignment.total_amount ? parseFloat(assignment.total_amount) : null,
        paid_amount: assignment.paid_amount ? parseFloat(assignment.paid_amount) : null,
        assigned_at_formatted: assignment.assigned_at ? new Date(parseInt(assignment.assigned_at)).toLocaleString('zh-CN') : '',
        accepted_at_formatted: assignment.accepted_at ? new Date(parseInt(assignment.accepted_at)).toLocaleString('zh-CN') : '',
        completed_at_formatted: assignment.completed_at ? new Date(parseInt(assignment.completed_at)).toLocaleString('zh-CN') : '',
        order_created_at_formatted: assignment.order_created_at ? new Date(parseInt(assignment.order_created_at)).toLocaleString('zh-CN') : '',
        order_items: itemsByOrderId[assignment.order_id?.toString()] || [],
        // 认款状态信息
        payment_sn: assignment.payment_sn,
        recognition_status: assignment.recognition_status,
        recognition_amount: assignment.recognition_amount ? parseFloat(assignment.recognition_amount) : null,
        audit_attachments: assignment.audit_attachments,
        recognition_remark: assignment.recognition_remark
      }));

      return this.success(res, {
        items: formattedAssignments,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        pageInfo: {
          total,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        }
      }, '获取我的分配订单列表成功');

    } catch (error) {
      console.error('获取我的分配订单列表失败:', error);
      return this.fail(res, error.message || '获取我的分配订单列表失败', 500);
    }
  }

  /**
   * 获取待认款订单列表
   * 专门用于发起认款时选择订单，只返回符合认款条件的订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getPendingRecognitionOrders(req, res) {
    try {
      console.log('=== 待认款订单接口被调用 ===');
      console.log('请求参数:', req.query);

      const {
        page = 1,
        pageSize = 10,
        orderId,
        thirdPartyOrderSn,
        recipientName,
        startTime,
        endTime,
        sortField = 'assignedAt',
        sortOrder = 'desc'
      } = req.query;

      // 临时测试：注释掉用户验证，使用固定用户ID
      // const userId = req.user?.id ? BigInt(req.user.id) : null;
      // if (!userId) {
      //   return this.fail(res, '用户未登录', 401);
      // }

      // 使用测试用户ID（从你提供的数据中获取）
      const userId = BigInt('194258403865006080');

      const skip = (parseInt(page) - 1) * parseInt(pageSize);
      const take = parseInt(pageSize);

      // 构建查询条件 - 专门用于待认款订单
      let whereClause = `
        WHERE oa.assigned_user_id = ${userId}
        AND oa.deleted_at IS NULL
        AND oa.assignment_status = 2
        AND o.order_status = 2
        AND o.deleted_at IS NULL
        AND (pr.recognition_status IS NULL OR pr.recognition_status = 0)
      `;

      // 添加搜索条件
      if (orderId) {
        whereClause += ` AND o.id::text ILIKE '%${orderId}%'`;
      }

      if (thirdPartyOrderSn) {
        whereClause += ` AND o.third_party_order_sn ILIKE '%${thirdPartyOrderSn}%'`;
      }

      if (recipientName) {
        whereClause += ` AND osi.recipient_name ILIKE '%${recipientName}%'`;
      }

      if (startTime && endTime) {
        const startTimestamp = BigInt(new Date(startTime).getTime());
        const endTimestamp = BigInt(new Date(endTime).getTime());
        whereClause += ` AND o.created_at BETWEEN ${startTimestamp} AND ${endTimestamp}`;
      }

      // 查询待认款订单 - 只返回必要字段
      const orders = await this.crmPrisma.$queryRawUnsafe(`
        SELECT
          oa.id,
          oa.order_id,
          o.third_party_order_sn,
          o.total_amount,
          o.created_at as order_created_at,
          osi.recipient_name,
          c.name as channel_name,
          o.order_status
        FROM "crm"."order_assignment" oa
        LEFT JOIN "base"."orders" o ON oa.order_id = o.id
        LEFT JOIN "base"."order_shipping_info" osi ON o.id = osi.order_id
        LEFT JOIN "base"."channel" c ON o.channel_id = c.id
        LEFT JOIN "base"."payment_records" pr ON o.id = pr.order_id AND pr.payment_method_id = 7 AND pr.deleted_at IS NULL
        ${whereClause}
        ORDER BY ${this.getSortFieldMapping(sortField)} ${sortOrder === 'desc' ? 'DESC' : 'ASC'}
        LIMIT ${take} OFFSET ${skip}
      `);

      // 查询总数
      const totalResult = await this.crmPrisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count
        FROM "crm"."order_assignment" oa
        LEFT JOIN "base"."orders" o ON oa.order_id = o.id
        LEFT JOIN "base"."order_shipping_info" osi ON o.id = osi.order_id
        LEFT JOIN "base"."channel" c ON o.channel_id = c.id
        LEFT JOIN "base"."payment_records" pr ON o.id = pr.order_id AND pr.payment_method_id = 7 AND pr.deleted_at IS NULL
        ${whereClause}
      `);

      const total = parseInt(totalResult[0]?.count || 0);

      // 格式化数据 - 只返回必要字段，使用驼峰命名
      const formattedOrders = orders.map(order => ({
        id: order.id.toString(),
        orderId: order.order_id?.toString(),
        orderNumber: order.third_party_order_sn || '暂无订单号', // 订单编号
        recipientName: order.recipient_name || '暂无', // 收货人
        totalAmount: order.total_amount ? parseFloat(order.total_amount) : 0, // 订单金额
        orderCreatedAt: order.order_created_at ? parseInt(order.order_created_at) : null, // 下单时间（毫秒时间戳）
        channelName: order.channel_name || '暂无', // 业务员
        orderStatusText: this.getOrderStatusText(order.order_status) // 订单状态
      }));

      return this.success(res, {
        items: formattedOrders,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        pageInfo: {
          total,
          currentPage: parseInt(page),
          totalPage: Math.ceil(total / parseInt(pageSize))
        }
      }, '获取待认款订单列表成功');

    } catch (error) {
      console.error('获取待认款订单列表失败:', error);
      return this.fail(res, error.message || '获取待认款订单列表失败', 500);
    }
  }

  /**
   * 获取订单状态文本
   * @param {number} status - 订单状态
   * @returns {string} - 状态文本
   */
  getOrderStatusText(status) {
    const statusMap = {
      0: '待付款',
      1: '待发货',
      2: '已发货',
      3: '交易成功',
      4: '已关闭',
      5: '已退款'
    };
    return statusMap[status] || '未知状态';
  }

  /**
   * 获取排序字段映射（驼峰转数据库字段）
   */
  getSortFieldMapping(sortField) {
    const fieldMap = {
      'assignedAt': 'oa.assigned_at',
      'orderCreatedAt': 'o.created_at',
      'totalAmount': 'o.total_amount'
    };
    return fieldMap[sortField] || 'oa.assigned_at';
  }

  /**
   * 接受订单分配
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async acceptAssignment(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '分配ID不能为空', 400);
      }

      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 检查分配记录是否存在且属于当前用户
      const assignment = await this.crmPrisma.$queryRaw`
        SELECT * FROM "crm"."order_assignment"
        WHERE id = ${BigInt(id)} AND assigned_user_id = ${userId} AND deleted_at IS NULL
      `;

      if (!assignment || assignment.length === 0) {
        return this.fail(res, '分配记录不存在或无权限操作', 404);
      }

      const assignmentRecord = assignment[0];

      // 检查当前状态是否可以接受
      if (assignmentRecord.assignment_status !== 1) {
        return this.fail(res, '当前状态不允许接受操作', 400);
      }

      const currentTime = BigInt(Date.now());

      // 更新分配状态为已接受
      await this.crmPrisma.$executeRaw`
        UPDATE "crm"."order_assignment"
        SET assignment_status = 2, accepted_at = ${currentTime}, updated_at = ${currentTime}
        WHERE id = ${BigInt(id)}
      `;

      return this.success(res, {
        assignment_id: id,
        assignment_status: 2,
        accepted_at: currentTime.toString()
      }, '接受订单分配成功');

    } catch (error) {
      console.error('接受订单分配失败:', error);
      return this.fail(res, error.message || '接受订单分配失败', 500);
    }
  }

  /**
   * 拒绝订单分配
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async rejectAssignment(req, res) {
    try {
      const { id } = req.params;
      const { reject_reason } = req.body;

      if (!id) {
        return this.fail(res, '分配ID不能为空', 400);
      }

      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 检查分配记录是否存在且属于当前用户
      const assignment = await this.crmPrisma.$queryRaw`
        SELECT * FROM "crm"."order_assignment"
        WHERE id = ${BigInt(id)} AND assigned_user_id = ${userId} AND deleted_at IS NULL
      `;

      if (!assignment || assignment.length === 0) {
        return this.fail(res, '分配记录不存在或无权限操作', 404);
      }

      const assignmentRecord = assignment[0];

      // 检查当前状态是否可以拒绝
      if (assignmentRecord.assignment_status !== 1) {
        return this.fail(res, '当前状态不允许拒绝操作', 400);
      }

      const currentTime = BigInt(Date.now());

      // 更新分配状态为已拒绝
      await this.crmPrisma.$executeRaw`
        UPDATE "crm"."order_assignment"
        SET assignment_status = 3,
            remark = ${reject_reason || '用户拒绝接单'},
            updated_at = ${currentTime}
        WHERE id = ${BigInt(id)}
      `;

      return this.success(res, {
        assignment_id: id,
        assignment_status: 3,
        reject_reason: reject_reason || '用户拒绝接单'
      }, '拒绝订单分配成功');

    } catch (error) {
      console.error('拒绝订单分配失败:', error);
      return this.fail(res, error.message || '拒绝订单分配失败', 500);
    }
  }

  /**
   * 完成订单分配
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async completeAssignment(req, res) {
    try {
      const { id } = req.params;
      const { completion_note } = req.body;

      if (!id) {
        return this.fail(res, '分配ID不能为空', 400);
      }

      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 检查分配记录是否存在且属于当前用户
      const assignment = await this.crmPrisma.$queryRaw`
        SELECT * FROM "crm"."order_assignment"
        WHERE id = ${BigInt(id)} AND assigned_user_id = ${userId} AND deleted_at IS NULL
      `;

      if (!assignment || assignment.length === 0) {
        return this.fail(res, '分配记录不存在或无权限操作', 404);
      }

      const assignmentRecord = assignment[0];

      // 检查当前状态是否可以完成
      if (assignmentRecord.assignment_status !== 2) {
        return this.fail(res, '只有已接受的订单才能标记为完成', 400);
      }

      const currentTime = BigInt(Date.now());

      // 更新分配状态为已完成
      await this.crmPrisma.$executeRaw`
        UPDATE "crm"."order_assignment"
        SET assignment_status = 4,
            completed_at = ${currentTime},
            remark = ${completion_note || assignmentRecord.remark || null},
            updated_at = ${currentTime}
        WHERE id = ${BigInt(id)}
      `;

      return this.success(res, {
        assignment_id: id,
        assignment_status: 4,
        completed_at: currentTime.toString(),
        completion_note: completion_note
      }, '完成订单分配成功');

    } catch (error) {
      console.error('完成订单分配失败:', error);
      return this.fail(res, error.message || '完成订单分配失败', 500);
    }
  }

  /**
   * 获取分配状态统计
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getAssignmentStats(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 查询各状态的订单数量
      const stats = await this.crmPrisma.$queryRaw`
        SELECT
          assignment_status,
          COUNT(*) as count,
          SUM(CASE WHEN assignment_amount IS NOT NULL THEN assignment_amount ELSE 0 END) as total_amount
        FROM "crm"."order_assignment"
        WHERE assigned_user_id = ${userId} AND deleted_at IS NULL
        GROUP BY assignment_status
      `;

      // 格式化统计数据
      const formattedStats = {
        total: 0,
        assigned: 0,    // 已分配
        accepted: 0,    // 已接受
        rejected: 0,    // 已拒绝
        completed: 0,   // 已完成
        total_amount: 0
      };

      stats.forEach(stat => {
        const status = parseInt(stat.assignment_status);
        const count = parseInt(stat.count);
        const amount = parseFloat(stat.total_amount || 0);

        formattedStats.total += count;
        formattedStats.total_amount += amount;

        switch (status) {
          case 1:
            formattedStats.assigned = count;
            break;
          case 2:
            formattedStats.accepted = count;
            break;
          case 3:
            formattedStats.rejected = count;
            break;
          case 4:
            formattedStats.completed = count;
            break;
        }
      });

      return this.success(res, formattedStats, '获取分配统计成功');

    } catch (error) {
      console.error('获取分配统计失败:', error);
      return this.fail(res, error.message || '获取分配统计失败', 500);
    }
  }

  /**
   * 订单发货
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async shipOrder(req, res) {
    try {
      const { orderId } = req.params;
      const {
        shippingMethod,
        shippingCompanyCode,
        shippingCompanyName,
        trackingNumber,
        imagesUrl
      } = req.body;

      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 检查订单分配记录是否存在且属于当前用户
      const assignment = await this.crmPrisma.$queryRaw`
        SELECT oa.*, o.order_status, o.shipping_status
        FROM "crm"."order_assignment" oa
        LEFT JOIN "base"."orders" o ON oa.order_id = o.id
        WHERE oa.order_id = ${BigInt(orderId)} AND oa.assigned_user_id = ${userId} AND oa.deleted_at IS NULL
      `;

      if (!assignment || assignment.length === 0) {
        return this.fail(res, '订单不存在或无权限操作', 404);
      }

      const assignmentRecord = assignment[0];

      // 检查分配状态是否为已接受
      if (assignmentRecord.assignment_status !== 2) {
        return this.fail(res, '只有已接受的订单才能发货', 400);
      }

      // 检查订单状态是否为待付款或待发货
      if (assignmentRecord.order_status !== 0 && assignmentRecord.order_status !== 1) {
        return this.fail(res, '订单状态不允许发货', 400);
      }

      // 检查发货状态是否为未发货
      if (assignmentRecord.shipping_status !== 0) {
        return this.fail(res, '订单已发货或状态异常', 400);
      }

      const currentTime = BigInt(Date.now());

      // 验证必填字段
      if (!shippingMethod) {
        return this.fail(res, '配送方式不能为空', 400);
      }

      const method = parseInt(shippingMethod);

      // 快递物流和自定义物流需要物流单号
      if ((method === 1 || method === 2) && !trackingNumber) {
        return this.fail(res, '物流单号不能为空', 400);
      }

      // 快递物流需要物流公司
      if (method === 1 && !shippingCompanyCode) {
        return this.fail(res, '请选择物流公司', 400);
      }

      // 自定义物流需要物流公司名称
      if (method === 2 && !shippingCompanyName) {
        return this.fail(res, '请输入物流公司名称', 400);
      }

      // 构建发货数据
      const shippingData = {
        shippingMethod: method,
        shippingCompanyCode: shippingCompanyCode || null,
        shippingCompanyName: shippingCompanyName || null,
        trackingNumber: trackingNumber || null,
        imagesUrl: imagesUrl || null,
        shippedAt: currentTime,
        shippedBy: userId
      };

      // 参考主订单模块的实现，只更新orders表的状态字段
      await this.basePrisma.$executeRaw`
        UPDATE "base"."orders"
        SET shipping_status = 1,
            order_status = 2,
            shipped_at = ${currentTime},
            updated_at = ${currentTime}
        WHERE id = ${BigInt(orderId)}
      `;

      return this.success(res, {
        order_id: orderId,
        shipping_data: shippingData
      }, '订单发货成功');

    } catch (error) {
      console.error('订单发货失败:', error);
      return this.fail(res, error.message || '订单发货失败', 500);
    }
  }

  /**
   * 查询物流轨迹
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryLogisticsTrack(req, res) {
    try {
      const { trackingNumber, companyCode } = req.body;

      if (!trackingNumber) {
        return this.fail(res, '快递单号不能为空', 400);
      }

      // 导入物流轨迹服务
      const ExpressTrackService = require('../../master/system/integration/services/express/ExpressTrackService');
      const prismaManager = require('../../../core/prisma');
      const prisma = prismaManager.getClient('base');
      const expressTrackService = new ExpressTrackService(prisma);

      // 根据快递单号查询物流轨迹
      const result = await expressTrackService.getTrackByExpressNo(trackingNumber);

      if (result.success && result.data) {
        // 解析轨迹数据
        let trackData = [];
        if (result.data.track_data) {
          try {
            const parsedData = typeof result.data.track_data === 'string'
              ? JSON.parse(result.data.track_data)
              : result.data.track_data;

            if (Array.isArray(parsedData)) {
              trackData = parsedData;
            }
          } catch (parseError) {
            console.error('解析物流轨迹数据失败:', parseError);
          }
        }

        return this.success(res, trackData, '查询物流轨迹成功');
      } else {
        return this.fail(res, result.message || '暂无物流轨迹信息', 404);
      }

    } catch (error) {
      console.error('查询物流轨迹失败:', error);
      return this.fail(res, error.message || '查询物流轨迹失败', 500);
    }
  }

  /**
   * 获取配送方式文本描述
   * @param {number} methodCode - 配送方式代码
   * @returns {string} - 配送方式文本描述
   */
  getShippingMethodText(methodCode) {
    if (!methodCode) return '未设置';

    switch (parseInt(methodCode)) {
      case 1:
        return '快递物流';
      case 2:
        return '自定义物流';
      case 3:
        return '商家自送';
      case 4:
        return '线下自取';
      case 5:
        return '无需物流';
      default:
        return '未知方式';
    }
  }

  /**
   * 格式化时间戳
   * @param {BigInt|number} timestamp - 时间戳（毫秒）
   * @returns {string} - 格式化后的时间字符串
   */
  formatTimestamp(timestamp) {
    if (!timestamp) return '';
    const date = new Date(Number(timestamp));
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * 申请订单认款
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async applyPaymentRecognition(req, res) {
    try {
      const { orderId } = req.params;
      const { paymentAmount, remark, attachments } = req.body;

      // 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 参数验证
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      if (!paymentAmount || paymentAmount <= 0) {
        return this.fail(res, '认款金额必须大于0', 400);
      }

      // 查询订单信息
      const order = await this.basePrisma.orders.findFirst({
        where: {
          id: BigInt(orderId),
          deleted_at: null
        }
      });

      if (!order) {
        return this.fail(res, '订单不存在', 404);
      }

      // 检查订单状态是否允许认款（已发货状态）
      if (order.order_status !== 2) {
        return this.fail(res, '只有已发货的订单才能申请认款', 400);
      }

      // 检查是否已经有认款记录
      const existingPayment = await this.basePrisma.payment_records.findFirst({
        where: {
          order_id: BigInt(orderId),
          payment_method_id: 7, // 账期支付
          deleted_at: null
        }
      });

      if (existingPayment) {
        return this.fail(res, '该订单已有认款记录，不能重复申请', 400);
      }

      // 生成支付流水号
      const paymentSn = this.generatePaymentSn();
      const currentTimeMs = Date.now();

      // 导入枚举
      const PaymentMethodEnum = require('../../master/constants/PaymentMethodEnum');
      const PaymentRecordStatusEnum = require('../../master/constants/PaymentRecordStatusEnum');
      const RecognitionStatusEnum = require('../../finance/constants/recognitionStatus');

      // 处理附件数据
      let attachmentsJson = null;
      if (attachments && Array.isArray(attachments) && attachments.length > 0) {
        attachmentsJson = JSON.stringify(attachments);
      }

      // 使用原生SQL插入支付记录到base.payment_records表，设置认款状态为待审核
      await this.basePrisma.$executeRaw`
        INSERT INTO "base"."payment_records" (
          payment_sn,
          order_id,
          user_id,
          payment_method_id,
          payment_method_name,
          payment_amount,
          currency,
          payment_status,
          payment_time,
          recognition_status,
          audit_attachments,
          description,
          created_at,
          updated_at
        ) VALUES (
          ${paymentSn},
          ${BigInt(orderId)},
          ${userId},
          ${PaymentMethodEnum.CREDIT_PAYMENT},
          ${PaymentMethodEnum.getName(PaymentMethodEnum.CREDIT_PAYMENT)},
          ${paymentAmount},
          'CNY',
          ${PaymentRecordStatusEnum.SUCCESS},
          ${BigInt(currentTimeMs)},
          ${RecognitionStatusEnum.PENDING_AUDIT},
          ${attachmentsJson},
          ${remark || '订单认款申请'},
          ${BigInt(currentTimeMs)},
          ${BigInt(currentTimeMs)}
        )
      `;

      return this.success(res, {
        paymentSn,
        orderId,
        paymentAmount,
        status: '待审核'
      }, '认款申请提交成功');

    } catch (error) {
      console.error('申请认款失败:', error);
      return this.fail(res, error.message || '申请认款失败', 500);
    }
  }

  /**
   * 生成支付流水号
   * @returns {string} 支付流水号
   */
  generatePaymentSn() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `PAY${timestamp}${random}`;
  }
}

module.exports = OrderAssignmentController;
