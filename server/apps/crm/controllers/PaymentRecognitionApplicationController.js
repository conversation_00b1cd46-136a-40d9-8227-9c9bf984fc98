/**
 * 直销模块申请认款控制器
 * 处理直销模块申请认款相关的业务逻辑
 */

const BaseController = require('../../../core/controllers/BaseController');
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const {
  CreatePaymentRecognitionApplicationDto,
  QueryPaymentRecognitionApplicationDto
} = require('../dto/PaymentRecognitionApplicationDto');
const {
  PaymentRecognitionApplicationStatusEnum,
  PaymentRecognitionVoucherTypeEnum,
  PaymentRecognitionApplicationConstants
} = require('../constants/paymentRecognitionApplication');

class PaymentRecognitionApplicationController extends BaseController {
  constructor() {
    super();
    this.prisma = prisma; // 使用共享的Prisma客户端
  }

  /**
   * 创建申请认款
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createApplication(req, res) {
    try {
      console.log('[DEBUG] 创建申请认款 - 开始处理请求');
      console.log('[DEBUG] 请求数据:', JSON.stringify(req.body, null, 2));

      // 获取当前用户ID（测试时使用默认值）
      const applicantId = req.user?.id || req.user?.user_id || '198693388495753200'; // 测试用默认用户ID
      console.log('[DEBUG] 申请人ID:', applicantId);

      // 先查询订单获取总金额
      console.log('[DEBUG] 查询订单，使用原生SQL跨schema查询');
      const orderIdsArray = req.body.orderIds.map(id => id.toString());
      const placeholders = orderIdsArray.map((_, index) => `$${index + 1}`).join(',');
      const ordersResult = await this.prisma.$queryRawUnsafe(`
        SELECT id, total_amount, order_status
        FROM base.orders
        WHERE id::text IN (${placeholders})
        AND deleted_at IS NULL
      `, ...orderIdsArray);

      // 转换查询结果
      const orders = ordersResult.map(row => ({
        id: row.id,
        total_amount: row.total_amount,
        order_status: row.order_status
      }));

      if (orders.length !== req.body.orderIds.length) {
        throw new Error('部分订单不存在或已删除');
      }

      // 计算总金额
      const totalAmount = orders.reduce((sum, order) => {
        return sum + parseFloat(order.total_amount);
      }, 0);

      console.log('[DEBUG] 计算的总金额:', totalAmount);

      // 数据验证（添加计算出的总金额）
      const dtoData = { ...req.body, totalAmount };
      const dto = new CreatePaymentRecognitionApplicationDto(dtoData);
      console.log('[DEBUG] 数据验证通过:', JSON.stringify(dto, null, 2));

      const currentTime = Date.now();

      // 验证订单状态（只有已发货的订单才能申请认款）
      const invalidOrders = orders.filter(order => order.order_status !== 2);
      if (invalidOrders.length > 0) {
        const invalidOrderIds = invalidOrders.map(o => o.id.toString());
        throw new Error(`订单 ${invalidOrderIds.join(', ')} 状态不符合认款条件`);
      }

      // 开始事务处理
      const result = await this.prisma.$transaction(async (prisma) => {
        // 2. 检查订单是否已有认款申请
        const existingApplications = await prisma.crmPaymentRecognitionOrder.findMany({
          where: {
            orderId: { in: dto.orderIds.map(id => BigInt(id)) },
            deletedAt: null
          },
          include: {
            application: {
              select: {
                id: true,
                applicationSn: true,
                status: true
              }
            }
          }
        });

        if (existingApplications.length > 0) {
          const conflictOrderIds = existingApplications.map(app => app.orderId.toString());
          throw new Error(`订单 ${conflictOrderIds.join(', ')} 已有认款申请`);
        }

        // 3. 生成申请编号
        const applicationSn = PaymentRecognitionApplicationConstants.generateApplicationSn();

        // 4. 创建申请记录
        const applicationId = generateSnowflakeId();
        const application = await prisma.crmPaymentRecognitionApplication.create({
          data: {
            id: applicationId,
            applicationSn,
            applicantId: BigInt(applicantId),
            totalAmount: dto.totalAmount,
            paymentDate: BigInt(dto.paymentDate),
            description: dto.description,
            status: PaymentRecognitionApplicationStatusEnum.PENDING_AUDIT,
            createdAt: BigInt(currentTime),
            updatedAt: BigInt(currentTime),
            createdBy: BigInt(applicantId),
            updatedBy: BigInt(applicantId)
          }
        });

        console.log('[DEBUG] 申请记录创建成功:', application.id.toString());

        // 5. 创建订单关联记录
        const orderData = orders.map(order => ({
          id: generateSnowflakeId(),
          applicationId: application.id,
          orderId: order.id,
          orderAmount: order.total_amount,
          createdAt: BigInt(currentTime),
          updatedAt: BigInt(currentTime),
          createdBy: BigInt(applicantId),
          updatedBy: BigInt(applicantId)
        }));

        await prisma.crmPaymentRecognitionOrder.createMany({
          data: orderData
        });

        console.log('[DEBUG] 订单关联记录创建成功，数量:', orderData.length);

        // 6. 创建付款凭证关联记录
        if (dto.selectedVoucherIds && dto.selectedVoucherIds.length > 0) {
          const voucherData = dto.selectedVoucherIds.map(voucherId => ({
            id: generateSnowflakeId(),
            applicationId: application.id,
            voucherId: BigInt(voucherId),
            createdAt: BigInt(currentTime),
            updatedAt: BigInt(currentTime),
            createdBy: BigInt(applicantId),
            updatedBy: BigInt(applicantId)
          }));

          await prisma.crmPaymentRecognitionVoucher.createMany({
            data: voucherData
          });

          console.log('[DEBUG] 付款凭证关联记录创建成功，数量:', voucherData.length);
        }

        // 7. 创建附件记录
        if (dto.attachments && dto.attachments.length > 0) {
          const attachmentData = dto.attachments.map((attachmentUrl, index) => {
            // 从URL中提取文件名和扩展名
            const url = new URL(attachmentUrl);
            const pathname = url.pathname;
            const fileName = pathname.split('/').pop() || `attachment_${index + 1}`;
            const extension = fileName.split('.').pop() || '';

            return {
              id: generateSnowflakeId(),
              applicationId: application.id,
              fileName: fileName,
              originalName: fileName,
              fileUrl: attachmentUrl,
              fileSize: BigInt(0), // 文件大小未知，设为0
              fileType: this.getFileTypeByExtension(extension),
              extension: extension,
              sortOrder: index,
              createdAt: BigInt(currentTime),
              updatedAt: BigInt(currentTime),
              createdBy: BigInt(applicantId),
              updatedBy: BigInt(applicantId)
            };
          });

          await prisma.crmPaymentRecognitionAttachment.createMany({
            data: attachmentData
          });

          console.log('[DEBUG] 附件记录创建成功，数量:', attachmentData.length);
        }

        return {
          id: application.id.toString(),
          applicationSn: application.applicationSn,
          status: application.status,
          orderCount: orders.length,
          voucherCount: dto.selectedVoucherIds?.length || 0,
          attachmentCount: dto.attachments?.length || 0
        };
      });

      console.log('[DEBUG] 申请认款创建成功:', JSON.stringify(result, null, 2));

      return this.success(res, result, '申请认款提交成功');

    } catch (error) {
      console.error('[ERROR] 创建申请认款失败:', error);
      return this.fail(res, error.message || '申请认款提交失败', 500);
    }
  }

  /**
   * 获取申请认款列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getApplicationList(req, res) {
    try {
      console.log('[DEBUG] 获取申请认款列表 - 开始处理请求');
      console.log('[DEBUG] 查询参数:', JSON.stringify(req.query, null, 2));

      // 数据验证
      const dto = new QueryPaymentRecognitionApplicationDto(req.query);
      
      const offset = (dto.page - 1) * dto.pageSize;

      // 构建查询条件
      const whereConditions = {
        deletedAt: null
      };

      if (dto.status !== null) {
        whereConditions.status = dto.status;
      }

      if (dto.applicantId) {
        whereConditions.applicantId = BigInt(dto.applicantId);
      }

      if (dto.applicationSn) {
        whereConditions.applicationSn = {
          contains: dto.applicationSn,
          mode: 'insensitive'
        };
      }

      if (dto.startDate || dto.endDate) {
        whereConditions.createdAt = {};
        if (dto.startDate) {
          whereConditions.createdAt.gte = BigInt(dto.startDate);
        }
        if (dto.endDate) {
          whereConditions.createdAt.lte = BigInt(dto.endDate);
        }
      }

      // 查询总数
      const total = await this.prisma.crmPaymentRecognitionApplication.count({
        where: whereConditions
      });

      // 查询列表数据 - 使用原生SQL查询以获取用户姓名和关联数据
      const applications = await this.prisma.$queryRawUnsafe(`
        SELECT
          pra.*,
          applicant.nickname as applicant_name,
          auditor.nickname as auditor_name,
          (
            SELECT json_agg(voucher_id)
            FROM "crm"."payment_recognition_vouchers"
            WHERE application_id = pra.id AND deleted_at IS NULL
          ) as voucher_ids
        FROM "crm"."payment_recognition_applications" pra
        LEFT JOIN "base"."system_user" applicant ON pra.applicant_id = applicant.id AND applicant.deleted_at IS NULL
        LEFT JOIN "base"."system_user" auditor ON pra.auditor_id = auditor.id AND auditor.deleted_at IS NULL
        WHERE pra.deleted_at IS NULL
        ${dto.status !== null ? `AND pra.status = ${dto.status}` : ''}
        ${dto.applicantId ? `AND pra.applicant_id = ${dto.applicantId}` : ''}
        ${dto.applicationSn ? `AND pra.application_sn ILIKE '%${dto.applicationSn}%'` : ''}
        ${dto.startDate ? `AND pra.created_at >= ${dto.startDate}` : ''}
        ${dto.endDate ? `AND pra.created_at <= ${dto.endDate}` : ''}
        ORDER BY pra.created_at DESC
        LIMIT ${dto.pageSize} OFFSET ${offset}
      `);

      // 获取每个申请的订单列表
      const applicationIds = applications.map(app => app.id.toString());
      let ordersMap = {};
      let attachmentsMap = {};

      if (applicationIds.length > 0) {
        // 查询订单信息
        const orders = await this.prisma.crmPaymentRecognitionOrder.findMany({
          where: {
            applicationId: { in: applicationIds.map(id => BigInt(id)) },
            deletedAt: null
          },
          select: {
            applicationId: true,
            orderId: true,
            orderAmount: true
          }
        });

        // 按申请ID分组订单
        orders.forEach(order => {
          const appId = order.applicationId.toString();
          if (!ordersMap[appId]) {
            ordersMap[appId] = [];
          }
          ordersMap[appId].push({
            orderId: order.orderId.toString(),
            orderAmount: parseFloat(order.orderAmount)
          });
        });

        // 查询附件信息
        const attachments = await this.prisma.crmPaymentRecognitionAttachment.findMany({
          where: {
            applicationId: { in: applicationIds.map(id => BigInt(id)) },
            deletedAt: null
          },
          select: {
            applicationId: true,
            id: true,
            fileName: true,
            fileUrl: true
          }
        });

        // 按申请ID分组附件
        attachments.forEach(attachment => {
          const appId = attachment.applicationId.toString();
          if (!attachmentsMap[appId]) {
            attachmentsMap[appId] = [];
          }
          attachmentsMap[appId].push({
            id: attachment.id.toString(),
            fileName: attachment.fileName,
            fileUrl: attachment.fileUrl
          });
        });
      }

      // 格式化返回数据
      const items = applications.map(app => {
        const appId = app.id.toString();
        const appOrders = ordersMap[appId] || [];
        const appAttachments = attachmentsMap[appId] || [];

        return {
          id: appId,
          applicationSn: app.application_sn,
          applicantId: app.applicant_id.toString(),
          applicantName: app.applicant_name || '未知用户',
          totalAmount: parseFloat(app.total_amount),
          paymentDate: app.payment_date.toString(),
          description: app.description,
          selectedVoucherIds: app.voucher_ids || [],
          status: app.status,
          statusName: PaymentRecognitionApplicationStatusEnum.getStatusName(app.status),
          auditorId: app.auditor_id ? app.auditor_id.toString() : null,
          auditorName: app.auditor_name || null,
          auditTime: app.audit_time ? app.audit_time.toString() : null,
          auditRemark: app.audit_remark,
          orderIds: appOrders.map(order => order.orderId), // 返回多个订单ID
          orders: appOrders, // 完整的订单信息
          orderCount: appOrders.length,
          voucherCount: (app.voucher_ids || []).length,
          attachments: appAttachments,
          attachmentCount: appAttachments.length,
          createdAt: app.created_at.toString(),
          updatedAt: app.updated_at.toString()
        };
      });

      const pageInfo = {
        total,
        currentPage: dto.page,
        totalPage: Math.ceil(total / dto.pageSize)
      };

      console.log('[DEBUG] 申请认款列表查询成功，总数:', total);

      return this.success(res, { items, pageInfo }, '查询成功');

    } catch (error) {
      console.error('[ERROR] 获取申请认款列表失败:', error);
      return this.fail(res, error.message || '查询失败', 500);
    }
  }

  /**
   * 获取申请认款详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getApplicationDetail(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '申请ID不能为空', 400);
      }

      console.log('[DEBUG] 获取申请认款详情 - ID:', id);

      const application = await this.prisma.crmPaymentRecognitionApplication.findFirst({
        where: {
          id: BigInt(id),
          deletedAt: null
        },
        include: {
          orders: {
            select: {
              id: true,
              orderId: true,
              orderAmount: true
            }
          },
          vouchers: {
            select: {
              id: true,
              voucherId: true
            }
          },
          attachments: {
            select: {
              id: true,
              fileName: true,
              originalName: true,
              fileUrl: true,
              fileSize: true,
              fileType: true,
              extension: true,
              sortOrder: true
            },
            orderBy: {
              sortOrder: 'asc'
            }
          }
        }
      });

      if (!application) {
        return this.fail(res, '申请记录不存在', 404);
      }

      // 获取订单详细信息，包括渠道和平台信息
      const orderIds = application.orders.map(order => order.orderId.toString());
      const orderIdsPlaceholders = orderIds.map((_, index) => `$${index + 1}`).join(',');
      const orderDetails = await this.prisma.$queryRawUnsafe(`
        SELECT
          o.id,
          o.id::text as order_sn,
          o.total_amount,
          o.channel_id,
          o.platform_id,
          o.store_id,
          c.name as channel_name,
          p.name as platform_name,
          s.name as store_name
        FROM "base"."orders" o
        LEFT JOIN "base"."channel" c ON o.channel_id = c.id AND c.deleted_at IS NULL
        LEFT JOIN "base"."platform" p ON o.platform_id = p.id AND p.deleted_at IS NULL
        LEFT JOIN "base"."store" s ON o.store_id = s.id AND s.deleted_at IS NULL
        WHERE o.id::text IN (${orderIdsPlaceholders}) AND o.deleted_at IS NULL
      `, ...orderIds);

      // 获取付款凭证详细信息
      const voucherIds = application.vouchers?.map(v => v.voucherId.toString()) || [];
      let voucherDetails = [];
      if (voucherIds.length > 0) {
        const voucherIdsPlaceholders = voucherIds.map((_, index) => `$${index + 1}`).join(',');
        voucherDetails = await this.prisma.$queryRawUnsafe(`
          SELECT
            pv.id,
            pv.voucher_number,
            pv.amount,
            pv.payment_date,
            pv.payer_name,
            pv.payer_bank_account,
            pv.payer_bank_name,
            pv.remarks
          FROM "finance"."payment_vouchers" pv
          WHERE pv.id::text IN (${voucherIdsPlaceholders}) AND pv.deleted_at IS NULL
        `, ...voucherIds);
      }

      // 格式化返回数据
      const result = {
        id: application.id.toString(),
        applicationSn: application.applicationSn,
        applicantId: application.applicantId.toString(),
        totalAmount: parseFloat(application.totalAmount),
        paymentDate: application.paymentDate.toString(),
        description: application.description,

        // 付款方银行信息
        payerBankAccount: application.payerBankAccount,
        payerBankName: application.payerBankName,

        // 状态信息
        status: application.status,
        statusName: PaymentRecognitionApplicationStatusEnum.getStatusName(application.status),
        auditorId: application.auditorId?.toString(),
        auditTime: application.auditTime?.toString(),
        auditRemark: application.auditRemark,

        // 订单信息（包含来源信息）
        orders: application.orders.map(order => {
          const orderDetail = orderDetails.find(od => od.id.toString() === order.orderId.toString());
          const orderSource = orderDetail ?
            `${orderDetail.channel_name || '未知渠道'}+${orderDetail.platform_name || '未知平台'}` :
            '未知来源';

          return {
            id: order.id.toString(),
            orderId: order.orderId.toString(),
            orderSn: orderDetail?.order_sn || '',
            orderAmount: parseFloat(order.orderAmount),
            totalAmount: orderDetail ? parseFloat(orderDetail.total_amount) : 0,
            orderSource: orderSource,
            orderSourceText: orderSource, // 添加带Text后缀的字段
            channelId: orderDetail?.channel_id?.toString(),
            channelName: orderDetail?.channel_name,
            platformId: orderDetail?.platform_id?.toString(),
            platformName: orderDetail?.platform_name,
            storeId: orderDetail?.store_id?.toString(),
            storeName: orderDetail?.store_name
          };
        }),

        // 付款凭证信息（多个）
        vouchers: voucherDetails.map(voucher => ({
          id: voucher.id.toString(),
          voucherNumber: voucher.voucher_number,
          amount: parseFloat(voucher.amount),
          paymentDate: voucher.payment_date?.toString(),
          payerName: voucher.payer_name,
          payerBankAccount: voucher.payer_bank_account,
          payerBankName: voucher.payer_bank_name,
          remarks: voucher.remarks
        })),

        // 附件凭证信息（多个）
        attachments: application.attachments.map(attachment => ({
          id: attachment.id.toString(),
          fileName: attachment.fileName,
          originalName: attachment.originalName,
          fileUrl: attachment.fileUrl,
          fileSize: attachment.fileSize.toString(),
          fileType: attachment.fileType,
          extension: attachment.extension,
          sortOrder: attachment.sortOrder
        })),

        // 系统信息
        createdAt: application.createdAt.toString(),
        updatedAt: application.updatedAt.toString()
      };

      console.log('[DEBUG] 申请认款详情查询成功');

      return this.success(res, result, '查询成功');

    } catch (error) {
      console.error('[ERROR] 获取申请认款详情失败:', error);
      return this.fail(res, error.message || '查询失败', 500);
    }
  }

  /**
   * 审核申请认款
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async auditApplication(req, res) {
    try {
      const { id } = req.params;
      const { action, auditRemark } = req.body;

      // 获取当前用户ID（测试时使用默认值）
      const auditorId = req.user?.id || req.user?.user_id || '198693388495753201'; // 测试用默认审核员ID
      console.log('[DEBUG] 审核员ID:', auditorId);

      // 参数验证
      if (!id) {
        return this.fail(res, '申请ID不能为空', 400);
      }

      if (!action || !['approve', 'reject'].includes(action)) {
        return this.fail(res, '审核操作必须是approve(通过)或reject(驳回)', 400);
      }

      console.log('[DEBUG] 审核申请认款 - ID:', id, '操作:', action);

      const currentTime = Date.now();

      // 开始事务处理
      const result = await this.prisma.$transaction(async (prisma) => {
        // 查询申请记录
        const application = await prisma.crmPaymentRecognitionApplication.findFirst({
          where: {
            id: BigInt(id),
            deletedAt: null
          }
        });

        if (!application) {
          throw new Error('申请记录不存在');
        }

        // 检查当前状态是否可以审核
        if (application.status !== PaymentRecognitionApplicationStatusEnum.PENDING_AUDIT) {
          throw new Error('该申请不是待审核状态，无法审核');
        }

        // 确定新状态
        const newStatus = action === 'approve'
          ? PaymentRecognitionApplicationStatusEnum.APPROVED
          : PaymentRecognitionApplicationStatusEnum.REJECTED;

        // 更新申请记录
        const updatedApplication = await prisma.crmPaymentRecognitionApplication.update({
          where: { id: BigInt(id) },
          data: {
            status: newStatus,
            auditorId: BigInt(auditorId),
            auditTime: BigInt(currentTime),
            auditRemark: auditRemark || null,
            updatedAt: BigInt(currentTime),
            updatedBy: BigInt(auditorId)
          }
        });

        return {
          id: updatedApplication.id.toString(),
          status: updatedApplication.status,
          statusName: PaymentRecognitionApplicationStatusEnum.getStatusName(updatedApplication.status),
          auditTime: updatedApplication.auditTime.toString()
        };
      });

      const successMessage = action === 'approve' ? '审核通过' : '审核驳回';
      console.log('[DEBUG] 申请认款审核成功:', successMessage);

      return this.success(res, result, successMessage);

    } catch (error) {
      console.error('[ERROR] 审核申请认款失败:', error);
      return this.fail(res, error.message || '审核失败', 500);
    }
  }

  /**
   * 根据文件扩展名获取文件类型
   * @param {string} extension - 文件扩展名
   * @returns {string} - 文件MIME类型
   */
  getFileTypeByExtension(extension) {
    const mimeTypes = {
      'pdf': 'application/pdf',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'txt': 'text/plain',
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed'
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }
}

module.exports = PaymentRecognitionApplicationController;
