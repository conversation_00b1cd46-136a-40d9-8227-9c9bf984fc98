/**
 * 直销模块申请认款数据传输对象
 */

const { PaymentRecognitionApplicationConstants } = require('../constants/paymentRecognitionApplication');

/**
 * 创建申请认款DTO
 */
class CreatePaymentRecognitionApplicationDto {
  constructor(data = {}) {
    // 基本信息验证
    this.orderIds = this.validateOrderIds(data.orderIds);
    this.totalAmount = this.validateTotalAmount(data.totalAmount);
    this.paymentDate = this.validatePaymentDate(data.paymentDate);
    this.description = this.validateDescription(data.description);

    // 认款凭证信息 - 修改为支持付款凭证和附件凭证并存
    this.selectedVoucherIds = this.validateSelectedVoucherIds(data.selectedVoucherIds);
    this.attachments = this.validateAttachments(data.attachments);
  }

  /**
   * 验证订单ID列表
   * @param {Array} orderIds - 订单ID数组
   * @returns {Array} - 验证后的订单ID数组
   */
  validateOrderIds(orderIds) {
    if (!Array.isArray(orderIds) || orderIds.length === 0) {
      throw new Error('订单ID列表不能为空');
    }
    
    if (orderIds.length > PaymentRecognitionApplicationConstants.MAX_ORDER_COUNT) {
      throw new Error(`订单数量不能超过${PaymentRecognitionApplicationConstants.MAX_ORDER_COUNT}个`);
    }
    
    // 验证每个订单ID都是有效的
    const validOrderIds = orderIds.filter(id => {
      const numId = parseInt(id);
      return !isNaN(numId) && numId > 0;
    });
    
    if (validOrderIds.length !== orderIds.length) {
      throw new Error('存在无效的订单ID');
    }
    
    // 去重
    const uniqueOrderIds = [...new Set(validOrderIds)];
    if (uniqueOrderIds.length !== orderIds.length) {
      throw new Error('订单ID列表中存在重复项');
    }
    
    return uniqueOrderIds;
  }

  /**
   * 验证总金额
   * @param {number|string} totalAmount - 总金额
   * @returns {number} - 验证后的总金额
   */
  validateTotalAmount(totalAmount) {
    const amount = parseFloat(totalAmount);
    
    if (isNaN(amount) || amount <= 0) {
      throw new Error('认款总金额必须大于0');
    }
    
    if (amount > 999999999.99) {
      throw new Error('认款总金额不能超过999,999,999.99');
    }
    
    // 保留两位小数
    return Math.round(amount * 100) / 100;
  }

  /**
   * 验证付款日期
   * @param {string|number} paymentDate - 付款日期
   * @returns {number} - 验证后的付款日期（毫秒时间戳）
   */
  validatePaymentDate(paymentDate) {
    let timestamp;
    
    if (typeof paymentDate === 'string') {
      // 如果是字符串，尝试解析为日期
      const date = new Date(paymentDate);
      if (isNaN(date.getTime())) {
        throw new Error('付款日期格式无效');
      }
      timestamp = date.getTime();
    } else if (typeof paymentDate === 'number') {
      timestamp = paymentDate;
    } else {
      throw new Error('付款日期不能为空');
    }
    
    // 验证日期不能是未来时间
    const now = Date.now();
    if (timestamp > now) {
      throw new Error('付款日期不能是未来时间');
    }
    
    // 验证日期不能太久远（比如不能超过10年前）
    const tenYearsAgo = now - (10 * 365 * 24 * 60 * 60 * 1000);
    if (timestamp < tenYearsAgo) {
      throw new Error('付款日期不能超过10年前');
    }
    
    return timestamp;
  }

  /**
   * 验证备注说明
   * @param {string} description - 备注说明
   * @returns {string|null} - 验证后的备注说明
   */
  validateDescription(description) {
    if (!description) {
      return null;
    }
    
    if (typeof description !== 'string') {
      throw new Error('备注说明必须是字符串');
    }
    
    if (description.length > 1000) {
      throw new Error('备注说明不能超过1000个字符');
    }
    
    return description.trim();
  }

  /**
   * 验证选择的付款凭证ID列表
   * @param {Array} selectedVoucherIds - 选择的付款凭证ID列表
   * @returns {Array|null} - 验证后的付款凭证ID列表
   */
  validateSelectedVoucherIds(selectedVoucherIds) {
    if (!selectedVoucherIds || !Array.isArray(selectedVoucherIds)) {
      return null;
    }

    if (selectedVoucherIds.length === 0) {
      return null;
    }

    // 验证每个ID都是有效的
    const validIds = selectedVoucherIds.filter(id => {
      const numId = parseInt(id);
      return !isNaN(numId) && numId > 0;
    });

    if (validIds.length === 0) {
      return null;
    }

    // 去重
    const uniqueIds = [...new Set(validIds.map(id => parseInt(id)))];

    if (uniqueIds.length > 10) {
      throw new Error('最多只能选择10个付款凭证');
    }

    return uniqueIds;
  }

  /**
   * 验证附件列表
   * @param {Array} attachments - 附件URL数组
   * @returns {Array} - 验证后的附件URL数组
   */
  validateAttachments(attachments) {
    if (!attachments) {
      return [];
    }

    if (!Array.isArray(attachments)) {
      throw new Error('附件列表必须是数组');
    }

    if (attachments.length > PaymentRecognitionApplicationConstants.MAX_ATTACHMENT_COUNT) {
      throw new Error(`附件数量不能超过${PaymentRecognitionApplicationConstants.MAX_ATTACHMENT_COUNT}个`);
    }

    // 验证每个附件URL
    return attachments.map((attachmentUrl, index) => {
      if (typeof attachmentUrl !== 'string' || !attachmentUrl.trim()) {
        throw new Error(`第${index + 1}个附件URL不能为空`);
      }

      // 验证URL格式
      try {
        new URL(attachmentUrl);
      } catch (error) {
        throw new Error(`第${index + 1}个附件URL格式无效`);
      }

      return attachmentUrl.trim();
    });
  }


}

/**
 * 查询申请认款列表DTO
 */
class QueryPaymentRecognitionApplicationDto {
  constructor(query = {}) {
    this.page = this.validatePage(query.page);
    this.pageSize = this.validatePageSize(query.pageSize);
    this.status = this.validateStatus(query.status);
    this.applicantId = this.validateApplicantId(query.applicantId);
    this.startDate = this.validateDate(query.startDate);
    this.endDate = this.validateDate(query.endDate);
    this.applicationSn = this.validateApplicationSn(query.applicationSn);
  }

  validatePage(page) {
    const pageNum = parseInt(page) || 1;
    return pageNum > 0 ? pageNum : 1;
  }

  validatePageSize(pageSize) {
    const size = parseInt(pageSize) || 20;
    return size > 0 && size <= 100 ? size : 20;
  }

  validateStatus(status) {
    if (status === undefined || status === null || status === '') {
      return null;
    }
    const statusNum = parseInt(status);
    return !isNaN(statusNum) && [0, 1, 2].includes(statusNum) ? statusNum : null;
  }

  validateApplicantId(applicantId) {
    if (!applicantId) return null;
    const id = parseInt(applicantId);
    return !isNaN(id) && id > 0 ? id : null;
  }

  validateDate(date) {
    if (!date) return null;
    const timestamp = new Date(date).getTime();
    return !isNaN(timestamp) ? timestamp : null;
  }

  validateApplicationSn(applicationSn) {
    if (!applicationSn || typeof applicationSn !== 'string') {
      return null;
    }
    return applicationSn.trim();
  }
}

module.exports = {
  CreatePaymentRecognitionApplicationDto,
  QueryPaymentRecognitionApplicationDto
};
