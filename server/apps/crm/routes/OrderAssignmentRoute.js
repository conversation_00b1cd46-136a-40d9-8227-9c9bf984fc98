/**
 * CRM订单分配路由
 * 处理直销模块中的订单分配相关路由配置
 */
const express = require('express');
const router = express.Router();
const OrderAssignmentController = require('../controllers/OrderAssignmentController');
const PaymentRecognitionApplicationController = require('../controllers/PaymentRecognitionApplicationController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new OrderAssignmentController();
const paymentRecognitionController = new PaymentRecognitionApplicationController();

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(express.Router());

/**
 * @swagger
 * /api/v1/crm/order-assignment/orders:
 *   get:
 *     tags: [CRM订单分配]
 *     summary: 获取订单分配列表
 *     description: 获取可分配的订单列表，包含分配状态信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: orderNumber
 *         in: query
 *         description: 订单号
 *         schema:
 *           type: string
 *       - name: orderSource
 *         in: query
 *         description: 订单来源
 *         schema:
 *           type: string
 *       - name: startTime
 *         in: query
 *         description: 开始时间
 *         schema:
 *           type: string
 *       - name: endTime
 *         in: query
 *         description: 结束时间
 *         schema:
 *           type: string
 *       - name: receiverName
 *         in: query
 *         description: 收货人姓名
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取订单列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
protectedRouter.get('/orders', controller.getOrderList.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/system-users:
 *   get:
 *     tags: [CRM订单分配]
 *     summary: 获取内部用户列表
 *     description: 获取可分配的内部用户列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量
 *         schema:
 *           type: integer
 *           default: 1000
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取内部用户列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           username:
 *                             type: string
 *                           nickname:
 *                             type: string
 *                           display_name:
 *                             type: string
 *                           phone:
 *                             type: string
 *                           email:
 *                             type: string
 *                     total:
 *                       type: integer
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
protectedRouter.get('/system-users', controller.getSystemUsers.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/assign:
 *   post:
 *     tags: [CRM订单分配]
 *     summary: 创建订单分配
 *     description: 将订单分配给内部用户
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - order_id
 *               - assigned_user_id
 *               - rate
 *             properties:
 *               order_id:
 *                 type: string
 *                 description: 订单ID
 *               order_report_id:
 *                 type: string
 *                 description: 报备信息ID（可选）
 *               assigned_user_id:
 *                 type: string
 *                 description: 被分配的内部用户ID
 *               rate:
 *                 type: number
 *                 description: 费率
 *               assignment_amount:
 *                 type: number
 *                 description: 分配金额
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 分配成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 订单分配成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     assignment_id:
 *                       type: string
 *                     order_id:
 *                       type: string
 *                     assigned_user_id:
 *                       type: string
 *                     rate:
 *                       type: number
 *                     assignment_status:
 *                       type: integer
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单或用户不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/assign', controller.createAssignment.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/report-records:
 *   get:
 *     tags: [CRM订单分配]
 *     summary: 获取报备信息列表
 *     description: 获取订单相关的报备信息列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量
 *         schema:
 *           type: integer
 *           default: 100
 *       - name: audit_status
 *         in: query
 *         description: 审核状态
 *         schema:
 *           type: integer
 *           default: 2
 *       - name: related_order_number
 *         in: query
 *         description: 关联订单号
 *         schema:
 *           type: string
 *       - name: platform_id
 *         in: query
 *         description: 平台ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
protectedRouter.get('/report-records', controller.getReportRecords.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/{id}:
 *   get:
 *     tags: [CRM订单分配]
 *     summary: 获取订单分配详情
 *     description: 根据分配ID获取订单分配的详细信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: 分配ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分配记录不存在
 *       500:
 *         description: 服务器错误
 */
/**
 * @swagger
 * /api/v1/crm/order-assignment/my-orders:
 *   get:
 *     tags: [CRM订单分配]
 *     summary: 获取分配给当前用户的订单列表
 *     description: 获取分配给当前登录用户的订单列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: assignment_status
 *         in: query
 *         description: 分配状态
 *         schema:
 *           type: integer
 *       - name: orderNumber
 *         in: query
 *         description: 订单号
 *         schema:
 *           type: string
 *       - name: startTime
 *         in: query
 *         description: 开始时间
 *         schema:
 *           type: string
 *       - name: endTime
 *         in: query
 *         description: 结束时间
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
protectedRouter.get('/my-orders', controller.getMyAssignedOrders.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/stats:
 *   get:
 *     tags: [CRM订单分配]
 *     summary: 获取分配状态统计
 *     description: 获取当前用户的订单分配状态统计信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 查询成功
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
protectedRouter.get('/stats', controller.getAssignmentStats.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/pending-recognition-orders:
 *   get:
 *     tags: [CRM订单分配]
 *     summary: 获取待认款订单列表
 *     description: 获取分配给当前用户且符合认款条件的订单列表，用于发起认款时选择订单
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: order_id
 *         in: query
 *         description: 订单ID
 *         schema:
 *           type: string
 *       - name: third_party_order_sn
 *         in: query
 *         description: 第三方订单号
 *         schema:
 *           type: string
 *       - name: recipient_name
 *         in: query
 *         description: 收货人姓名
 *         schema:
 *           type: string
 *       - name: startTime
 *         in: query
 *         description: 开始时间
 *         schema:
 *           type: string
 *       - name: endTime
 *         in: query
 *         description: 结束时间
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取待认款订单列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 分配记录ID
 *                           order_id:
 *                             type: string
 *                             description: 订单ID
 *                           third_party_order_sn:
 *                             type: string
 *                             description: 第三方订单号
 *                           total_amount:
 *                             type: number
 *                             description: 订单总金额
 *                           recipient_name:
 *                             type: string
 *                             description: 收货人姓名
 *                           recipient_phone:
 *                             type: string
 *                             description: 收货人电话
 *                           channel_name:
 *                             type: string
 *                             description: 渠道名称
 *                           order_created_at_formatted:
 *                             type: string
 *                             description: 订单创建时间
 *                           order_items:
 *                             type: array
 *                             description: 订单商品列表
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: 总记录数
 *                         page:
 *                           type: integer
 *                           description: 当前页码
 *                         pageSize:
 *                           type: integer
 *                           description: 每页数量
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
// 临时测试：将待认款订单接口移到公开路由（无需认证）
router.get('/pending-recognition-orders', controller.getPendingRecognitionOrders.bind(controller));

// 简单测试接口
router.get('/test', (req, res) => {
  res.json({
    code: 200,
    message: '测试接口正常',
    data: {
      timestamp: new Date().toISOString(),
      query: req.query
    }
  });
});

// ==================== 申请认款相关路由（必须在通用路由之前） ====================

protectedRouter.post('/payment-recognition-application', paymentRecognitionController.createApplication.bind(paymentRecognitionController));
protectedRouter.get('/payment-recognition-application', paymentRecognitionController.getApplicationList.bind(paymentRecognitionController));
protectedRouter.get('/payment-recognition-application/:id', paymentRecognitionController.getApplicationDetail.bind(paymentRecognitionController));
protectedRouter.post('/payment-recognition-application/:id/audit', paymentRecognitionController.auditApplication.bind(paymentRecognitionController));

// ==================== 订单分配相关路由 ====================

protectedRouter.get('/:id', controller.getAssignmentDetail.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/{id}/accept:
 *   post:
 *     tags: [CRM订单分配]
 *     summary: 接受订单分配
 *     description: 用户接受分配给自己的订单
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: 分配ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 接受成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分配记录不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/:id/accept', controller.acceptAssignment.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/{id}/reject:
 *   post:
 *     tags: [CRM订单分配]
 *     summary: 拒绝订单分配
 *     description: 用户拒绝分配给自己的订单
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: 分配ID
 *         schema:
 *           type: string
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reject_reason:
 *                 type: string
 *                 description: 拒绝原因
 *     responses:
 *       200:
 *         description: 拒绝成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分配记录不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/:id/reject', controller.rejectAssignment.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/{id}/complete:
 *   post:
 *     tags: [CRM订单分配]
 *     summary: 完成订单分配
 *     description: 用户标记订单分配为完成
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: 分配ID
 *         schema:
 *           type: string
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               completion_note:
 *                 type: string
 *                 description: 完成备注
 *     responses:
 *       200:
 *         description: 完成成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 分配记录不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/:id/complete', controller.completeAssignment.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/ship/{orderId}:
 *   post:
 *     tags: [CRM订单管理]
 *     summary: 订单发货
 *     description: 对已接受的订单进行发货操作
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: orderId
 *         in: path
 *         required: true
 *         description: 订单ID
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               shippingMethod:
 *                 type: integer
 *                 description: 配送方式 1-快递物流 2-自定义物流 3-商家自送 4-线下自取 5-无需物流
 *               shippingCompanyCode:
 *                 type: string
 *                 description: 物流公司编码
 *               shippingCompanyName:
 *                 type: string
 *                 description: 物流公司名称
 *               trackingNumber:
 *                 type: string
 *                 description: 物流单号
 *               imagesUrl:
 *                 type: string
 *                 description: 附件图片URL
 *             required:
 *               - shippingMethod
 *     responses:
 *       200:
 *         description: 发货成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/ship/:orderId', controller.shipOrder.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/logistics/track:
 *   post:
 *     tags: [CRM订单管理]
 *     summary: 查询物流轨迹
 *     description: 根据快递单号查询物流轨迹信息
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               trackingNumber:
 *                 type: string
 *                 description: 快递单号
 *               companyCode:
 *                 type: string
 *                 description: 快递公司编码（可选）
 *             required:
 *               - trackingNumber
 *     responses:
 *       200:
 *         description: 查询成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 暂无物流轨迹信息
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/logistics/track', controller.queryLogisticsTrack.bind(controller));

/**
 * @swagger
 * /api/v1/crm/order-assignment/payment-recognition/{orderId}:
 *   post:
 *     tags: [CRM订单管理]
 *     summary: 申请订单认款
 *     description: 对已发货的订单申请认款
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: orderId
 *         in: path
 *         required: true
 *         description: 订单ID
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               paymentAmount:
 *                 type: number
 *                 description: 认款金额
 *               remark:
 *                 type: string
 *                 description: 认款备注
 *               attachments:
 *                 type: array
 *                 description: 认款附件数组
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: 文件名
 *                     url:
 *                       type: string
 *                       description: 文件URL
 *                     size:
 *                       type: number
 *                       description: 文件大小
 *                     type:
 *                       type: string
 *                       description: 文件类型
 *             required:
 *               - paymentAmount
 *     responses:
 *       200:
 *         description: 认款申请成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/payment-recognition/:orderId', controller.applyPaymentRecognition.bind(controller));









// 将受保护的路由挂载到主路由
router.use('/', protectedRouter);

module.exports = router;
