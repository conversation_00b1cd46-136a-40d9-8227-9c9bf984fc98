const express = require('express');
const router = express.Router();
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

/**
 * @swagger
 * tags:
 *   - name: CRM
 *     description: CRM 模块相关接口
 */

// 公开访问的路由（无需认证）
// 客户管理路由（暂时去掉权限验证）
const customerRoutes = require('./CustomerRoute');
router.use('/customers', customerRoutes);

// 联系人管理路由（暂时去掉权限验证）
const contactRoutes = require('./ContactRoute');
router.use('/contacts', contactRoutes);

// CRM订单分配路由（包含测试接口，无需认证）
const orderAssignmentRoutes = require('./OrderAssignmentRoute');
router.use('/order-assignment', orderAssignmentRoutes);

// 需要认证的路由
router.use(authMiddleware);

// CRM订单管理路由（需要认证）
const crmOrderRoutes = require('./CrmOrderRoute');
router.use('/orders', crmOrderRoutes);

// CRM发票管理路由（需要认证）
const invoiceRoutes = require('./InvoiceRoute');
router.use('/invoice', invoiceRoutes);

module.exports = router;
