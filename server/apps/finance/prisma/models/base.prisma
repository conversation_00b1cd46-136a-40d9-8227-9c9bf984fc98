// Finance 财务管理相关模型

// 银行流水表
model BankJournal {
  id                        BigInt   @id // 雪花ID
  channelId                 BigInt   @map("channel_id") // 渠道ID，关联base.channel表
  type                      Int      @default(0) @map("type") // 类型：0-收入，1-支出
  tradeDate                 BigInt   @map("trade_date") // 支付日期（毫秒时间戳）
  transactionSerialNumber   String?  @map("transaction_serial_number") @db.VarChar(50) // 交易流水号
  paymentAmount             Decimal  @map("payment_amount") @db.Decimal(13,2) // 付款金额
  payerName                 String?  @map("payer_name") @db.VarChar(255) // 付款人姓名
  payerAccount              String?  @map("payer_account") @db.VarChar(255) // 付款人账号
  payerBank                 String?  @map("payer_bank") @db.VarChar(255) // 付款人开户行
  payeeOrderId              String?  @map("payee_order_id") @db.VarChar(255) // 收款单号
  payeeName                 String?  @map("payee_name") @db.VarChar(255) // 收款人名称
  payeeAccount              String?  @map("payee_account") @db.VarChar(255) // 收款人账号
  payeeBank                 String?  @map("payee_bank") @db.VarChar(255) // 收款人开户行
  remarks                   String?  @map("remarks") @db.VarChar(255) // 备注
  userId                    BigInt   @default(0) @map("user_id") // 添加者用户ID
  status                    Int      @default(0) @map("status") // 认款状态：0-待认款，1-已认款，2-认款申请中
  auditTime                 BigInt   @default(0) @map("audit_time") // 最终审核通过的时间
  attach                    String?  @map("attach") @db.VarChar(255) // 附件URL
  uploadRecordId            BigInt?  @map("upload_record_id") // 关联的导入记录ID

  // 系统字段 - 使用毫秒时间戳
  createdAt                 BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                 BigInt   @map("updated_at")
  deletedAt                 BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                 BigInt?  @map("created_by") // 创建人
  updatedBy                 BigInt?  @map("updated_by") // 更新人

  // 关联关系
  uploadRecord              BankJournalUploadRecord? @relation(fields: [uploadRecordId], references: [id])
  importDetails             BankJournalImportDetail[] @relation(name: "JournalImportDetails")

  @@map("bank_journals")
  @@schema("finance")
  @@index([payeeName], map: "idx_bank_journal_payee_name")
  @@index([payerName], map: "idx_bank_journal_payer_name")
  @@index([payeeOrderId], map: "idx_bank_journal_payee_order_id")
  @@index([tradeDate], map: "idx_bank_journal_trade_date")
  @@index([channelId], map: "idx_bank_journal_channel_id")
  @@index([status], map: "idx_bank_journal_status")
}

// 银行流水导入记录表
model BankJournalUploadRecord {
  id                        BigInt   @id // 雪花ID
  upFileName                String?  @map("up_file_name") @db.VarChar(255) // 导入文件名
  upFileUrl                 String?  @map("up_file_url") @db.VarChar(255) // 导入文件地址
  channelId                 BigInt   @map("channel_id") // 导入渠道ID，关联base.channel表
  message                   String?  @map("message") @db.Text // 处理消息结果
  downFileUrl               String?  @map("down_file_url") @db.VarChar(255) // 下载文件地址
  uploadTime                BigInt?  @map("upload_time") // 上传文件时间（毫秒时间戳）
  completionTime            BigInt?  @map("completion_time") // 处理完成时间（毫秒时间戳）
  status                    Int?     @map("status") // 状态：0-处理中，1-已完成，2-处理异常
  takeTime                  Int?     @map("take_time") // 耗时秒
  totalCount                Int      @default(0) @map("total_count") // 总记录数
  successCount              Int      @default(0) @map("success_count") // 成功导入数
  failCount                 Int      @default(0) @map("fail_count") // 失败数

  // 系统字段 - 使用毫秒时间戳
  createdAt                 BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                 BigInt   @map("updated_at")
  deletedAt                 BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                 BigInt?  @map("created_by") // 创建人
  updatedBy                 BigInt?  @map("updated_by") // 更新人

  // 关联关系
  journals                  BankJournal[]
  importDetails             BankJournalImportDetail[]

  @@map("bank_journal_upload_records")
  @@schema("finance")
  @@index([channelId], map: "idx_upload_record_channel_id")
  @@index([status], map: "idx_upload_record_status")
  @@index([createdAt], map: "idx_upload_record_created_at")
}

// 银行流水导入详细记录表
model BankJournalImportDetail {
  id                              BigInt   @id @default(autoincrement()) // 自增ID
  uploadRecordId                  BigInt   @map("upload_record_id") // 关联导入记录ID
  rowNumber                       Int      @map("row_number") // Excel行号

  // 流水数据字段（保持与bank_journals表一致的结构）
  tradeDate                       BigInt?  @map("trade_date") // 交易日期（毫秒时间戳）
  transactionSerialNumber         String?  @map("transaction_serial_number") @db.VarChar(50) // 交易流水号
  paymentAmount                   Decimal? @map("payment_amount") @db.Decimal(13,2) // 付款金额
  payerName                       String?  @map("payer_name") @db.VarChar(255) // 付款人姓名
  payerAccount                    String?  @map("payer_account") @db.VarChar(255) // 付款人账号
  payerBank                       String?  @map("payer_bank") @db.VarChar(255) // 付款人开户行
  payeeOrderId                    String?  @map("payee_order_id") @db.VarChar(100) // 收款订单号
  payeeName                       String?  @map("payee_name") @db.VarChar(255) // 收款人名称
  payeeAccount                    String?  @map("payee_account") @db.VarChar(255) // 收款人账号
  payeeBank                       String?  @map("payee_bank") @db.VarChar(255) // 收款人开户行
  remarks                         String?  @map("remarks") @db.Text // 备注

  // 导入状态和结果
  importStatus                    String   @map("import_status") @db.VarChar(20) // 导入状态：success, failed
  errorReason                     String?  @map("error_reason") @db.Text // 失败原因详细描述
  journalId                       BigInt?  @map("journal_id") // 成功导入后的流水记录ID

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  uploadRecord                    BankJournalUploadRecord @relation(fields: [uploadRecordId], references: [id])
  journal                         BankJournal? @relation(fields: [journalId], references: [id], name: "JournalImportDetails")

  @@map("bank_journal_import_details")
  @@schema("finance")
  @@index([uploadRecordId], map: "idx_bank_journal_import_details_upload_record_id")
  @@index([importStatus], map: "idx_bank_journal_import_details_import_status")
  @@index([uploadRecordId, rowNumber], map: "idx_bank_journal_import_details_row_number")
  @@index([journalId], map: "idx_bank_journal_import_details_journal_id")
  @@index([createdAt], map: "idx_bank_journal_import_details_created_at")
}

// 付款凭证表
model PaymentVoucher {
  id                              BigInt   @id // 雪花ID
  voucherNumber                   String   @unique @map("voucher_number") @db.VarChar(50) // 凭证编号，自动生成
  voucherType                     Int      @map("voucher_type") // 凭证类型：0-银行流水，1-现金收款，2-其他
  amount                          Decimal  @map("amount") @db.Decimal(13,2) // 金额
  payerName                       String   @map("payer_name") @db.VarChar(255) // 付款方名称
  payerBankAccount                String?  @map("payer_bank_account") @db.VarChar(100) // 付款方银行账号
  payerBankName                   String?  @map("payer_bank_name") @db.VarChar(200) // 付款方银行名称
  paymentDate                     BigInt   @map("payment_date") // 付款日期（毫秒时间戳）
  remarks                         String?  @map("remarks") @db.Text // 备注
  status                          Int      @default(0) @map("status") // 关联状态：0-未关联，1-已关联
  mergedStatus                    Int      @default(0) @map("merged_status") // 合并状态：0-未合并，1-已合并，2-合并源凭证
  mergedVoucherId                 BigInt?  @map("merged_voucher_id") // 合并后的凭证ID（如果是被合并的凭证）
  mergedReason                    String?  @map("merged_reason") @db.Text // 合并原因说明
  originalVoucherIds              String?  @map("original_voucher_ids") @db.Text // 原始凭证ID列表（JSON格式，用于合并后的凭证）

  // 系统字段 - 使用毫秒时间戳
  createdAt                       BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000")) @map("created_at")
  updatedAt                       BigInt   @map("updated_at")
  deletedAt                       BigInt?  @map("deleted_at") // 删除时间（毫秒时间戳）
  createdBy                       BigInt?  @map("created_by") // 创建人
  updatedBy                       BigInt?  @map("updated_by") // 更新人

  // 关联关系
  attachments                     PaymentVoucherAttachment[]

  @@map("payment_vouchers")
  @@schema("finance")
  @@index([voucherType], map: "idx_payment_voucher_type")
  @@index([status], map: "idx_payment_voucher_status")
  @@index([paymentDate], map: "idx_payment_voucher_payment_date")
  @@index([payerName], map: "idx_payment_voucher_payer_name")
  @@index([createdAt], map: "idx_payment_voucher_created_at")
}

// 付款凭证附件表
model PaymentVoucherAttachment {
  id                              BigInt   @id // 雪花ID
  voucherId                       BigInt   @map("voucher_id") // 关联付款凭证ID

  // 关联关系
  voucher                         PaymentVoucher @relation(fields: [voucherId], references: [id])

  @@map("payment_voucher_attachments")
  @@schema("finance")
  @@index([voucherId], map: "idx_payment_voucher_attachment_voucher_id")
}
