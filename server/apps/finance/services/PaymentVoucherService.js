/**
 * 付款凭证服务类
 */
const { paymentVoucher: constants } = require('../constants');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class PaymentVoucherService {
  constructor(prisma) {
    this.prisma = prisma;
    // 注意：Prisma模型名称应该是camelCase
    this.paymentVoucherModel = prisma.paymentVoucher;
    this.paymentVoucherAttachmentModel = prisma.paymentVoucherAttachment;
  }

  /**
   * 生成凭证编号
   */
  async generateVoucherNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    // 查询当天已有的凭证数量
    const startOfDay = new Date(year, now.getMonth(), now.getDate()).getTime();
    const endOfDay = startOfDay + 24 * 60 * 60 * 1000 - 1;
    
    const count = await this.paymentVoucherModel.count({
      where: {
        createdAt: {
          gte: BigInt(startOfDay),
          lte: BigInt(endOfDay)
        },
        deletedAt: null
      }
    });
    
    const sequence = String(count + 1).padStart(3, '0');
    return `${constants.VOUCHER_NUMBER_PREFIX}${year}${month}${day}-${sequence}`;
  }

  /**
   * 创建付款凭证
   */
  async createPaymentVoucher(createData, operatorId) {
    const { voucherType, amount, payerName, paymentDate, remarks, attachments, status } = createData;
    
    const now = BigInt(Date.now());
    const voucherId = generateSnowflakeId();
    const voucherNumber = await this.generateVoucherNumber();
    
    // 处理付款日期
    let paymentDateTimestamp;
    if (typeof paymentDate === 'string') {
      paymentDateTimestamp = BigInt(new Date(paymentDate).getTime());
    } else {
      paymentDateTimestamp = BigInt(paymentDate);
    }

    return await this.prisma.$transaction(async (tx) => {
      // 创建付款凭证
      const voucher = await tx.paymentVoucher.create({
        data: {
          id: voucherId,
          voucherNumber: voucherNumber,
          voucherType: parseInt(voucherType),
          amount: parseFloat(amount),
          payerName: payerName.trim(),
          paymentDate: paymentDateTimestamp,
          remarks: remarks ? remarks.trim() : null,
          status: status !== undefined ? parseInt(status) : constants.STATUS.UNRELATED,
          createdAt: now,
          updatedAt: now,
          createdBy: operatorId ? BigInt(operatorId) : null,
          updatedBy: operatorId ? BigInt(operatorId) : null
        }
      });

      // 创建附件记录
      if (attachments && attachments.length > 0) {
        const attachmentData = attachments.map(() => ({
          id: generateSnowflakeId(),
          voucherId: voucherId
        }));

        await tx.paymentVoucherAttachment.createMany({
          data: attachmentData
        });
      }

      // 查询完整的凭证信息（包含附件）
      const result = await tx.paymentVoucher.findUnique({
        where: { id: voucherId },
        include: {
          attachments: true
        }
      });

      return this.formatVoucherData(result);
    });
  }

  /**
   * 获取付款凭证列表
   */
  async getPaymentVoucherList(queryData) {
    const {
      page,
      pageSize,
      voucherType,
      status,
      payerName,
      startDate,
      endDate,
      voucherNumber
    } = queryData;

    // 构建查询条件
    const where = {
      deletedAt: null,
      // 过滤掉已合并的源凭证，只显示未合并的凭证和合并后的新凭证
      mergedStatus: {
        in: [constants.MERGED_STATUS.NOT_MERGED, constants.MERGED_STATUS.MERGED]
      }
    };

    if (voucherType !== undefined && voucherType !== null && voucherType !== '') {
      where.voucherType = parseInt(voucherType);
    }

    if (status !== undefined && status !== null && status !== '') {
      where.status = parseInt(status);
    }



    if (payerName) {
      where.payerName = {
        contains: payerName.trim()
      };
    }

    if (voucherNumber) {
      where.voucherNumber = {
        contains: voucherNumber.trim()
      };
    }

    // 处理日期范围
    if (startDate || endDate) {
      where.paymentDate = {};
      if (startDate) {
        // 如果已经是毫秒时间戳，直接使用；否则转换为时间戳
        const startTimestamp = typeof startDate === 'number' ? startDate :
                              (typeof startDate === 'string' && /^\d+$/.test(startDate)) ? parseInt(startDate) :
                              new Date(startDate).getTime();
        where.paymentDate.gte = BigInt(startTimestamp);
      }
      if (endDate) {
        // 如果已经是毫秒时间戳，直接使用；否则转换为时间戳
        const endTimestamp = typeof endDate === 'number' ? endDate :
                            (typeof endDate === 'string' && /^\d+$/.test(endDate)) ? parseInt(endDate) :
                            new Date(endDate).getTime();
        where.paymentDate.lte = BigInt(endTimestamp);
      }
    }

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 查询总数
    const total = await this.paymentVoucherModel.count({ where });

    // 查询数据
    const vouchers = await this.paymentVoucherModel.findMany({
      where,
      include: {
        attachments: true
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: pageSize
    });

    // 格式化数据
    const items = vouchers.map(voucher => this.formatVoucherData(voucher));

    return {
      items,
      pageInfo: {
        total,
        currentPage: page,
        totalPage: Math.ceil(total / pageSize),
        pageSize
      }
    };
  }

  /**
   * 获取付款凭证详情
   */
  async getPaymentVoucherById(id) {
    const voucher = await this.paymentVoucherModel.findFirst({
      where: {
        id: BigInt(id),
        deletedAt: null
      },
      include: {
        attachments: true
      }
    });

    if (!voucher) {
      throw new Error('付款凭证不存在');
    }

    return this.formatVoucherData(voucher);
  }



  /**
   * 格式化凭证数据
   */
  formatVoucherData(voucher) {
    if (!voucher) return null;

    return {
      id: voucher.id.toString(),
      voucherNumber: voucher.voucherNumber,
      voucherType: voucher.voucherType,
      voucherTypeLabel: constants.VOUCHER_TYPE_NAMES[voucher.voucherType],
      amount: parseFloat(voucher.amount),
      payerName: voucher.payerName,
      paymentDate: parseInt(voucher.paymentDate.toString()),
      remarks: voucher.remarks,
      status: voucher.status,
      statusLabel: constants.STATUS_NAMES[voucher.status],
      mergedStatus: voucher.mergedStatus || 0,
      mergedStatusLabel: constants.MERGED_STATUS_NAMES[voucher.mergedStatus || 0],
      mergedVoucherId: voucher.mergedVoucherId ? voucher.mergedVoucherId.toString() : null,
      mergedReason: voucher.mergedReason,
      originalVoucherIds: voucher.originalVoucherIds,
      attachments: voucher.attachments ? voucher.attachments.map(attachment => ({
        id: attachment.id.toString(),
        fileName: attachment.fileName,
        originalName: attachment.originalName,
        fileUrl: attachment.fileUrl,
        fileSize: attachment.fileSize,
        fileType: attachment.fileType,
        extension: attachment.extension
      })) : [],
      createdAt: parseInt(voucher.createdAt.toString()),
      updatedAt: parseInt(voucher.updatedAt.toString()),
      createdBy: voucher.createdBy ? voucher.createdBy.toString() : null,
      updatedBy: voucher.updatedBy ? voucher.updatedBy.toString() : null
    };
  }

  /**
   * 合并付款凭证
   */
  async mergePaymentVouchers(voucherIds, mergeReason, operatorId) {
    console.log('[合并付款凭证] 开始合并凭证:', { voucherIds, mergeReason, operatorId });

    return await this.prisma.$transaction(async (tx) => {
      // 1. 查询要合并的凭证
      const vouchersToMerge = await tx.paymentVoucher.findMany({
        where: {
          id: { in: voucherIds.map(id => BigInt(id)) },
          deletedAt: null,
          mergedStatus: constants.MERGED_STATUS.NOT_MERGED // 只能合并未合并的凭证
        },
        include: {
          attachments: true
        }
      });

      if (vouchersToMerge.length !== voucherIds.length) {
        throw new Error('部分凭证不存在或已被合并，无法进行合并操作');
      }

      // 2. 验证凭证状态
      const relatedVouchers = vouchersToMerge.filter(v => v.status === constants.STATUS.RELATED);
      if (relatedVouchers.length > 0) {
        throw new Error('存在已关联的凭证，无法进行合并操作');
      }

      // 3. 计算合并后的数据
      const totalAmount = vouchersToMerge.reduce((sum, voucher) => sum + parseFloat(voucher.amount), 0);
      const payerNames = [...new Set(vouchersToMerge.map(v => v.payerName))];
      const voucherTypes = [...new Set(vouchersToMerge.map(v => v.voucherType))];
      const paymentDates = [...new Set(vouchersToMerge.map(v => v.paymentDate))];

      // 4. 生成新的合并凭证
      const now = BigInt(Date.now());
      const mergedVoucherId = generateSnowflakeId();
      const mergedVoucherNumber = await this.generateVoucherNumber();

      // 合并后的付款方名称（如果不一致则用逗号分隔）
      const mergedPayerName = payerNames.length === 1 ? payerNames[0] : payerNames.join(', ');

      // 合并后的凭证类型（如果不一致则使用"其他"）
      const mergedVoucherType = voucherTypes.length === 1 ? voucherTypes[0] : constants.VOUCHER_TYPE.OTHER;

      // 合并后的付款日期（使用最早的日期）
      const mergedPaymentDate = Math.min(...paymentDates.map(d => Number(d)));

      // 合并后的备注
      const originalRemarks = vouchersToMerge.map(v => v.remarks).filter(r => r).join('; ');
      const mergedRemarks = `合并凭证: ${mergeReason}${originalRemarks ? '; 原备注: ' + originalRemarks : ''}`;

      // 5. 创建合并后的新凭证
      const mergedVoucher = await tx.paymentVoucher.create({
        data: {
          id: mergedVoucherId,
          voucherNumber: mergedVoucherNumber,
          voucherType: mergedVoucherType,
          amount: totalAmount,
          payerName: mergedPayerName,
          paymentDate: BigInt(mergedPaymentDate),
          remarks: mergedRemarks,
          status: constants.STATUS.UNRELATED,
          mergedStatus: constants.MERGED_STATUS.MERGED,
          mergedReason: mergeReason,
          originalVoucherIds: JSON.stringify(voucherIds),
          createdAt: now,
          updatedAt: now,
          createdBy: operatorId ? BigInt(operatorId) : null,
          updatedBy: operatorId ? BigInt(operatorId) : null
        }
      });

      // 6. 更新原凭证状态为已合并
      await tx.paymentVoucher.updateMany({
        where: {
          id: { in: voucherIds.map(id => BigInt(id)) }
        },
        data: {
          mergedStatus: constants.MERGED_STATUS.SOURCE_MERGED,
          mergedVoucherId: mergedVoucherId,
          updatedAt: now,
          updatedBy: operatorId ? BigInt(operatorId) : null
        }
      });

      // 7. 复制所有附件到新凭证
      const allAttachments = vouchersToMerge.flatMap(v => v.attachments);
      if (allAttachments.length > 0) {
        // 根据 Prisma 模式，PaymentVoucherAttachment 表只有 id 和 voucherId 字段
        const newAttachments = allAttachments.map(() => ({
          id: generateSnowflakeId(),
          voucherId: mergedVoucherId
        }));

        await tx.paymentVoucherAttachment.createMany({
          data: newAttachments
        });
      }

      // 8. 查询并返回合并后的完整凭证信息
      const result = await tx.paymentVoucher.findUnique({
        where: { id: mergedVoucherId },
        include: {
          attachments: true
        }
      });

      console.log('[合并付款凭证] 合并完成:', { mergedVoucherId: mergedVoucherId.toString() });
      return this.formatVoucherData(result);
    });
  }

  /**
   * 获取可用于认款的付款凭证列表
   */
  async getAvailableVouchersForRecognition(filters = {}) {
    const { payerName, minAmount, maxAmount } = filters;

    const where = {
      deletedAt: null,
      status: constants.STATUS.UNRELATED, // 只返回未关联的凭证
      mergedStatus: {
        in: [constants.MERGED_STATUS.NOT_MERGED, constants.MERGED_STATUS.MERGED] // 未合并的凭证或合并后的凭证
      }
    };

    // 添加筛选条件
    if (payerName) {
      where.payerName = {
        contains: payerName,
        mode: 'insensitive'
      };
    }

    if (minAmount !== undefined || maxAmount !== undefined) {
      where.amount = {};
      if (minAmount !== undefined) {
        where.amount.gte = minAmount;
      }
      if (maxAmount !== undefined) {
        where.amount.lte = maxAmount;
      }
    }

    const vouchers = await this.paymentVoucherModel.findMany({
      where,
      include: {
        attachments: true
      },
      orderBy: [
        { paymentDate: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    return vouchers.map(voucher => this.formatVoucherData(voucher));
  }

  /**
   * 创建测试数据
   */
  async createTestData(operatorId) {
    console.log('[创建测试数据] 开始创建测试凭证');

    const testVouchers = [
      {
        voucherType: constants.VOUCHER_TYPE.BANK_TRANSFER,
        amount: 10000,
        payerName: '测试公司A',
        paymentDate: BigInt(Date.now() - ********), // 昨天
        remarks: '测试凭证1 - 银行转账'
      },
      {
        voucherType: constants.VOUCHER_TYPE.CASH,
        amount: 15000,
        payerName: '测试公司A',
        paymentDate: BigInt(Date.now() - ********), // 昨天
        remarks: '测试凭证2 - 现金收款'
      },
      {
        voucherType: constants.VOUCHER_TYPE.OTHER,
        amount: 8000,
        payerName: '测试公司B',
        paymentDate: BigInt(Date.now() - *********), // 前天
        remarks: '测试凭证3 - 其他方式'
      },
      {
        voucherType: constants.VOUCHER_TYPE.BANK_TRANSFER,
        amount: 12000,
        payerName: '测试公司C',
        paymentDate: BigInt(Date.now()),
        remarks: '测试凭证4 - 银行转账'
      },
      {
        voucherType: constants.VOUCHER_TYPE.CASH,
        amount: 20000,
        payerName: '测试公司C',
        paymentDate: BigInt(Date.now()),
        remarks: '测试凭证5 - 现金收款'
      }
    ];

    const createdVouchers = [];
    const now = BigInt(Date.now());

    for (const voucherData of testVouchers) {
      try {
        const voucherId = generateSnowflakeId();
        const voucherNumber = await this.generateVoucherNumber();

        const voucher = await this.paymentVoucherModel.create({
          data: {
            id: voucherId,
            voucherNumber,
            voucherType: voucherData.voucherType,
            amount: voucherData.amount,
            payerName: voucherData.payerName,
            paymentDate: voucherData.paymentDate,
            remarks: voucherData.remarks,
            status: constants.STATUS.UNRELATED,
            mergedStatus: constants.MERGED_STATUS.NOT_MERGED,
            createdAt: now,
            updatedAt: now,
            createdBy: operatorId ? BigInt(operatorId) : null,
            updatedBy: operatorId ? BigInt(operatorId) : null
          }
        });

        createdVouchers.push(this.formatVoucherData(voucher));
        console.log(`[创建测试数据] 创建凭证成功: ${voucherNumber}`);
      } catch (error) {
        console.error('[创建测试数据] 创建凭证失败:', error);
      }
    }

    console.log(`[创建测试数据] 完成，共创建 ${createdVouchers.length} 个测试凭证`);
    return {
      count: createdVouchers.length,
      vouchers: createdVouchers
    };
  }
}

module.exports = PaymentVoucherService;
