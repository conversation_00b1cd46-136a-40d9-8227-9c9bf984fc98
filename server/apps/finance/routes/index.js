const express = require("express");
const router = express.Router();
const multer = require("multer");
const authMiddleware = require("../../../core/middleware/AuthMiddleware");
const { prisma } = require("../../../core/database/prisma");
const BankJournalController = require("../controllers/BankJournalController");
const InvoiceAuditController = require("../controllers/InvoiceAuditController");
const RecognitionAuditController = require("../controllers/RecognitionAuditController");
const PaymentVoucherController = require("../controllers/PaymentVoucherController");

// 配置multer中间件
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 限制10MB
  },
});

// 创建控制器实例
const bankJournalController = new BankJournalController(prisma);
const invoiceAuditController = new InvoiceAuditController(prisma);
const recognitionAuditController = new RecognitionAuditController(prisma);
const paymentVoucherController = new PaymentVoucherController(prisma);

/**
 * @swagger
 * tags:
 *   - name: Finance
 *     description: Finance 财务模块相关接口
 *   - name: BankJournal
 *     description: 银行流水相关接口
 *   - name: InvoiceAudit
 *     description: 发票审核相关接口
 *   - name: RecognitionAudit
 *     description: 认款审核相关接口
 *   - name: PaymentVoucher
 *     description: 付款凭证相关接口
 */

//所有路由都需要认证 - 临时注释用于测试
// router.use(authMiddleware); // 临时注释掉用于测试

/**
 * @swagger
 * /api/finance/bank-journal/upload:
 *   post:
 *     tags: [BankJournal]
 *     summary: 上传银行流水文件
 *     description: 上传Excel格式的银行流水文件进行解析和导入
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Excel文件(.xlsx, .xls)
 *               channel:
 *                 type: integer
 *                 description: 渠道类型(0-银行，1-支付宝，2-微信，3-其他)
 *                 enum: [0, 1, 2, 3]
 *             required:
 *               - file
 *               - channel
 *     responses:
 *       200:
 *         description: 文件上传成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 文件上传成功，正在处理中...
 *                 data:
 *                   type: object
 *                   properties:
 *                     uploadRecordId:
 *                       type: string
 *                       description: 导入记录ID
 *                     fileName:
 *                       type: string
 *                       description: 文件名
 *                     status:
 *                       type: integer
 *                       description: 处理状态
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.post(
  "/bank-journal/upload",
  upload.single("file"),
  bankJournalController.uploadFile.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal/import-by-url:
 *   post:
 *     tags: [BankJournal]
 *     summary: 通过URL导入银行流水文件
 *     description: 通过文件URL导入Excel格式的银行流水文件进行解析和导入，支持单个或多个文件
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               channelId:
 *                 type: string
 *                 description: 渠道ID（雪花ID）
 *                 example: "*********0*********"
 *               files:
 *                 type: array
 *                 description: 文件列表
 *                 items:
 *                   type: object
 *                   properties:
 *                     fileUrl:
 *                       type: string
 *                       description: 文件URL地址
 *                     fileName:
 *                       type: string
 *                       description: 文件名（可选）
 *                   required:
 *                     - fileUrl
 *             required:
 *               - channelId
 *               - files
 *     responses:
 *       200:
 *         description: 导入任务创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 导入任务已创建
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalFiles:
 *                       type: integer
 *                       description: 总文件数
 *                     uploadRecordIds:
 *                       type: array
 *                       description: 导入记录ID列表
 *                       items:
 *                         type: string
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.post('/bank-journal/import-by-url', bankJournalController.importByUrl.bind(bankJournalController));

/**
 * @swagger
 * /api/finance/bank-journal/upload-record/{id}:
 *   get:
 *     tags: [BankJournal]
 *     summary: 获取导入记录详情
 *     description: 根据ID获取银行流水导入记录的详细信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 导入记录ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取导入记录成功
 *                 data:
 *                   type: object
 *                   description: 导入记录详情
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 */
router.get(
  "/bank-journal/upload-record/:id",
  bankJournalController.getUploadRecordDetail.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal/upload-records:
 *   get:
 *     tags: [BankJournal]
 *     summary: 获取导入记录列表
 *     description: 分页查询银行流水导入记录列表，支持多种筛选条件
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: channel
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2, 3]
 *         description: 渠道筛选(0-银行，1-支付宝，2-微信，3-其他)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2]
 *         description: 状态筛选(0-处理中，1-已完成，2-处理异常)
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取导入记录列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 导入记录列表
 *                     pageInfo:
 *                       type: object
 *                       description: 分页信息
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.get(
  "/bank-journal/upload-records",
  bankJournalController.getUploadRecordList.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal/upload-record/{uploadRecordId}/details:
 *   get:
 *     tags: [BankJournal]
 *     summary: 获取导入详细记录列表
 *     description: 分页查询指定导入记录的详细记录列表，包括每行数据的导入状态和失败原因
 *     parameters:
 *       - in: path
 *         name: uploadRecordId
 *         required: true
 *         schema:
 *           type: string
 *         description: 导入记录ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: importStatus
 *         schema:
 *           type: string
 *           enum: [success, failed]
 *         description: 导入状态筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取导入详细记录成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 详细记录列表
 *                     pageInfo:
 *                       type: object
 *                       description: 分页信息
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 导入记录不存在
 */
router.get(
  "/bank-journal/upload-record/:uploadRecordId/details",
  bankJournalController.getImportDetailList.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal/upload-record/{uploadRecordId}/result-data:
 *   get:
 *     tags: [BankJournal]
 *     summary: 获取导入结果数据（二维数组格式）
 *     description: 获取指定导入记录的所有详细数据，返回二维数组格式，适用于表格展示或Excel导出
 *     parameters:
 *       - in: path
 *         name: uploadRecordId
 *         required: true
 *         schema:
 *           type: string
 *         description: 导入记录ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取导入结果数据成功
 *                 data:
 *                   type: array
 *                   description: 二维数组格式的数据，第一行为表头，后续行为数据
 *                   items:
 *                     type: array
 *                     items:
 *                       oneOf:
 *                         - type: string
 *                         - type: number
 *                   example: [
 *                     ["行号", "导入状态", "失败原因", "交易日期", "付款人姓名"],
 *                     [1, "成功", "", "2024-01-01 10:00:00", "张三"],
 *                     [2, "失败", "金额格式错误", "", "李四"]
 *                   ]
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 导入记录不存在
 */
router.get(
  "/bank-journal/upload-record/:uploadRecordId/result-data",
  bankJournalController.getImportResultData.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal:
 *   post:
 *     tags: [BankJournal]
 *     summary: 录入收款流水信息
 *     description: 手动录入收款流水记录信息，支持收款凭证上传
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tradeDate:
 *                 type: integer
 *                 description: 交易日期（毫秒时间戳）
 *                 example: *************
 *               channelId:
 *                 type: string
 *                 description: 渠道ID（雪花ID）
 *                 example: "185653478876647424"
 *               payerName:
 *                 type: string
 *                 description: 付款人姓名
 *                 example: "张三"
 *               payerAccount:
 *                 type: string
 *                 description: 付款人账号
 *                 example: "622202*********0123"
 *               payerBank:
 *                 type: string
 *                 description: 付款人开户行
 *                 example: "中国工商银行北京分行"
 *               payeeName:
 *                 type: string
 *                 description: 收款人名称
 *                 example: "李四"
 *               payeeAccount:
 *                 type: string
 *                 description: 收款人账号
 *                 example: "622202*********0456"
 *               payeeBank:
 *                 type: string
 *                 description: 收款人开户行
 *                 example: "中国建设银行上海分行"
 *               paymentAmount:
 *                 type: number
 *                 description: 付款金额
 *                 example: 1000.50
 *               transactionSerialNumber:
 *                 type: string
 *                 description: 交易流水号
 *                 example: "TXN20231227001"
 *               remark:
 *                 type: string
 *                 description: 备注
 *                 example: "货款支付"
 *               receiptVoucher:
 *                 type: array
 *                 description: 收款凭证URL数组
 *                 items:
 *                   type: string
 *                 example: ["https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/9a2f574cee96f26e74cec6b8466a81b2.png"]
 *             required:
 *               - tradeDate
 *               - channelId
 *               - payerName
 *               - payeeName
 *               - paymentAmount
 *     responses:
 *       200:
 *         description: 流水记录创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "收款流水信息录入成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 流水记录ID
 *                     channelId:
 *                       type: string
 *                       description: 渠道ID
 *                     type:
 *                       type: integer
 *                       description: 类型
 *                     tradeDate:
 *                       type: string
 *                       description: 交易日期
 *                     paymentAmount:
 *                       type: number
 *                       description: 付款金额
 *                     status:
 *                       type: integer
 *                       description: 认款状态
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "交易日期不能为空"
 */
router.post('/bank-journal', bankJournalController.createJournal.bind(bankJournalController));

/**
 * @swagger
 * /api/finance/bank-journal/list:
 *   get:
 *     tags: [BankJournal]
 *     summary: 获取银行流水列表
 *     description: 分页查询银行流水列表，支持多种筛选条件
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         description: 流水ID精确搜索
 *       - in: query
 *         name: channelId
 *         schema:
 *           type: string
 *         description: 渠道ID筛选
 *       - in: query
 *         name: channel
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2, 3]
 *         description: 渠道筛选(兼容旧字段，0-银行，1-支付宝，2-微信，3-其他)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2]
 *         description: 状态筛选(0-待认款，1-已认款，2-认款申请中)
 *       - in: query
 *         name: type
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 类型筛选(0-收入，1-支出)
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *         description: 开始日期(毫秒时间戳)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *         description: 结束日期(毫秒时间戳)
 *       - in: query
 *         name: paymentAmount
 *         schema:
 *           type: number
 *         description: 精确金额搜索
 *       - in: query
 *         name: minAmount
 *         schema:
 *           type: number
 *         description: 最小金额筛选
 *       - in: query
 *         name: maxAmount
 *         schema:
 *           type: number
 *         description: 最大金额筛选
 *       - in: query
 *         name: transactionSerialNumber
 *         schema:
 *           type: string
 *         description: 交易流水号搜索
 *       - in: query
 *         name: payerName
 *         schema:
 *           type: string
 *         description: 付款人姓名筛选
 *       - in: query
 *         name: payerAccount
 *         schema:
 *           type: string
 *         description: 付款人账号筛选
 *       - in: query
 *         name: payerBank
 *         schema:
 *           type: string
 *         description: 付款人开户行筛选
 *       - in: query
 *         name: payeeName
 *         schema:
 *           type: string
 *         description: 收款人姓名筛选
 *       - in: query
 *         name: payeeAccount
 *         schema:
 *           type: string
 *         description: 收款人账号筛选
 *       - in: query
 *         name: payeeBank
 *         schema:
 *           type: string
 *         description: 收款人开户行筛选
 *       - in: query
 *         name: payeeOrderId
 *         schema:
 *           type: string
 *         description: 收款单号筛选
 *       - in: query
 *         name: remarks
 *         schema:
 *           type: string
 *         description: 备注搜索
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取银行流水列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 流水列表
 *                     pageInfo:
 *                       type: object
 *                       description: 分页信息
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.get(
  "/bank-journal/list",
  bankJournalController.getJournalList.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal/badge-count:
 *   get:
 *     tags: [BankJournal]
 *     summary: 获取客户流水角标统计
 *     description: 获取客户流水各状态的数量统计，用于显示角标。支持缓存机制，默认缓存30秒
 *     parameters:
 *       - in: query
 *         name: refresh
 *         required: false
 *         schema:
 *           type: string
 *           enum: ['true', '1']
 *         description: 是否强制刷新缓存
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取客户流水角标统计成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       description: 全部流水记录数量
 *                       example: 150
 *                     pending:
 *                       type: integer
 *                       description: 待认款记录数量
 *                       example: 25
 *                     applying:
 *                       type: integer
 *                       description: 认款申请中记录数量
 *                       example: 10
 *                     confirmed:
 *                       type: integer
 *                       description: 已认款记录数量
 *                       example: 115
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/bank-journal/badge-count', bankJournalController.getJournalBadgeCount.bind(bankJournalController));

/**
 * @swagger
 * /api/finance/bank-journal/{id}:
 *   get:
 *     tags: [BankJournal]
 *     summary: 获取银行流水详情
 *     description: 根据ID获取银行流水的详细信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 流水ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取银行流水详情成功
 *                 data:
 *                   type: object
 *                   description: 流水详情
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 */
router.get(
  "/bank-journal/:id",
  bankJournalController.getJournalDetail.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal/{id}/status:
 *   put:
 *     tags: [BankJournal]
 *     summary: 更新银行流水状态
 *     description: 更新银行流水的认款状态
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 流水ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 enum: [0, 1, 2]
 *                 description: 状态(0-待认款，1-已认款，2-认款申请中)
 *             required:
 *               - status
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新银行流水状态成功
 *                 data:
 *                   type: object
 *                   description: 更新后的流水信息
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 */
router.put(
  "/bank-journal/:id/status",
  bankJournalController.updateJournalStatus.bind(bankJournalController)
);

/**
 * @swagger
 * /api/finance/bank-journal/download-result/{id}:
 *   get:
 *     tags: [BankJournal]
 *     summary: 下载导入结果文件
 *     description: 根据导入记录ID下载对应的导入结果Excel文件
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 导入记录ID
 *     responses:
 *       200:
 *         description: 文件下载成功
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: 文件不存在
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.get('/bank-journal/download-result/:id', bankJournalController.downloadImportResult.bind(bankJournalController));

/**
 * @swagger
 * /api/finance/bank-journal/{id}:
 *   delete:
 *     tags: [BankJournal]
 *     summary: 删除银行流水记录
 *     description: 根据ID删除指定的银行流水记录（软删除）
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 流水记录ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "流水记录删除成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 删除的记录ID
 *                     deletedAt:
 *                       type: number
 *                       description: 删除时间戳
 *                     deletedBy:
 *                       type: string
 *                       description: 删除人ID（使用updatedBy字段）
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 */
router.delete('/bank-journal/:id', bankJournalController.deleteJournal.bind(bankJournalController));

/**
 * @swagger
 * /api/finance/bank-journal/batch-delete:
 *   post:
 *     tags: [BankJournal]
 *     summary: 批量删除银行流水记录
 *     description: 根据ID列表批量删除银行流水记录（软删除）
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 要删除的流水记录ID列表
 *                 example: ["*********", "*********"]
 *     responses:
 *       200:
 *         description: 批量删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "批量删除完成，成功删除 2 条记录"
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalRequested:
 *                       type: integer
 *                       description: 请求删除的总数
 *                     totalDeleted:
 *                       type: integer
 *                       description: 实际删除的总数
 *                     notFoundIds:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: 未找到的记录ID列表
 *                     deletedAt:
 *                       type: number
 *                       description: 删除时间戳
 *                     deletedBy:
 *                       type: string
 *                       description: 删除人ID（使用updatedBy字段）
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.post('/bank-journal/batch-delete', bankJournalController.batchDeleteJournals.bind(bankJournalController));

// ==================== 发票审核相关路由 ====================

/**
 * @swagger
 * /api/finance/invoice-audit/list:
 *   get:
 *     tags: [InvoiceAudit]
 *     summary: 获取发票审核列表
 *     description: 分页查询发票审核列表，支持多种筛选条件
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2, 3]
 *         description: 状态筛选(0-待审核，1-财务审核通过，2-财务审核驳回，3-已开票)
 *       - in: query
 *         name: sourceType
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 数据来源类型(0-系统订单，1-第三方订单)
 *       - in: query
 *         name: applicationNo
 *         schema:
 *           type: string
 *         description: 申请单号搜索
 *       - in: query
 *         name: orderId
 *         schema:
 *           type: string
 *         description: 订单ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *         description: 创建开始时间(毫秒时间戳)
 *         example: "170*********0"
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *         description: 创建结束时间(毫秒时间戳)
 *         example: "1701320967890"
 *       - in: query
 *         name: auditStartDate
 *         schema:
 *           type: string
 *         description: 审核开始时间(毫秒时间戳)
 *         example: "170*********0"
 *       - in: query
 *         name: auditEndDate
 *         schema:
 *           type: string
 *         description: 审核结束时间(毫秒时间戳)
 *         example: "1701320967890"
 *       - in: query
 *         name: minAmount
 *         schema:
 *           type: number
 *         description: 最小申请金额
 *         example: 100.00
 *       - in: query
 *         name: maxAmount
 *         schema:
 *           type: number
 *         description: 最大申请金额
 *         example: 10000.00
 *       - in: query
 *         name: invoiceType
 *         schema:
 *           type: string
 *         description: 发票类型
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取发票审核列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 发票审核列表
 *                     pageInfo:
 *                       type: object
 *                       description: 分页信息
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.get('/invoice-audit/list', invoiceAuditController.getInvoiceAuditList.bind(invoiceAuditController));

/**
 * @swagger
 * /api/finance/invoice-audit/statistics:
 *   get:
 *     tags: [InvoiceAudit]
 *     summary: 获取发票审核统计
 *     description: 获取各状态的发票申请数量统计
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取发票审核统计成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       description: 总数
 *                     pendingAudit:
 *                       type: integer
 *                       description: 待审核
 *                     financeApproved:
 *                       type: integer
 *                       description: 财务审核通过
 *                     financeRejected:
 *                       type: integer
 *                       description: 财务审核驳回
 *                     invoiced:
 *                       type: integer
 *                       description: 已开票
 *       401:
 *         description: 未授权
 */
router.get('/invoice-audit/statistics', invoiceAuditController.getInvoiceAuditStatistics.bind(invoiceAuditController));

/**
 * @swagger
 * /api/finance/invoice-audit/{id}:
 *   get:
 *     tags: [InvoiceAudit]
 *     summary: 获取发票审核详情
 *     description: 根据ID获取发票审核的详细信息，包括发票抬头和明细
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 发票申请ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取发票审核详情成功
 *                 data:
 *                   type: object
 *                   description: 发票审核详情
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 */
router.get('/invoice-audit/:id', invoiceAuditController.getInvoiceAuditDetail.bind(invoiceAuditController));

/**
 * @swagger
 * /api/finance/invoice-audit/{id}/finance-audit:
 *   post:
 *     tags: [InvoiceAudit]
 *     summary: 财务审核
 *     description: 对发票申请进行财务审核，支持通过和驳回操作
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 发票申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [approve, reject]
 *                 description: 审核操作(approve-通过，reject-驳回)
 *               remark:
 *                 type: string
 *                 description: 审核备注(驳回时必填)
 *             required:
 *               - action
 *     responses:
 *       200:
 *         description: 审核成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 财务审核操作成功
 *                 data:
 *                   type: object
 *                   description: 审核结果
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 */
router.post('/invoice-audit/:id/finance-audit', invoiceAuditController.financeAudit.bind(invoiceAuditController));

/**
 * @swagger
 * /api/finance/invoice-audit/{id}/upload-invoice:
 *   post:
 *     tags: [InvoiceAudit]
 *     summary: 上传发票文件
 *     description: 上传发票文件并更新发票申请状态为已开票
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 发票申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               invoiceNumber:
 *                 type: string
 *                 description: 发票号码(可选)
 *               invoiceCode:
 *                 type: string
 *                 description: 发票代码(可选)
 *               invoiceDate:
 *                 type: string
 *                 format: date
 *                 description: 开票日期(可选)
 *               invoiceFileUrl:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 发票文件URL数组(必填，支持图片和PDF格式)
 *                 example: ["https://jlc-4.oss-cn-guangzhou.aliyuncs.com/invoices/6d23a963132792e01bdd4807e35d6938.jpeg", "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/invoices/invoice.pdf"]
 *               remark:
 *                 type: string
 *                 description: 备注(可选)
 *             required:
 *               - invoiceFileUrl
 *     responses:
 *       200:
 *         description: 上传成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 发票上传成功
 *                 data:
 *                   type: object
 *                   description: 上传结果
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 */
router.post('/invoice-audit/:id/upload-invoice', invoiceAuditController.uploadInvoice.bind(invoiceAuditController));



// ==================== 认款审核相关路由 ====================

/**
 * @swagger
 * /api/finance/recognition-audit:
 *   get:
 *     summary: 获取认款审核列表
 *     description: 获取账期支付认款审核列表，支持多种筛选条件
 *     tags: [RecognitionAudit]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: paymentSn
 *         schema:
 *           type: string
 *         description: 支付流水号（模糊搜索）
 *       - in: query
 *         name: orderSn
 *         schema:
 *           type: string
 *         description: 订单ID（精确搜索，兼容参数名）
 *       - in: query
 *         name: orderId
 *         schema:
 *           type: string
 *         description: 订单ID（精确搜索）
 *       - in: query
 *         name: recognitionStatus
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2, 3]
 *         description: 认款状态 0-待认款 1-待审核 2-审核通过 3-审核驳回
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: integer
 *         description: 支付开始时间（毫秒时间戳）
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: integer
 *         description: 支付结束时间（毫秒时间戳）
 *       - in: query
 *         name: applyStartTime
 *         schema:
 *           type: integer
 *         description: 申请开始时间（毫秒时间戳，基于创建时间）
 *       - in: query
 *         name: applyEndTime
 *         schema:
 *           type: integer
 *         description: 申请结束时间（毫秒时间戳，基于创建时间）
 *       - in: query
 *         name: channelId
 *         schema:
 *           type: string
 *         description: 渠道ID
 *       - in: query
 *         name: platformId
 *         schema:
 *           type: string
 *         description: 平台ID
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *         description: 店铺ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取认款审核列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 认款审核列表
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: 总记录数
 *                         currentPage:
 *                           type: integer
 *                           description: 当前页码
 *                         totalPage:
 *                           type: integer
 *                           description: 总页数
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/recognition-audit', recognitionAuditController.getRecognitionAuditList.bind(recognitionAuditController));

/**
 * @swagger
 * /api/finance/recognition-audit/{id}:
 *   get:
 *     summary: 获取认款审核详情
 *     description: 根据ID获取认款审核详细信息
 *     tags: [RecognitionAudit]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 认款记录ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取认款审核详情成功
 *                 data:
 *                   type: object
 *                   description: 认款审核详情
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/recognition-audit/:id', recognitionAuditController.getRecognitionAuditDetail.bind(recognitionAuditController));

/**
 * @swagger
 * /api/finance/recognition-audit/{id}/audit:
 *   post:
 *     summary: 认款审核操作
 *     description: 对认款记录进行审核通过或驳回操作，审核通过时同时更新订单状态为交易成功
 *     tags: [RecognitionAudit]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 认款记录ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [approve, reject]
 *                 description: 审核操作 approve-通过 reject-驳回
 *               auditAttachments:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 审核附件URL数组（可选）
 *               remark:
 *                 type: string
 *                 description: 审核备注（可选）
 *           example:
 *             action: "approve"
 *             auditAttachments: ["https://example.com/file1.pdf", "https://example.com/file2.jpg"]
 *             remark: "审核通过，资料齐全"
 *     responses:
 *       200:
 *         description: 审核成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 认款审核通过成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 认款记录ID
 *                     recognitionStatus:
 *                       type: integer
 *                       description: 更新后的认款状态
 *                     recognitionStatusName:
 *                       type: string
 *                       description: 认款状态名称
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 记录不存在
 *       500:
 *         description: 服务器错误
 */
router.post('/recognition-audit/:id/audit', recognitionAuditController.auditRecognition.bind(recognitionAuditController));

// ==================== 付款凭证相关路由 ====================

/**
 * @swagger
 * /api/finance/payment-voucher:
 *   post:
 *     tags: [PaymentVoucher]
 *     summary: 创建付款凭证
 *     description: 创建新的付款凭证记录
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               voucherType:
 *                 type: integer
 *                 description: 凭证类型(0-银行流水，1-现金收款，2-其他)
 *                 enum: [0, 1, 2]
 *               amount:
 *                 type: number
 *                 description: 金额
 *                 minimum: 0.01
 *               payerName:
 *                 type: string
 *                 description: 付款方名称
 *                 maxLength: 255
 *               paymentDate:
 *                 type: string
 *                 format: date-time
 *                 description: 付款日期
 *               remarks:
 *                 type: string
 *                 description: 备注
 *                 maxLength: 1000
 *               attachments:
 *                 type: array
 *                 description: 附件列表
 *                 items:
 *                   type: object
 *                   properties:
 *                     fileName:
 *                       type: string
 *                       description: 文件名
 *                     originalName:
 *                       type: string
 *                       description: 原始文件名
 *                     fileUrl:
 *                       type: string
 *                       description: 文件URL
 *                     fileSize:
 *                       type: integer
 *                       description: 文件大小(字节)
 *                     fileType:
 *                       type: string
 *                       description: 文件类型
 *                     extension:
 *                       type: string
 *                       description: 文件扩展名
 *             required:
 *               - voucherType
 *               - amount
 *               - payerName
 *               - paymentDate
 *     responses:
 *       200:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 创建付款凭证成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 凭证ID
 *                     voucherNumber:
 *                       type: string
 *                       description: 凭证编号
 *                     voucherType:
 *                       type: integer
 *                       description: 凭证类型
 *                     voucherTypeLabel:
 *                       type: string
 *                       description: 凭证类型名称
 *                     amount:
 *                       type: number
 *                       description: 金额
 *                     payerName:
 *                       type: string
 *                       description: 付款方名称
 *                     paymentDate:
 *                       type: integer
 *                       description: 付款日期(时间戳)
 *                     remarks:
 *                       type: string
 *                       description: 备注
 *                     status:
 *                       type: integer
 *                       description: 状态
 *                     statusLabel:
 *                       type: string
 *                       description: 状态名称
 *                     attachments:
 *                       type: array
 *                       description: 附件列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 附件ID
 *                           fileName:
 *                             type: string
 *                             description: 文件名
 *                           originalName:
 *                             type: string
 *                             description: 原始文件名
 *                           fileUrl:
 *                             type: string
 *                             description: 文件URL
 *                           fileSize:
 *                             type: integer
 *                             description: 文件大小
 *                           fileType:
 *                             type: string
 *                             description: 文件类型
 *                     createdAt:
 *                       type: integer
 *                       description: 创建时间(时间戳)
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/payment-voucher', paymentVoucherController.createPaymentVoucher.bind(paymentVoucherController));

/**
 * @swagger
 * /api/finance/payment-voucher:
 *   get:
 *     tags: [PaymentVoucher]
 *     summary: 获取付款凭证列表
 *     description: 分页获取付款凭证列表，支持多种筛选条件
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: voucherType
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2]
 *         description: 凭证类型(0-银行流水，1-现金收款，2-其他)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 状态(0-未关联，1-已关联)
 *       - in: query
 *         name: relatedStatus
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 关联状态(0-未关联，1-已关联)
 *       - in: query
 *         name: payerName
 *         schema:
 *           type: string
 *         description: 付款方名称(模糊搜索)
 *       - in: query
 *         name: voucherNumber
 *         schema:
 *           type: string
 *         description: 凭证编号(模糊搜索)
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期(YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期(YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取付款凭证列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 凭证列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 凭证ID
 *                           voucherNumber:
 *                             type: string
 *                             description: 凭证编号
 *                           voucherType:
 *                             type: integer
 *                             description: 凭证类型
 *                           voucherTypeLabel:
 *                             type: string
 *                             description: 凭证类型名称
 *                           amount:
 *                             type: number
 *                             description: 金额
 *                           payerName:
 *                             type: string
 *                             description: 付款方名称
 *                           paymentDate:
 *                             type: integer
 *                             description: 付款日期(时间戳)
 *                           status:
 *                             type: integer
 *                             description: 状态
 *                           statusLabel:
 *                             type: string
 *                             description: 状态名称
 *                           relatedStatus:
 *                             type: integer
 *                             description: 关联状态
 *                           relatedStatusLabel:
 *                             type: string
 *                             description: 关联状态名称
 *                           attachments:
 *                             type: array
 *                             description: 附件列表
 *                           createdAt:
 *                             type: integer
 *                             description: 创建时间(时间戳)
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: 总记录数
 *                         currentPage:
 *                           type: integer
 *                           description: 当前页码
 *                         totalPage:
 *                           type: integer
 *                           description: 总页数
 *                         pageSize:
 *                           type: integer
 *                           description: 每页数量
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/payment-voucher', paymentVoucherController.getPaymentVoucherList.bind(paymentVoucherController));

/**
 * @swagger
 * /api/finance/payment-voucher/{id}:
 *   get:
 *     tags: [PaymentVoucher]
 *     summary: 获取付款凭证详情
 *     description: 根据ID获取付款凭证详细信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 付款凭证ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取付款凭证详情成功
 *                 data:
 *                   type: object
 *                   description: 付款凭证详细信息
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 凭证不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/payment-voucher/:id', paymentVoucherController.getPaymentVoucherById.bind(paymentVoucherController));

/**
 * @swagger
 * /api/finance/payment-voucher/merge:
 *   post:
 *     tags: [PaymentVoucher]
 *     summary: 合并付款凭证
 *     description: 将多个付款凭证合并为一个新的凭证
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               voucherIds:
 *                 type: array
 *                 description: 要合并的凭证ID列表
 *                 items:
 *                   type: string
 *                 minItems: 2
 *               mergeReason:
 *                 type: string
 *                 description: 合并原因说明
 *                 maxLength: 500
 *             required:
 *               - voucherIds
 *               - mergeReason
 *     responses:
 *       200:
 *         description: 合并成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 付款凭证合并成功
 *                 data:
 *                   type: object
 *                   description: 合并后的新凭证信息
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/payment-voucher/merge', paymentVoucherController.mergePaymentVouchers.bind(paymentVoucherController));

/**
 * @swagger
 * /api/finance/payment-voucher/available-for-recognition:
 *   get:
 *     tags: [PaymentVoucher]
 *     summary: 获取可用于认款的付款凭证列表
 *     description: 获取未关联且可用于认款申请的付款凭证列表
 *     parameters:
 *       - in: query
 *         name: payerName
 *         schema:
 *           type: string
 *         description: 付款方名称筛选
 *       - in: query
 *         name: minAmount
 *         schema:
 *           type: number
 *         description: 最小金额筛选
 *       - in: query
 *         name: maxAmount
 *         schema:
 *           type: number
 *         description: 最大金额筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取可用付款凭证列表成功
 *                 data:
 *                   type: array
 *                   description: 可用凭证列表
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: 凭证ID
 *                       voucherNumber:
 *                         type: string
 *                         description: 凭证编号
 *                       voucherType:
 *                         type: integer
 *                         description: 凭证类型
 *                       voucherTypeLabel:
 *                         type: string
 *                         description: 凭证类型名称
 *                       amount:
 *                         type: number
 *                         description: 金额
 *                       payerName:
 *                         type: string
 *                         description: 付款方名称
 *                       paymentDate:
 *                         type: integer
 *                         description: 付款日期(时间戳)
 *                       mergedStatus:
 *                         type: integer
 *                         description: 合并状态
 *                       mergedStatusLabel:
 *                         type: string
 *                         description: 合并状态名称
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/payment-voucher/available-for-recognition', paymentVoucherController.getAvailableVouchersForRecognition.bind(paymentVoucherController));

// 创建测试数据路由
router.post('/payment-voucher/create-test-data', paymentVoucherController.createTestData.bind(paymentVoucherController));

module.exports = router;
