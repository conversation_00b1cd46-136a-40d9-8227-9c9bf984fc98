-- ====================================================================
-- 发货管理模块数据库表创建脚本
-- 包含：配送方式表(csm_delivery_method)、发货信息表(csm_shipping_info)
-- 创建时间：2024-07-11
-- ====================================================================

-- 创建 csm schema（如果不存在）
CREATE SCHEMA IF NOT EXISTS csm;

-- ====================================================================
-- 配送方式表 (csm_delivery_method)
-- ====================================================================
CREATE TABLE IF NOT EXISTS csm.csm_delivery_method (
    id SERIAL PRIMARY KEY,
    method_name VARCHAR(100) NOT NULL COMMENT '配送方式名称（如：标准快递、次日达）',
    method_code VARCHAR(50) NOT NULL COMMENT '配送方式编码（如：EXPRESS、NEXT_DAY）',
    description TEXT COMMENT '配送方式描述信息，如时效说明',
    channel_id BIGINT NULL COMMENT '渠道ID（关联已有渠道表，NULL表示通用配送方式）',
    is_enabled INT NOT NULL DEFAULT 1 CHECK (is_enabled IN (0, 1)) COMMENT '启用状态（INT类型）：1=启用，0=禁用',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，自动生成',
    update_time TIMESTAMP COMMENT '更新时间，手动维护'
);

-- 添加约束和索引
ALTER TABLE csm.csm_delivery_method 
ADD CONSTRAINT uniq_method_code_channel UNIQUE (method_code, channel_id);

-- 添加外键约束（如果channel表存在）
-- ALTER TABLE csm.csm_delivery_method 
-- ADD CONSTRAINT fk_delivery_channel FOREIGN KEY (channel_id) 
--     REFERENCES channel(id) ON DELETE SET NULL;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_delivery_channel_enabled ON csm.csm_delivery_method(channel_id, is_enabled);
CREATE INDEX IF NOT EXISTS idx_delivery_method_enabled ON csm.csm_delivery_method(is_enabled);
CREATE INDEX IF NOT EXISTS idx_delivery_method_code ON csm.csm_delivery_method(method_code);

-- 添加表注释
COMMENT ON TABLE csm.csm_delivery_method IS '配送方式配置表，支持按渠道区分';
COMMENT ON COLUMN csm.csm_delivery_method.id IS '自增主键，唯一标识一种配送方式';
COMMENT ON COLUMN csm.csm_delivery_method.method_name IS '配送方式名称（如：标准快递、次日达）';
COMMENT ON COLUMN csm.csm_delivery_method.method_code IS '配送方式编码（如：EXPRESS、NEXT_DAY）';
COMMENT ON COLUMN csm.csm_delivery_method.description IS '配送方式描述信息，如时效说明';
COMMENT ON COLUMN csm.csm_delivery_method.channel_id IS '渠道ID（关联已有渠道表，NULL表示通用配送方式）';
COMMENT ON COLUMN csm.csm_delivery_method.is_enabled IS '启用状态（INT类型）：1=启用，0=禁用';
COMMENT ON COLUMN csm.csm_delivery_method.create_time IS '创建时间，自动生成';
COMMENT ON COLUMN csm.csm_delivery_method.update_time IS '更新时间，手动维护';

-- ====================================================================
-- 发货信息表 (csm_shipping_info)
-- ====================================================================
CREATE TABLE IF NOT EXISTS csm.csm_shipping_info (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL COMMENT '订单编号，关联业务系统订单标识',
    order_type INT NOT NULL CHECK (order_type IN (1, 2)) COMMENT '订单类型（INT类型）：1=采购订单，2=拆分商品',
    delivery_method_id INT NOT NULL COMMENT '配送方式ID，关联csm_delivery_method表主键',
    express_company_name VARCHAR(100) NOT NULL COMMENT '物流公司名称（如：顺丰速运）',
    express_company_id VARCHAR(50) NOT NULL COMMENT '物流公司编码（如：SF）',
    tracking_no VARCHAR(50) NOT NULL COMMENT '物流单号，用于跟踪物流状态',
    shipping_location VARCHAR(200) NOT NULL COMMENT '发货地址，详细到门牌号',
    attachment VARCHAR(500) COMMENT '附件路径，多个附件用逗号分隔',
    delivery_list_photo VARCHAR(500) COMMENT '配送清单照片路径，支持多张',
    package_photo VARCHAR(500) COMMENT '包裹照片路径，支持多张',
    business_contact VARCHAR(50) NOT NULL COMMENT '业务联系人姓名',
    business_phone VARCHAR(20) NOT NULL COMMENT '业务联系电话，支持手机/固话',
    remarks TEXT COMMENT '发货备注信息，如特殊要求',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，自动生成',
    update_time TIMESTAMP COMMENT '记录更新时间，手动维护',
    delete_time TIMESTAMP COMMENT '逻辑删除标记（NULL=未删除，非NULL=删除时间）'
);

-- 添加外键约束
ALTER TABLE csm.csm_shipping_info 
ADD CONSTRAINT fk_delivery_method FOREIGN KEY (delivery_method_id) 
    REFERENCES csm.csm_delivery_method(id);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_shipping_order ON csm.csm_shipping_info(order_no, order_type);
CREATE INDEX IF NOT EXISTS idx_tracking_no ON csm.csm_shipping_info(tracking_no);
CREATE INDEX IF NOT EXISTS idx_shipping_create_time ON csm.csm_shipping_info(create_time);
CREATE INDEX IF NOT EXISTS idx_shipping_delete_time ON csm.csm_shipping_info(delete_time);

-- 添加表注释
COMMENT ON TABLE csm.csm_shipping_info IS '发货信息主表，记录订单物流配送详情';
COMMENT ON COLUMN csm.csm_shipping_info.id IS '自增主键，唯一标识一条发货记录';
COMMENT ON COLUMN csm.csm_shipping_info.order_no IS '订单编号，关联业务系统订单标识';
COMMENT ON COLUMN csm.csm_shipping_info.order_type IS '订单类型（INT类型）：1=采购订单，2=拆分商品';
COMMENT ON COLUMN csm.csm_shipping_info.delivery_method_id IS '配送方式ID，关联csm_delivery_method表主键';
COMMENT ON COLUMN csm.csm_shipping_info.express_company_name IS '物流公司名称（如：顺丰速运）';
COMMENT ON COLUMN csm.csm_shipping_info.express_company_id IS '物流公司编码（如：SF）';
COMMENT ON COLUMN csm.csm_shipping_info.tracking_no IS '物流单号，用于跟踪物流状态';
COMMENT ON COLUMN csm.csm_shipping_info.shipping_location IS '发货地址，详细到门牌号';
COMMENT ON COLUMN csm.csm_shipping_info.attachment IS '附件路径，多个附件用逗号分隔';
COMMENT ON COLUMN csm.csm_shipping_info.delivery_list_photo IS '配送清单照片路径，支持多张';
COMMENT ON COLUMN csm.csm_shipping_info.package_photo IS '包裹照片路径，支持多张';
COMMENT ON COLUMN csm.csm_shipping_info.business_contact IS '业务联系人姓名';
COMMENT ON COLUMN csm.csm_shipping_info.business_phone IS '业务联系电话，支持手机/固话';
COMMENT ON COLUMN csm.csm_shipping_info.remarks IS '发货备注信息，如特殊要求';
COMMENT ON COLUMN csm.csm_shipping_info.create_time IS '记录创建时间，自动生成';
COMMENT ON COLUMN csm.csm_shipping_info.update_time IS '记录更新时间，手动维护';
COMMENT ON COLUMN csm.csm_shipping_info.delete_time IS '逻辑删除标记（NULL=未删除，非NULL=删除时间）';

-- ====================================================================
-- 初始化基础配送方式数据
-- ====================================================================
INSERT INTO csm.csm_delivery_method (method_name, method_code, description, channel_id, is_enabled) VALUES
('京东大件物流', 'JD_LARGE', '适用于大件商品的物流配送', NULL, 1),
('京东快运', 'JD_EXPRESS', '京东快运物流服务', NULL, 1),
('京东快递', 'JD_COURIER', '京东快递配送服务', NULL, 1),
('德邦物流', 'DEPPON', '德邦物流配送服务', NULL, 1),
('德邦快递', 'DEPPON_EXPRESS', '德邦快递配送服务', NULL, 1),
('德邦快运', 'DEPPON_FREIGHT', '德邦快运物流服务', NULL, 1),
('百世快递', 'BEST_EXPRESS', '百世快递配送服务', NULL, 1),
('顺丰速运', 'SF_EXPRESS', '顺丰快递配送服务', NULL, 1),
('圆通速递', 'YTO_EXPRESS', '圆通快递配送服务', NULL, 1),
('中通快递', 'ZTO_EXPRESS', '中通快递配送服务', NULL, 1),
('申通快递', 'STO_EXPRESS', '申通快递配送服务', NULL, 1),
('韵达速递', 'YD_EXPRESS', '韵达快递配送服务', NULL, 1),
('天天快递', 'TT_EXPRESS', '天天快递配送服务', NULL, 1),
('宅急送', 'ZJS_EXPRESS', '宅急送配送服务', NULL, 1),
('邮政EMS', 'EMS_EXPRESS', '中国邮政EMS配送服务', NULL, 1),
('安能物流', 'ANE_EXPRESS', '安能物流配送服务', NULL, 1),
('壹米滴答', 'YMDD_EXPRESS', '壹米滴答物流服务', NULL, 1),
('优速快递', 'UC_EXPRESS', '优速快递配送服务', NULL, 1),
('国通快递', 'GTO_EXPRESS', '国通快递配送服务', NULL, 1),
('其他', 'OTHER', '其他配送方式', NULL, 1)
ON CONFLICT (method_code, channel_id) DO NOTHING;

-- ====================================================================
-- 执行完成提示
-- ====================================================================
DO $$
BEGIN
    RAISE NOTICE '发货管理模块数据库表创建完成！';
    RAISE NOTICE '已创建表：';
    RAISE NOTICE '  - csm.csm_delivery_method (配送方式表)';
    RAISE NOTICE '  - csm.csm_shipping_info (发货信息表)';
    RAISE NOTICE '已初始化 % 条基础配送方式数据', (SELECT COUNT(*) FROM csm.csm_delivery_method);
END $$;
