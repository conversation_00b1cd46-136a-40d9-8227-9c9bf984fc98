/// 询价详情表，存储询价单的具体产品信息
model csm_inquiry_details {
  // 主键
  id                    BigInt    @id @default(autoincrement()) /// 询价详情ID，主键，自增长
  
  // 关联信息
  inquiry_id            BigInt    /// 询价单ID
  inquiry_no            String    @db.VarChar(50) /// 询价序号
  
  // 产品信息
  product_name          String    @db.VarChar(255) /// 产品名称
  brand                 String?   @db.VarChar(100) /// 品牌
  model                 String?   @db.VarChar(100) /// 型号
  specification         String?   @db.Text /// 规格描述
  reference_link        String?   @db.VarChar(500) /// 参考链接
  unit                  String?   @db.VarChar(50) /// 单位
  quantity              Decimal   @db.Decimal(15,4) /// 需求数量
  reference_price       Decimal?  @db.Decimal(15,4) /// 参考价格
  customer_product_code String?   @db.VarChar(100) /// 客户产品编码
  inquiry_remark        String?   @db.Text /// 询价备注
  
  // 报价信息
  quote_price           Decimal?  @db.Decimal(15,4) /// 报价价格
  quote_status          Int       @default(0) /// 报价状态：0-未报价，1-已报价，2-已确认，3-已拒绝
  inquiry_status        Int       @default(0) /// 询价状态：0-未询价，1-已询价待审核，2-审核通过，3-审核驳回，4-已取消，5-已确认销售价，6-重新询价，7-资料待审核，8-资料审核驳回，9-资料审核通过，10-已操作上架，11-上游-上架状态
  
  // 时间戳
  created_at            BigInt    /// 创建时间（时间戳）
  updated_at            BigInt    /// 更新时间（时间戳）
  deleted_at            BigInt?   /// 删除时间（软删除）
  created_by            BigInt?   /// 创建人 ID
  updated_by            BigInt?   /// 更新人 ID
  
  // 关联关系
  inquiry_order         csm_inquiry_orders @relation(fields: [inquiry_id], references: [id], onDelete: Cascade)
  
  // 索引
  @@index([deleted_at])
  @@index([inquiry_id])
  @@index([inquiry_no])
  @@index([product_name])
  @@index([brand])
  @@index([quote_status])
  @@index([created_by])
  @@index([updated_by])
  
  @@map("csm_inquiry_details")
  @@schema("csm")
}
