/// 发货信息主表，记录订单物流配送详情
model csm_shipping_info {
  id                    BigInt                @id @default(autoincrement()) /// 自增主键，唯一标识一条发货记录
  order_no              String                @db.VarChar(50) /// 订单编号，关联业务系统订单标识
  order_type            Int /// 订单类型（INT类型）：1=采购订单，2=拆分商品
  delivery_method_id    Int /// 配送方式ID，关联csm_delivery_method表主键
  express_company_name  String                @db.VarChar(100) /// 物流公司名称（如：顺丰速运）
  express_company_id    String                @db.VarChar(50) /// 物流公司编码（如：SF）
  tracking_no           String                @db.VarChar(50) /// 物流单号，用于跟踪物流状态
  shipping_location     String                @db.VarChar(200) /// 发货地址，详细到门牌号
  attachment            String?               @db.VarChar(500) /// 附件路径，多个附件用逗号分隔
  delivery_list_photo   String?               @db.VarChar(500) /// 配送清单照片路径，支持多张
  package_photo         String?               @db.VarChar(500) /// 包裹照片路径，支持多张
  business_contact      String                @db.VarChar(50) /// 业务联系人姓名
  business_phone        String                @db.VarChar(20) /// 业务联系电话，支持手机/固话
  remarks               String?               @db.Text /// 发货备注信息，如特殊要求
  create_time           DateTime              @default(now()) @db.Timestamp(6) /// 记录创建时间，自动生成
  update_time           DateTime?             @db.Timestamp(6) /// 记录更新时间，手动维护
  delete_time           DateTime?             @db.Timestamp(6) /// 逻辑删除标记（NULL=未删除，非NULL=删除时间）

  // 关联关系
  delivery_method       csm_delivery_method   @relation(fields: [delivery_method_id], references: [id])

  // 索引
  @@index([order_no, order_type], map: "idx_shipping_order")
  @@index([tracking_no], map: "idx_tracking_no")
  @@index([create_time], map: "idx_shipping_create_time")
  @@index([delete_time], map: "idx_shipping_delete_time")

  @@map("csm_shipping_info")
  @@schema("csm")
}
