// 采购订单拆分单主表
model purchase_order_split {
  id                        BigInt                   @id @default(autoincrement()) // 拆分单ID，主键，自增长
  split_number              String                   @unique @db.VarChar(64) // 拆分单编号，唯一
  original_purchase_order_id BigInt                  // 原始采购订单ID
  original_order_number     String                   @db.VarChar(64) // 原始采购订单编号
  
  // 拆分基础信息
  split_type                Int                      @default(1) // 拆分类型：1-单商品拆分 2-多商品拆分

  // 收货信息（从原订单复制）
  recipient_name            String?                  @db.VarChar(100) // 收货人姓名
  recipient_phone           String?                  @db.VarChar(20) // 收货电话
  delivery_address          String?                  @db.Text // 配送地址
  order_source              String?                  @db.VarChar(50) // 订单来源
  
  // 拆分状态
  split_status              Int                      @default(0) // 拆分状态：0-待处理 1-拆分中 2-已完成 3-已取消

  // 数量统计
  total_split_quantity      Int                      @default(0) // 拆分总数量
  
  // 操作人员信息
  splitter_id               BigInt?                  // 拆分操作员ID
  splitter_name             String?                  @db.VarChar(100) // 拆分操作员姓名

  // 时间信息
  split_time                DateTime                 @default(now()) // 拆分时间
  
  // 系统字段
  submitter                 String?                  @db.VarChar(100) // 提交人
  updater                   String?                  @db.VarChar(100) // 更新人
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime?                // 删除时间
  
  // 关联关系
  original_purchase_order   purchase_order           @relation(fields: [original_purchase_order_id], references: [id])
  split_items               purchase_order_split_item[] // 拆分商品项
  shipping_link             purchase_order_shipping_link? // 发货链接
  
  @@index([split_number], map: "idx_split_number")
  @@index([original_purchase_order_id], map: "idx_original_purchase_order_id")
  @@index([split_status], map: "idx_split_status")
  @@index([splitter_id], map: "idx_splitter_id")
  @@index([split_time], map: "idx_split_time")
  @@index([created_at], map: "idx_split_created_at")
  @@index([deleted_at], map: "idx_split_deleted_at")
  @@schema("csm")
}
