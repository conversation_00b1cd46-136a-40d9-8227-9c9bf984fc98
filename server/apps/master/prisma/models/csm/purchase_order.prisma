// 采购订单主表
model purchase_order {
  id                        BigInt                   @id @default(autoincrement()) // 采购订单ID，主键，自增长
  purchase_order_number     String                   @unique @db.VarChar(64) // 采购订单编号，唯一
  original_order_id         BigInt                   // 原始订单ID，关联base.orders表
  original_order_number     String?                  @db.VarChar(64) // 原始订单编号
  third_party_order_sn      String?                  @db.VarChar(128) // 第三方订单号
  
  // 采购相关信息
  purchaser_id              BigInt?                  // 采购员ID
  purchaser_name            String?                  @db.VarChar(100) // 采购员姓名
  supplier_id               BigInt?                  // 供应商ID
  supplier_name             String?                  @db.VarChar(200) // 供应商名称
  supplier_code             String?                  @db.VarChar(100) // 供应商编码

  // 订单基础信息
  order_source              String?                  @db.VarChar(50) // 订单来源：商城/竞价/手动
  order_type                String?                  @db.VarChar(100) // 订单类型：商城自然单/竞价自然单等
  follower                  String?                  @db.VarChar(100) // 跟单员
  buyer_account             String?                  @db.VarChar(200) // 下单账号
  delivery_address          String?                  @db.Text // 配送地址

  // 订单状态
  purchase_status           Int                      @default(0) // 采购状态：0-待处理 1-已分配 2-采购中 3-已完成 4-已取消
  order_status              Int                      @default(0) // 订单状态：0-待发货 1-发货未完成 2-待收货 3-已废止 4-已关闭 5-废止待确认
  payment_status            Int                      @default(0) // 支付状态：同步自原订单
  shipping_status           Int                      @default(0) // 配送状态：同步自原订单

  // 扩展状态信息
  erp_status                Int                      @default(0) // ERP状态：0-待同步 1-已同步 2-同步失败
  split_order_status        Int                      @default(0) // 拆单状态：0-未拆分 1-已拆分 2-拆分中
  split_status              Int                      @default(0) // 商品拆分状态：0-未拆分 1-部分拆分 2-全部拆分 3-拆分中
  audit_status              Int                      @default(0) // 审核状态：0-待审核 1-审核通过 2-审核驳回 3-废止待确认
  cancel_status             Int?                     // 取消状态：0-待废止 1-废止待确认 2-已废止
  pending_cancel_status     Int?                     // 待取消状态：0-待废止 1-废止待确认 2-废止审核中 3-采购员待审核
  link_status               Int                      @default(0) // 链接状态：0-unlinked 1-linked
  is_loss                   Int                      @default(0) // 是否亏损：0-no 1-yes
  loss_reason               String?                  @db.VarChar(100) // 亏损原因：freight_loss/quote_error等
  link_generated            Boolean                  @default(false) // 是否生成链接
  
  // 金额信息
  total_product_amount      Decimal                  @default(0.00) @db.Decimal(12, 2) // 商品总金额
  shipping_fee              Decimal                  @default(0.00) @db.Decimal(10, 2) // 运费
  freight                   Decimal                  @default(0.00) @db.Decimal(10, 2) // 运费(页面字段)
  discount_amount           Decimal                  @default(0.00) @db.Decimal(10, 2) // 折扣金额
  tax_amount                Decimal                  @default(0.00) @db.Decimal(10, 2) // 税费
  total_amount              Decimal                  @default(0.00) @db.Decimal(12, 2) // 订单总金额
  total_quantity            Int                      @default(0) // 总数量
  split_count               Int                      @default(0) // 拆分次数
  purchase_cost             Decimal?                 @db.Decimal(12, 2) // 采购成本
  cost_total                Decimal?                 @db.Decimal(12, 2) // 成本总价
  gross_profit_rate         String?                  @db.VarChar(20) // 毛利率

  // 物流信息
  logistics_info            String?                  @db.VarChar(200) // 物流信息
  logistics_number          String?                  @db.VarChar(100) // 物流单号
  
  // 收货信息
  recipient_name            String?                  @db.VarChar(100) // 收货人姓名
  recipient_phone           String?                  @db.VarChar(20) // 收货人电话
  recipient_address         String?                  @db.Text // 收货地址
  actual_receiver           String?                  @db.VarChar(100) // 实际收货人
  actual_phone              String?                  @db.VarChar(20) // 实际联系电话
  actual_address            String?                  @db.Text // 实际收货地址

  // 备注信息
  business_remark           String?                  @db.Text // 业务员备注
  order_remark              String?                  @db.Text // 订单备注
  customer_remark           String?                  @db.Text // 客户备注
  purchase_remark           String?                  @db.Text // 采购备注
  
  // 渠道信息
  channel_id                BigInt? // 渠道ID
  channel_name              String?                  @db.VarChar(100) // 渠道名称
  platform_id               BigInt? // 平台ID
  platform_name             String?                  @db.VarChar(100) // 平台名称
  store_id                  BigInt? // 店铺ID
  store_name                String?                  @db.VarChar(100) // 店铺名称
  
  // 时间信息
  original_order_time       DateTime? // 原始下单时间
  purchase_time             DateTime? // 采购时间
  purchase_apply_time       DateTime                 @default(now()) // 申请采购时间
  purchase_assign_time      DateTime? // 分配采购员时间
  purchase_complete_time    DateTime? // 采购完成时间
  expected_delivery_time    DateTime? // 预期交货时间
  actual_delivery_time      DateTime? // 实际交货时间
  
  // 系统字段
  submitter                 String?                  @db.VarChar(100) // 提交人
  updater                   String?                  @db.VarChar(100) // 更新人
  created_at                DateTime                 @default(now()) // 创建时间
  updated_at                DateTime                 @updatedAt // 更新时间
  deleted_at                DateTime? // 删除时间
  
  // 关联关系
  original_order            orders                @relation(fields: [original_order_id], references: [id], map: "fk_purchase_order_original_order_id") // 关联原订单
  purchase_order_items      purchase_order_item[] // 采购订单商品项
  purchase_order_attachments purchase_order_attachment[] // 采购订单附件
  purchase_order_logs       purchase_order_log[] // 采购订单日志
  purchase_order_splits     purchase_order_split[] // 采购订单拆分单
  purchase_order_expenses   purchase_order_expense[] // 采购订单费用单
  after_sales_applications  afterSalesApplication[] // 售后申请
  purchase_order_shipping_link purchase_order_shipping_link? // 采购订单发货链接
  
  @@index([purchase_order_number], map: "idx_purchase_order_number")
  @@index([original_order_id], map: "idx_original_order_id")
  @@index([purchaser_id], map: "idx_purchaser_id")
  @@index([supplier_id], map: "idx_supplier_id")
  @@index([purchase_status], map: "idx_purchase_status")
  @@index([purchase_apply_time], map: "idx_purchase_apply_time")
  @@index([created_at], map: "idx_purchase_order_created_at")
  @@index([deleted_at], map: "idx_purchase_order_deleted_at")
  @@index([order_source], map: "idx_purchase_order_order_source")
  @@index([order_type], map: "idx_purchase_order_order_type")
  @@index([follower], map: "idx_purchase_order_follower")
  @@index([erp_status], map: "idx_purchase_order_erp_status")
  @@index([split_status], map: "idx_purchase_order_split_status")
  @@index([audit_status], map: "idx_purchase_order_audit_status")
  @@index([link_status], map: "idx_purchase_order_link_status")
  @@index([is_loss], map: "idx_purchase_order_is_loss")
  @@index([logistics_number], map: "idx_purchase_order_logistics_number")
  @@index([purchase_time], map: "idx_purchase_order_purchase_time")
  @@schema("csm")
}
