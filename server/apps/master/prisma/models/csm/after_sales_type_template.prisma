/// 售后类型内容模板表，存储售后申请表单中的售后类型和对应的模板内容
model CsmAfterSalesTypeTemplate {
  // 主键
  id            BigInt    @id @default(autoincrement())        /// 售后类型模板ID，自增主键
  
  // 基本信息
  pid           BigInt    @default(0)                          /// 父级ID，0表示顶级分类
  name          String    @db.VarChar(100)                     /// 售后类型名称，必填，最大100字符
  template      String?   @db.Text                             /// 模板内容，选填（父级分类可能没有模板）
  sort_order    Int       @default(0)                          /// 排序字段，默认为0
  status        Int       @default(1)                          /// 状态：1-启用，0-禁用，默认启用
  
  // 审计字段
  created_at    BigInt                                         /// 创建时间戳（毫秒）
  updated_at    BigInt                                         /// 更新时间戳（毫秒）
  created_by    BigInt?                                        /// 创建人ID，选填
  updated_by    BigInt?                                        /// 更新人ID，选填
  deleted_at    BigInt?                                        /// 删除时间戳（毫秒）（软删除）

  // 自关联关系（支持层级结构）
  parent        CsmAfterSalesTypeTemplate?  @relation("ParentChild", fields: [pid], references: [id])
  children      CsmAfterSalesTypeTemplate[] @relation("ParentChild")

  // 索引
  @@index([pid])                                               /// 父级ID索引，用于查询子分类
  @@index([status])                                            /// 状态索引，用于过滤启用/禁用的记录
  @@index([deleted_at])                                        /// 软删除索引
  @@index([created_at])                                        /// 创建时间索引
  @@index([sort_order])                                        /// 排序索引

  @@map("csm_after_sales_type_template")
  @@schema("csm")
}
