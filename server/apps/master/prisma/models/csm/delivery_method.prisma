/// 配送方式配置表，支持按渠道区分
model csm_delivery_method {
  id            Int       @id @default(autoincrement()) /// 自增主键，唯一标识一种配送方式
  method_name   String    @db.VarChar(100) /// 配送方式名称（如：标准快递、次日达）
  method_code   String    @db.VarChar(50) /// 配送方式编码（如：EXPRESS、NEXT_DAY）
  description   String?   @db.Text /// 配送方式描述信息，如时效说明
  channel_id    BigInt? /// 渠道ID（关联已有渠道表，NULL表示通用配送方式）
  is_enabled    Int       @default(1) /// 启用状态（INT类型）：1=启用，0=禁用
  create_time   DateTime  @default(now()) @db.Timestamp(6) /// 创建时间，自动生成
  update_time   DateTime? @db.Timestamp(6) /// 更新时间，手动维护

  // 关联关系
  channel       channel?  @relation(fields: [channel_id], references: [id], onDelete: SetNull)
  shipping_info csm_shipping_info[] /// 关联的发货信息

  // 索引和约束
  @@unique([method_code, channel_id], map: "uniq_method_code_channel")
  @@index([channel_id, is_enabled], map: "idx_delivery_channel_enabled")
  @@index([is_enabled], map: "idx_delivery_method_enabled")
  @@index([method_code], map: "idx_delivery_method_code")

  @@map("csm_delivery_method")
  @@schema("csm")
}
