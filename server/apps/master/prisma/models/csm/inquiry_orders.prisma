/// 询价单表，存储询价单基本信息
model csm_inquiry_orders {
  // 主键
  id                BigInt    @id @default(autoincrement()) /// 询价单ID，主键，自增长
  
  // 基本信息
  inquiry_no        String    @unique @db.VarChar(50) /// 询价单号，唯一
  customer_id       String    @db.VarChar(255) /// 客户ID
  contact_id        BigInt?   /// 联系人ID
  customer_address  String?   @db.Text /// 客户地址
  salesman_id       BigInt?   /// 业务员ID
  inquirer_id       BigInt?   /// 询价员ID
  channel_id        BigInt?   /// 下单渠道ID
  attachments       Json?     /// 附件信息（JSON格式存储）
  
  // 状态信息
  total_items       Int       @default(0) /// 询价项目总数
  remark            String?   @db.Text /// 备注
  
  // 时间戳
  created_at        BigInt    /// 创建时间（时间戳）
  updated_at        BigInt    /// 更新时间（时间戳）
  created_by        BigInt?   /// 创建人ID
  updated_by        BigInt?   /// 更新人ID
  deleted_at        BigInt?   /// 删除时间（软删除）
  
  // 关联关系
  inquiry_details   csm_inquiry_details[] /// 询价详情列表
  
  // 索引
  @@index([deleted_at])
  @@index([inquiry_no])
  @@index([customer_id])
  @@index([salesman_id])
  @@index([inquirer_id])
  @@index([channel_id])
  @@index([created_at])
  
  @@map("csm_inquiry_orders")
  @@schema("csm")
}
