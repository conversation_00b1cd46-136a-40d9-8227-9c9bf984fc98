const AfterSalesTypeTemplateService = require('../services/AfterSalesTypeTemplateService');

/**
 * 售后类型内容模板控制器
 */
class AfterSalesTypeTemplateController {
  constructor(prisma) {
    this.afterSalesTypeTemplateService = new AfterSalesTypeTemplateService(prisma);
  }

  /**
   * 获取售后类型内容模板列表
   */
  async getList(req, res) {
    try {
      const result = await this.afterSalesTypeTemplateService.getList(req.query);
      res.json(result);
    } catch (error) {
      console.error('获取售后类型内容模板列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取售后类型内容模板列表失败'
      });
    }
  }

  /**
   * 根据ID获取售后类型内容模板详情
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.afterSalesTypeTemplateService.getById(parseInt(id));
      
      if (result.code === 404) {
        return res.status(404).json(result);
      }
      
      res.json(result);
    } catch (error) {
      console.error('获取售后类型内容模板详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取售后类型内容模板详情失败'
      });
    }
  }
}

module.exports = AfterSalesTypeTemplateController;
