const InquiryService = require('../services/InquiryService');
const InquiryOrderService = require('../services/InquiryOrderService');
const QuotationExcelService = require('../services/QuotationExcelService');

/**
 * 询价控制器
 */
class InquiryController {
  constructor(prisma) {
    this.inquiryService = new InquiryService();
    this.inquiryOrderService = new InquiryOrderService(prisma);
    this.prisma = prisma;
  }

  /**
   * 解析Excel文件
   */
  async parseExcel(req, res) {
    try {
      const { filePath } = req.body;

      if (!filePath) {
        return res.status(400).json({
          code: 400,
          message: 'Excel文件路径不能为空'
        });
      }

      const result = await this.inquiryService.parseExcel(filePath);
      res.json(result);
    } catch (error) {
      console.error('解析Excel文件失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '解析Excel文件失败'
      });
    }
  }

  /**
   * 保存文件上传记录
   */
  async saveFileUploadRecord(req, res) {
    try {
      const {
        fileName,
        fileUrl,
        fileSize,
        fileType,
        fileExtension,
        uploadStatus = 1,
        parseStatus = 0,
        importStatus = 0,
        totalRows = 0,
        successRows = 0,
        failedRows = 0,
        errorMessage,
        resultFileUrl
      } = req.body;

      // 验证必填字段
      if (!fileName || !fileUrl) {
        return res.status(400).json({
          code: 400,
          message: '文件名和文件URL不能为空'
        });
      }

      // 获取当前用户ID（从认证中间件获取）
      const uploadedBy = req.user ? req.user.id : null;
      const now = Date.now();

      // 保存文件上传记录
      const record = await this.prisma.inquiry_file_uploads.create({
        data: {
          file_name: fileName,
          file_url: fileUrl,
          file_size: BigInt(fileSize || 0),
          file_type: fileType,
          file_extension: fileExtension,
          upload_status: uploadStatus,
          parse_status: parseStatus,
          import_status: importStatus,
          total_rows: totalRows,
          success_rows: successRows,
          failed_rows: failedRows,
          error_message: errorMessage,
          result_file_url: resultFileUrl,
          uploaded_by: uploadedBy ? BigInt(uploadedBy) : null,
          created_at: BigInt(now),
          updated_at: BigInt(now)
        }
      });

      res.json({
        code: 200,
        message: '文件上传记录保存成功',
        data: {
          id: record.id.toString(),
          fileName: record.file_name,
          fileUrl: record.file_url,
          uploadStatus: record.upload_status,
          parseStatus: record.parse_status,
          importStatus: record.import_status,
          createdAt: record.created_at.toString()
        }
      });
    } catch (error) {
      console.error('保存文件上传记录失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '保存文件上传记录失败'
      });
    }
  }

  /**
   * 获取文件上传记录列表
   */
  async getFileUploadRecords(req, res) {
    try {
      const { page = 1, pageSize = 10, uploadedBy } = req.query;
      const skip = (page - 1) * pageSize;

      // 构建查询条件
      const where = {
        deleted_at: null
      };

      if (uploadedBy) {
        where.uploaded_by = BigInt(uploadedBy);
      }

      // 查询记录
      const [records, total] = await Promise.all([
        this.prisma.inquiry_file_uploads.findMany({
          where,
          orderBy: {
            created_at: 'desc'
          },
          skip: parseInt(skip),
          take: parseInt(pageSize)
        }),
        this.prisma.inquiry_file_uploads.count({ where })
      ]);

      // 格式化返回数据
      const list = records.map(record => ({
        id: record.id.toString(),
        fileName: record.file_name,
        fileUrl: record.file_url,
        fileSize: record.file_size.toString(),
        fileType: record.file_type,
        fileExtension: record.file_extension,
        uploadStatus: record.upload_status,
        parseStatus: record.parse_status,
        importStatus: record.import_status,
        totalRows: record.total_rows,
        successRows: record.success_rows,
        failedRows: record.failed_rows,
        errorMessage: record.error_message,
        resultFileUrl: record.result_file_url,
        uploadedBy: record.uploaded_by ? record.uploaded_by.toString() : null,
        createdAt: record.created_at.toString(),
        updatedAt: record.updated_at.toString()
      }));

      res.json({
        code: 200,
        message: '获取文件上传记录成功',
        data: {
          list,
          pagination: {
            current: parseInt(page),
            pageSize: parseInt(pageSize),
            total
          }
        }
      });
    } catch (error) {
      console.error('获取文件上传记录失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取文件上传记录失败'
      });
    }
  }

  /**
   * 更新文件上传记录状态
   */
  async updateFileUploadRecord(req, res) {
    try {
      const { id } = req.params;
      const {
        parseStatus,
        importStatus,
        totalRows,
        successRows,
        failedRows,
        errorMessage,
        resultFileUrl
      } = req.body;

      if (!id) {
        return res.status(400).json({
          code: 400,
          message: '记录ID不能为空'
        });
      }

      // 构建更新数据
      const updateData = {
        updated_at: BigInt(Date.now())
      };

      if (parseStatus !== undefined) updateData.parse_status = parseStatus;
      if (importStatus !== undefined) updateData.import_status = importStatus;
      if (totalRows !== undefined) updateData.total_rows = totalRows;
      if (successRows !== undefined) updateData.success_rows = successRows;
      if (failedRows !== undefined) updateData.failed_rows = failedRows;
      if (errorMessage !== undefined) updateData.error_message = errorMessage;
      if (resultFileUrl !== undefined) updateData.result_file_url = resultFileUrl;

      // 更新记录
      const record = await this.prisma.inquiry_file_uploads.update({
        where: { id: BigInt(id) },
        data: updateData
      });

      res.json({
        code: 200,
        message: '文件上传记录更新成功',
        data: {
          id: record.id.toString(),
          parseStatus: record.parse_status,
          importStatus: record.import_status,
          updatedAt: record.updated_at.toString()
        }
      });
    } catch (error) {
      console.error('更新文件上传记录失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '更新文件上传记录失败'
      });
    }
  }

  /**
   * 提交询价单
   */
  async submitInquiry(req, res) {
    try {
      const data = req.body;

      // 获取当前用户ID（从认证中间件获取）
      const userId = req.user ? req.user.id : null;

      const result = await this.inquiryOrderService.submitInquiry(data, userId);
      res.json(result);
    } catch (error) {
      console.error('提交询价单失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '提交询价单失败'
      });
    }
  }

  /**
   * 获取询价单列表
   */
  async getInquiryOrderList(req, res) {
    try {
      const params = req.query;
      const result = await this.inquiryOrderService.getInquiryList(params);
      res.json(result);
    } catch (error) {
      console.error('获取询价单列表失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取询价单列表失败'
      });
    }
  }

  /**
   * 获取询价单详情
   */
  async getInquiryOrderDetail(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({
          code: 400,
          message: '询价单ID不能为空'
        });
      }

      const result = await this.inquiryOrderService.getInquiryDetail(id);
      res.json(result);
    } catch (error) {
      console.error('获取询价单详情失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '获取询价单详情失败'
      });
    }
  }

  /**
   * 检查重复询价
   */
  async checkDuplicateInquiry(req, res) {
    try {
      const { customerId, inquiryItems } = req.body;

      if (!customerId) {
        return res.status(400).json({
          code: 400,
          message: '客户ID不能为空'
        });
      }

      if (!inquiryItems || !Array.isArray(inquiryItems) || inquiryItems.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '询价项目不能为空'
        });
      }

      const result = await this.inquiryOrderService.checkDuplicateInquiry(customerId, inquiryItems);
      res.json(result);
    } catch (error) {
      console.error('检查重复询价失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '检查重复询价失败'
      });
    }
  }

  /**
   * 生成报价单Excel文件
   */
  async generateQuotationExcel(req, res) {
    try {
      const data = req.body;

      // 使用服务生成Excel文件
      const workbook = QuotationExcelService.createQuotationExcel(data);

      // 生成文件名
      const fileName = `报价单_${data.documentNumber || new Date().toISOString().slice(0, 10)}.xlsx`;

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

      // 写入Excel文件并发送
      const XLSX = require('xlsx-js-style');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      res.send(buffer);

    } catch (error) {
      console.error('生成报价单Excel文件时出错:', error);
      res.status(500).json({
        code: 500,
        message: '生成报价单Excel文件失败: ' + error.message
      });
    }
  }
  
  /**
   * 批量确认询价
   * @swagger
   * /api/csm/inquiry/confirm:
   *   post:
   *     summary: 批量确认询价
   *     description: 批量更新询价详情的询价状态
   *     tags: [询价管理]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               inquiryDetails:
   *                 type: array
   *                 description: 询价详情列表
   *                 items:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: 询价详情ID
   *                     inquiry_status:
   *                       type: integer
   *                       description: 询价状态
   *     responses:
   *       200:
   *         description: 询价确认成功
   *       207:
   *         description: 部分询价确认成功
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器内部错误
   */
  async confirmInquiries(req, res) {
    try {
      const { inquiryDetails } = req.body;
      
      if (!inquiryDetails || !Array.isArray(inquiryDetails) || inquiryDetails.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '询价详情列表不能为空'
        });
      }

      // 获取当前用户ID（从认证中间件获取）
      const userId = req.user ? req.user.id : null;

      const result = await this.inquiryOrderService.confirmInquiries(inquiryDetails, userId);
      res.json(result);
    } catch (error) {
      console.error('确认询价失败:', error);
      res.status(500).json({
        code: 500,
        message: error.message || '确认询价失败'
      });
    }
  }
}

module.exports = InquiryController;
