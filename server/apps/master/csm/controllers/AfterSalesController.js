/**
 * 售后申请控制器
 * 负责处理售后申请相关的请求和响应
 */
const BaseController = require('../../../../core/controllers/BaseController');
const AfterSalesService = require('../services/AfterSalesService');

class AfterSalesController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.afterSalesService = new AfterSalesService(prisma);
  }

  /**
   * 创建售后申请
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async createAfterSalesApplication(req, res) {
    try {
      console.log('收到售后申请请求:', req.body);
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      // 验证必填字段
      const {
        orderNumber,
        afterSalesType,
        afterSalesContent,
        customerDemand,
        afterSalesStaff,
        products = [],
        attachments = []
      } = req.body;

      if (!orderNumber) {
        return this.fail(res, '订单号不能为空', 400);
      }
      if (!afterSalesType) {
        return this.fail(res, '售后类型不能为空', 400);
      }
      if (!afterSalesContent) {
        return this.fail(res, '售后内容不能为空', 400);
      }
      if (!customerDemand) {
        return this.fail(res, '客户诉求不能为空', 400);
      }
      if (!afterSalesStaff) {
        return this.fail(res, '售后员不能为空', 400);
      }

      // 调用服务创建售后申请
      const result = await this.afterSalesService.createAfterSalesApplication(req.body, currentUser);

      // 返回成功响应
      this.success(res, result.data, result.message, 200);
    } catch (error) {
      // 记录错误日志
      console.error('创建售后申请失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }
      if (error.message.includes('已经申请过')) {
        return this.fail(res, error.message, 409);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取售后申请列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getAfterSalesApplications(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;

      // 构建查询过滤条件
      const filters = {};

      // 调试日志 - 打印所有查询参数
      console.log('售后申请列表查询参数:', req.query);

      // 基础过滤条件 (保持原有逻辑)
      if (req.query.afterSalesNumber) {
        filters.after_sales_number = {
          contains: req.query.afterSalesNumber
        };
      }

      if (req.query.orderNumber) {
        filters.original_order_number = {
          contains: req.query.orderNumber
        };
      }

      if (req.query.afterSalesStatus !== undefined && req.query.afterSalesStatus !== '') {
        filters.after_sales_status = parseInt(req.query.afterSalesStatus);
      }

      if (req.query.orderSource) {
        filters.order_source = req.query.orderSource;
      }

      // 新增搜索条件
      // 1. 跟单员搜索
      if (req.query.follower) {
        filters.follower = req.query.follower;
      }

      // 2. 销售部门搜索 (优先使用部门名称)
      if (req.query.dept_name) {
        filters.dept_name = req.query.dept_name;
      } else if (req.query.dept_id) {
        filters.dept_id = req.query.dept_id;
      }

      // 3. 售后类型搜索
      if (req.query.after_sales_type) {
        filters.after_sales_type = req.query.after_sales_type;
      }

      // 4. 客服是否退货搜索
      if (req.query.is_return !== undefined && req.query.is_return !== '') {
        filters.is_return = req.query.is_return;
      }

      // 5. 售后员搜索 (通过用户名)
      if (req.query.after_sales_staff_username) {
        filters.after_sales_staff_username = req.query.after_sales_staff_username;
      }

      // 6. 提出人搜索 (通过用户名)
      if (req.query.proposer_username) {
        filters.proposer_username = req.query.proposer_username;
      }

      // 7. 采购员搜索 (通过用户名)
      if (req.query.purchaser_username) {
        filters.purchaser_username = req.query.purchaser_username;
      }

      // 8. 供应商名称搜索
      if (req.query.supplier_name) {
        filters.supplier_name = req.query.supplier_name;
      }

      // 9. 商品名称搜索
      if (req.query.product_name) {
        filters.product_name = req.query.product_name;
      }

      // 10. 商品编码搜索
      if (req.query.product_code) {
        filters.product_code = req.query.product_code;
      }

      // 11. 实际收货地址搜索
      if (req.query.delivery_address) {
        filters.delivery_address = req.query.delivery_address;
      }

      // 12. 寄出单号搜索
      if (req.query.shipping_number) {
        filters.shipping_number = req.query.shipping_number;
      }

      // 13. 售后时间范围搜索 (支持时间戳和ISO字符串)
      if (req.query.created_at_start) {
        filters.created_at_start = req.query.created_at_start;
      }
      if (req.query.created_at_end) {
        filters.created_at_end = req.query.created_at_end;
      }

      // 兼容旧的时间范围参数 (时间戳格式)
      if (req.query.startTime && req.query.endTime) {
        filters.created_at_start = req.query.startTime;
        filters.created_at_end = req.query.endTime;
      }

      // 兼容旧的参数名 (保持向后兼容)
      if (req.query.supplierName) {
        filters.supplier_name = req.query.supplierName;
      }

      if (req.query.afterSalesStaff) {
        filters.after_sales_staff_username = req.query.afterSalesStaff;
      }

      if (req.query.purchaser) {
        filters.purchaser_username = req.query.purchaser;
      }

      // 回复时间范围参数
      if (req.query.reply_start_time) {
        filters.reply_start_time = req.query.reply_start_time;
      }

      if (req.query.reply_end_time) {
        filters.reply_end_time = req.query.reply_end_time;
      }

      // 调用服务获取售后申请列表
      const result = await this.afterSalesService.getAfterSalesApplications(filters, page, pageSize);

      // 返回成功响应
      this.success(res, {
        items: result.items,
        pageInfo: {
          total: result.total,
          currentPage: result.page,
          pageSize: result.pageSize,
          totalPage: result.totalPages
        }
      }, '获取售后申请列表成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取售后申请列表失败', error);

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取售后申请详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getAfterSalesApplicationDetail(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }

      // 调用服务获取售后申请详情
      const result = await this.afterSalesService.getAfterSalesApplicationById(id);

      // 返回成功响应
      this.success(res, result, '获取售后申请详情成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取售后申请详情失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新售后申请状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updateAfterSalesStatus(req, res) {
    try {
      const { id } = req.params;
      const { status, progress, remark } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }

      // 调用服务更新售后申请状态
      const result = await this.afterSalesService.updateAfterSalesStatus(id, {
        status,
        progress,
        remark
      }, currentUser);

      // 返回成功响应
      this.success(res, result, '更新售后申请状态成功');
    } catch (error) {
      // 记录错误日志
      console.error('更新售后申请状态失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 添加售后回复
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async addAfterSalesReply(req, res) {
    try {
      const { id } = req.params;
      const {
        replyContent,
        responsibility,
        companyCostType,
        companyCostAmount,
        supplierCostType,
        supplierCostAmount,
        totalCostAmount,
        isReturn,
        attachments = []
      } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }
      if (!replyContent) {
        return this.fail(res, '回复内容不能为空', 400);
      }

      // 调用服务添加售后回复
      const result = await this.afterSalesService.addAfterSalesReply(id, {
        replyContent,
        responsibility,
        companyCostType,
        companyCostAmount,
        supplierCostType,
        supplierCostAmount,
        totalCostAmount,
        isReturn,
        attachments
      }, currentUser);

      // 返回成功响应
      this.success(res, result, '添加售后回复成功');
    } catch (error) {
      // 记录错误日志
      console.error('添加售后回复失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 添加售后备注
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async addAfterSalesRemark(req, res) {
    try {
      const { id } = req.params;
      const { remark, attachments = [] } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }
      if (!remark || !remark.trim()) {
        return this.fail(res, '备注内容不能为空', 400);
      }

      // 调用服务添加售后备注
      const result = await this.afterSalesService.addAfterSalesRemark(id, {
        remark: remark.trim(),
        attachments
      }, currentUser);

      // 返回成功响应
      this.success(res, result, '添加售后备注成功');
    } catch (error) {
      // 记录错误日志
      console.error('添加售后备注失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更换售后员
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async changeAfterSalesStaff(req, res) {
    try {
      const { id } = req.params;
      const { newStaffId, newStaffName, changeReason } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }
      if (!newStaffName) {
        return this.fail(res, '新售后员不能为空', 400);
      }

      // 调用服务更换售后员
      const result = await this.afterSalesService.changeAfterSalesStaff(id, {
        newStaffId,
        newStaffName,
        changeReason
      }, currentUser);

      // 返回成功响应
      this.success(res, result, '更换售后员成功');
    } catch (error) {
      // 记录错误日志
      console.error('更换售后员失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 单独更新售后状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updateAfterSalesStatusOnly(req, res) {
    try {
      const { id } = req.params;
      const { afterSalesStatus } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }
      if (afterSalesStatus === undefined || afterSalesStatus === null) {
        return this.fail(res, '售后状态不能为空', 400);
      }

      // 调用服务更新售后状态
      const result = await this.afterSalesService.updateAfterSalesStatusOnly(id, parseInt(afterSalesStatus), currentUser);

      // 返回成功响应
      this.success(res, result, '售后状态更新成功');
    } catch (error) {
      // 记录错误日志
      console.error('更新售后状态失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 单独更新售后进度
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updateAfterSalesProgress(req, res) {
    try {
      const { id } = req.params;
      const { afterSalesProgress } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }
      if (afterSalesProgress === undefined || afterSalesProgress === null) {
        return this.fail(res, '售后进度不能为空', 400);
      }

      // 调用服务更新售后进度
      const result = await this.afterSalesService.updateAfterSalesProgress(id, parseInt(afterSalesProgress), currentUser);

      // 返回成功响应
      this.success(res, result, '售后进度更新成功');
    } catch (error) {
      // 记录错误日志
      console.error('更新售后进度失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新退货地址
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updateReturnAddress(req, res) {
    try {
      const { id } = req.params;
      const { returnAddress } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }

      // 调用服务更新退货地址
      const result = await this.afterSalesService.updateReturnAddress(id, returnAddress || '', currentUser);

      // 返回成功响应
      this.success(res, result, '退货地址更新成功');
    } catch (error) {
      // 记录错误日志
      console.error('更新退货地址失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新退货地址
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updateReturnNumber(req, res) {
    try {
      const { id } = req.params;
      const { returnNumber } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }

      // 调用服务更新退货地址
      const result = await this.afterSalesService.updateReturnNumber(id, returnNumber || '', currentUser);

      // 返回成功响应
      this.success(res, result, '退货单号更新成功');
    } catch (error) {
      // 记录错误日志
      console.error('更新退货单号失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新ERP系统状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async updateErpStatus(req, res) {
    try {
      const { id } = req.params;
      const { erpStatus } = req.body;
      const currentUser = req.user || { id: 1, username: 'system', nickname: '系统用户' };

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }
      if (erpStatus === undefined || erpStatus === null) {
        return this.fail(res, 'ERP状态不能为空', 400);
      }

      // 调用服务更新ERP状态
      const result = await this.afterSalesService.updateErpStatus(id, parseInt(erpStatus), currentUser);

      // 返回成功响应
      this.success(res, result, 'ERP状态更新成功');
    } catch (error) {
      // 记录错误日志
      console.error('更新ERP状态失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取回复组件所需数据
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getReplyComponentData(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }

      // 调用服务获取回复组件数据
      const result = await this.afterSalesService.getReplyComponentData(id);

      // 返回成功响应
      this.success(res, result, '获取回复组件数据成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取回复组件数据失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取回复历史记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getReplyHistory(req, res) {
    try {
      const { id } = req.params;
      const { page = 1, pageSize = 10 } = req.query;

      if (!id) {
        return this.fail(res, '售后申请ID不能为空', 400);
      }

      // 调用服务获取回复历史记录
      const result = await this.afterSalesService.getReplyHistory(id, {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      });

      // 返回成功响应
      this.success(res, result, '获取回复历史记录成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取回复历史记录失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 根据订单ID获取回复历史记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getReplyHistoryByOrderId(req, res) {
    try {
      const { order_id } = req.query;
      const { page = 1, pageSize = 10 } = req.query;

      if (!order_id) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 调用服务获取回复历史记录
      const result = await this.afterSalesService.getReplyHistoryByOrderId(order_id, {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      });

      // 返回成功响应
      this.success(res, result, '获取回复历史记录成功');
    } catch (error) {
      // 记录错误日志
      console.error('根据订单ID获取回复历史记录失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }

      // 返回错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = AfterSalesController;
