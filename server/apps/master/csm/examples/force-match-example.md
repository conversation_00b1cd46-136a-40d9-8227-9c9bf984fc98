# 强制匹配模式使用示例

## 场景说明

假设我们有以下配送方式数据：

```sql
-- 通用配送方式（所有渠道都可用）
INSERT INTO csm.csm_delivery_method (id, method_name, method_code, channel_id, is_enabled) VALUES
(1, '顺丰速运', 'SF_EXPRESS', NULL, 1),
(2, '京东快递', 'JD_COURIER', NULL, 1),
(3, '圆通速递', 'YTO_EXPRESS', NULL, 1);

-- 渠道专用配送方式（只有特定渠道可用）
INSERT INTO csm.csm_delivery_method (id, method_name, method_code, channel_id, is_enabled) VALUES
(4, '京东大件物流', 'JD_LARGE', 123456, 1),
(5, '京东快运', 'JD_EXPRESS', 123456, 1),
(6, '淘宝物流', 'TAOBAO_EXPRESS', 789012, 1);
```

假设订单 `313300020216` 属于渠道ID `123456`（京东渠道）。

## API调用示例

### 1. 默认模式 (forceMatch=false 或不传参数)

**请求：**
```
GET /api/v1/master/csm/shipping/delivery-methods/313300020216
```

**响应：**
```json
{
  "code": 200,
  "message": "获取配送方式列表成功",
  "data": {
    "orderNo": "313300020216",
    "channelId": "123456",
    "forceMatch": false,
    "deliveryMethods": [
      {
        "id": 4,
        "methodName": "京东大件物流",
        "methodCode": "JD_LARGE",
        "description": "适用于大件商品的物流配送",
        "isChannelSpecific": true
      },
      {
        "id": 5,
        "methodName": "京东快运",
        "methodCode": "JD_EXPRESS",
        "description": "京东快运物流服务",
        "isChannelSpecific": true
      },
      {
        "id": 1,
        "methodName": "顺丰速运",
        "methodCode": "SF_EXPRESS",
        "description": "顺丰快递配送服务",
        "isChannelSpecific": false
      },
      {
        "id": 2,
        "methodName": "京东快递",
        "methodCode": "JD_COURIER",
        "description": "京东快递配送服务",
        "isChannelSpecific": false
      },
      {
        "id": 3,
        "methodName": "圆通速递",
        "methodCode": "YTO_EXPRESS",
        "description": "圆通快递配送服务",
        "isChannelSpecific": false
      }
    ]
  }
}
```

**说明：** 返回了渠道专用的配送方式（京东大件物流、京东快运）+ 所有通用配送方式

### 2. 强制匹配模式 (forceMatch=true)

**请求：**
```
GET /api/v1/master/csm/shipping/delivery-methods/313300020216?forceMatch=true
```

**响应：**
```json
{
  "code": 200,
  "message": "获取配送方式列表成功",
  "data": {
    "orderNo": "313300020216",
    "channelId": "123456",
    "forceMatch": true,
    "deliveryMethods": [
      {
        "id": 4,
        "methodName": "京东大件物流",
        "methodCode": "JD_LARGE",
        "description": "适用于大件商品的物流配送",
        "isChannelSpecific": true
      },
      {
        "id": 5,
        "methodName": "京东快运",
        "methodCode": "JD_EXPRESS",
        "description": "京东快运物流服务",
        "isChannelSpecific": true
      }
    ]
  }
}
```

**说明：** 只返回了渠道ID完全匹配的配送方式，不包含通用配送方式

## 前端使用

在H5发货页面中，用户可以通过"仅渠道专用"开关来切换模式：

- **关闭状态**：显示所有可用的配送方式（渠道专用 + 通用）
- **开启状态**：只显示该渠道专用的配送方式

这样可以让用户根据实际需求选择合适的配送方式范围。

## 使用场景

1. **默认模式**：适用于大多数情况，提供更多配送选择
2. **强制匹配模式**：适用于以下场景：
   - 渠道有特殊的物流合作协议
   - 需要使用渠道专属的物流服务
   - 避免使用通用物流可能带来的问题
   - 确保物流服务与渠道政策一致
