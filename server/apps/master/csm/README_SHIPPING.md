# 发货管理模块

## 概述

发货管理模块提供了根据订单号获取配送方式和创建发货信息的功能。该模块支持按渠道区分配送方式，并提供了完整的发货信息管理。

## 数据库结构

### 配送方式表 (csm_delivery_method)

```sql
CREATE TABLE csm.csm_delivery_method (
    id SERIAL PRIMARY KEY,
    method_name VARCHAR(100) NOT NULL,
    method_code VARCHAR(50) NOT NULL,
    description TEXT,
    channel_id BIGINT NULL,
    is_enabled INT NOT NULL DEFAULT 1,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP,
    
    CONSTRAINT uniq_method_code_channel UNIQUE (method_code, channel_id),
    CONSTRAINT fk_delivery_channel FOREIGN KEY (channel_id) 
        REFERENCES channel(id) ON DELETE SET NULL
);
```

### 发货信息表 (csm_shipping_info)

```sql
CREATE TABLE csm.csm_shipping_info (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL,
    order_type INT NOT NULL,
    delivery_method_id INT NOT NULL,
    express_company_name VARCHAR(100) NOT NULL,
    express_company_id VARCHAR(50) NOT NULL,
    tracking_no VARCHAR(50) NOT NULL,
    shipping_location VARCHAR(200) NOT NULL,
    attachment VARCHAR(500),
    delivery_list_photo VARCHAR(500),
    package_photo VARCHAR(500),
    business_contact VARCHAR(50) NOT NULL,
    business_phone VARCHAR(20) NOT NULL,
    remarks TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP,
    delete_time TIMESTAMP,
    
    CONSTRAINT fk_delivery_method FOREIGN KEY (delivery_method_id) 
        REFERENCES csm_delivery_method(id)
);
```

## API 接口

**重要说明：** 所有发货相关的API接口都不需要token验证，可以公开访问。这是为了支持H5发货页面的无障碍访问。

**配置说明：** 发货接口的公开访问通过以下配置实现：
1. 在 `server/config/auth.config.js` 中添加了公开路径：`'/api/v1/master/csm/shipping/*'`
2. 这样所有以 `/api/v1/master/csm/shipping/` 开头的接口都会跳过JWT认证

### 1. 根据订单号获取配送方式

**接口地址：** `GET /api/v1/master/csm/shipping/delivery-methods/{orderNo}`
**认证要求：** 无需认证，公开访问

**请求参数：**
- `orderNo` (路径参数): 订单号
- `forceMatch` (查询参数，可选): 是否强制匹配渠道，true/false，默认false

**参数说明：**
- `forceMatch=false` (默认模式): 返回渠道匹配的配送方式 + 通用配送方式（channel_id为null）
- `forceMatch=true` (强制匹配模式): 只返回渠道ID完全匹配的配送方式

**响应格式：**
```json
{
  "code": 200,
  "message": "获取配送方式列表成功",
  "data": {
    "orderNo": "313300020216",
    "channelId": "123456789",
    "forceMatch": false,
    "deliveryMethods": [
      {
        "id": 1,
        "methodName": "京东快递",
        "methodCode": "JD_COURIER",
        "description": "京东快递配送服务",
        "isChannelSpecific": false
      }
    ]
  }
}
```

### 2. 创建发货信息

**接口地址：** `POST /api/v1/master/csm/shipping/shipping-info`
**认证要求：** 无需认证，公开访问

**请求参数：**
```json
{
  "orderNo": "313300020216",
  "orderType": 1,
  "deliveryMethodId": 1,
  "expressCompanyName": "顺丰速运",
  "expressCompanyId": "SF",
  "trackingNo": "SF1234567890",
  "shippingLocation": "广东省深圳市南山区科技园",
  "businessContact": "张三",
  "businessPhone": "***********",
  "remarks": "测试发货信息"
}
```

**响应格式：**
```json
{
  "code": 200,
  "message": "创建发货信息成功",
  "data": {
    "id": "1",
    "orderNo": "313300020216",
    "orderType": 1,
    "deliveryMethod": {
      "id": 1,
      "name": "京东快递",
      "code": "JD_COURIER"
    },
    "trackingNo": "SF1234567890",
    "createTime": "2024-01-01T00:00:00.000Z"
  }
}
```

## 前端集成

### H5发货页面

发货页面位于 `pages/h5/SupplyChain/SendGoods.vue`，支持：

1. 根据URL参数中的订单ID自动获取配送方式
2. 动态显示该订单渠道对应的配送方式列表
3. **强制匹配模式切换**：用户可以通过开关选择是否只显示渠道专用的配送方式
4. 表单验证和数据提交
5. 错误处理和用户反馈

### 使用方式

访问路径：`/h5/SupplyChain/SendGoods/:orderid`

例如：`/h5/SupplyChain/SendGoods/313300020216`

## 初始化数据

运行以下SQL脚本来初始化基础的配送方式数据：

```bash
psql -d your_database -f server/apps/master/csm/sql/init_delivery_methods.sql
```

## 测试

使用测试脚本验证API功能：

```bash
node test/shipping-api-test.js
```

注意：发货接口无需认证，测试脚本可以直接运行。

## 业务逻辑

1. **配送方式获取逻辑：**
   - 首先从采购订单表查找订单对应的渠道ID
   - 如果找不到，则从原始订单表查找
   - 根据强制匹配模式决定查询策略：
     - **默认模式** (`forceMatch=false`): 查询渠道专用配送方式 + 通用配送方式（channel_id为null）
     - **强制匹配模式** (`forceMatch=true`): 只查询渠道ID完全匹配的配送方式
   - 渠道专用的配送方式优先显示

2. **订单类型：**
   - 1: 采购订单
   - 2: 拆分商品

3. **配送方式状态：**
   - 1: 启用
   - 0: 禁用

## 注意事项

1. 确保数据库中已创建相关表结构
2. 配送方式需要预先配置
3. 订单必须存在对应的渠道信息
4. 前端页面需要通过URL参数传递订单号
5. **重要：** 发货接口已配置为公开访问，无需token验证
6. 如果遇到401错误，请检查 `server/config/auth.config.js` 中的公开路径配置
7. 服务器重启后配置才会生效
