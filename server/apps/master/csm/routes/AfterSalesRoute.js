/**
 * 售后申请路由
 * 处理售后申请相关的路由配置
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../../core/database/prisma');
const AfterSalesController = require('../controllers/AfterSalesController');
const RouterConfig = require('../../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new AfterSalesController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * components:
 *   schemas:
 *     AfterSalesApplication:
 *       type: object
 *       required:
 *         - orderNumber
 *         - afterSalesType
 *         - afterSalesContent
 *         - customerDemand
 *         - afterSalesStaff
 *       properties:
 *         orderNumber:
 *           type: string
 *           description: 订单号
 *           example: "319444034949"
 *         afterSalesType:
 *           type: string
 *           description: 售后类型
 *           enum: [退货, 换货, 维修, 退款]
 *           example: "退货"
 *         afterSalesContent:
 *           type: string
 *           description: 售后内容描述
 *           example: "产品存在质量缺陷，客户要求退换货"
 *         customerDemand:
 *           type: string
 *           description: 客户诉求
 *           example: "要求全额退款并赔偿损失"
 *         purchaser:
 *           type: string
 *           description: 采购员姓名
 *           example: "张三"
 *         purchaser_id:
 *           type: string
 *           description: 采购员ID
 *           example: "194273034054537216"
 *         afterSalesStaff:
 *           type: string
 *           description: 售后员姓名
 *           example: "李四"
 *         after_sales_staff_id:
 *           type: string
 *           description: 售后员ID
 *           example: "194273190648877056"
 *         products:
 *           type: array
 *           description: 商品列表
 *           items:
 *             type: object
 *             properties:
 *               productCode:
 *                 type: string
 *                 description: 商品编码
 *               productName:
 *                 type: string
 *                 description: 商品名称
 *               quantity:
 *                 type: integer
 *                 description: 数量
 *               isReturn:
 *                 type: boolean
 *                 description: 是否退货
 *         attachments:
 *           type: array
 *           description: 附件列表
 *           items:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 文件名
 *               url:
 *                 type: string
 *                 description: 文件URL
 *               size:
 *                 type: integer
 *                 description: 文件大小
 *               type:
 *                 type: string
 *                 description: 文件类型
 *     AfterSalesApplicationResponse:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 售后申请ID
 *         afterSalesNumber:
 *           type: string
 *           description: 售后编号
 *         orderNumber:
 *           type: string
 *           description: 订单号
 *         afterSalesType:
 *           type: string
 *           description: 售后类型
 *         afterSalesStatus:
 *           type: integer
 *           description: 售后状态
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 */

/**
 * @swagger
 * /api/v1/master/csm/after-sales:
 *   post:
 *     summary: 创建售后申请
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AfterSalesApplication'
 *     responses:
 *       201:
 *         description: 售后申请创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "售后申请创建成功"
 *                 data:
 *                   $ref: '#/components/schemas/AfterSalesApplicationResponse'
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 订单不存在
 *       409:
 *         description: 订单已申请过售后
 */
protectedRouter.post('/', (req, res) => controller.createAfterSalesApplication(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales:
 *   get:
 *     summary: 获取售后申请列表
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: afterSalesNumber
 *         schema:
 *           type: string
 *         description: 售后编号
 *       - in: query
 *         name: orderNumber
 *         schema:
 *           type: string
 *         description: 订单号
 *       - in: query
 *         name: afterSalesType
 *         schema:
 *           type: string
 *         description: 售后类型
 *       - in: query
 *         name: afterSalesStatus
 *         schema:
 *           type: integer
 *         description: 售后状态
 *       - in: query
 *         name: orderSource
 *         schema:
 *           type: string
 *         description: 订单来源
 *       - in: query
 *         name: supplierName
 *         schema:
 *           type: string
 *         description: 供应商名称
 *       - in: query
 *         name: afterSalesStaff
 *         schema:
 *           type: string
 *         description: 售后员
 *       - in: query
 *         name: purchaser
 *         schema:
 *           type: string
 *         description: 采购员
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: integer
 *         description: 开始时间戳
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: integer
 *         description: 结束时间戳
 *     responses:
 *       200:
 *         description: 获取售后申请列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取售后申请列表成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/AfterSalesApplicationResponse'
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         currentPage:
 *                           type: integer
 *                         pageSize:
 *                           type: integer
 *                         totalPage:
 *                           type: integer
 */
protectedRouter.get('/', (req, res) => controller.getAfterSalesApplications(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}:
 *   get:
 *     summary: 获取售后申请详情
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     responses:
 *       200:
 *         description: 获取售后申请详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取售后申请详情成功"
 *                 data:
 *                   $ref: '#/components/schemas/AfterSalesApplicationResponse'
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.get('/:id', (req, res) => controller.getAfterSalesApplicationDetail(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/status:
 *   put:
 *     summary: 更新售后申请状态
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: integer
 *                 description: 售后状态
 *                 enum: [0, 1, 2, 3]
 *               progress:
 *                 type: string
 *                 description: 售后进度
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 更新售后申请状态成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.put('/:id/status', (req, res) => controller.updateAfterSalesStatus(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/reply:
 *   post:
 *     summary: 添加售后回复
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - replyContent
 *             properties:
 *               replyContent:
 *                 type: string
 *                 description: 回复内容
 *               attachments:
 *                 type: array
 *                 description: 回复附件
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     url:
 *                       type: string
 *                     size:
 *                       type: integer
 *                     type:
 *                       type: string
 *     responses:
 *       200:
 *         description: 添加售后回复成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.post('/:id/reply', (req, res) => controller.addAfterSalesReply(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/remark:
 *   post:
 *     summary: 添加售后备注
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - remark
 *             properties:
 *               remark:
 *                 type: string
 *                 description: 备注内容
 *               attachments:
 *                 type: array
 *                 description: 备注附件
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     url:
 *                       type: string
 *                     size:
 *                       type: integer
 *                     type:
 *                       type: string
 *     responses:
 *       200:
 *         description: 添加售后备注成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.post('/:id/remark', (req, res) => controller.addAfterSalesRemark(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/status-only:
 *   put:
 *     summary: 单独更新售后状态
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - afterSalesStatus
 *             properties:
 *               afterSalesStatus:
 *                 type: integer
 *                 description: 售后状态 (0=待处理, 1=处理中, 2=已完成)
 *                 enum: [0, 1, 2]
 *     responses:
 *       200:
 *         description: 售后状态更新成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.put('/:id/status-only', (req, res) => controller.updateAfterSalesStatusOnly(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/progress:
 *   put:
 *     summary: 单独更新售后进度
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - afterSalesProgress
 *             properties:
 *               afterSalesProgress:
 *                 type: integer
 *                 description: 售后进度 (0-10对应不同进度状态)
 *                 minimum: 0
 *                 maximum: 10
 *     responses:
 *       200:
 *         description: 售后进度更新成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.put('/:id/progress', (req, res) => controller.updateAfterSalesProgress(req, res));


protectedRouter.put('/:id/return-address', (req, res) => controller.updateReturnAddress(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/return-number:
 *   put:
 *     summary: 更新退货单号
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               returnNumber:
 *                 type: string
 *                 description: 退货单号
 *     responses:
 *       200:
 *         description: 退货单号更新成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.put('/:id/return-number', (req, res) => controller.updateReturnNumber(req, res));
/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/erp-status:
 *   put:
 *     summary: 更新ERP系统状态
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - erpStatus
 *             properties:
 *               erpStatus:
 *                 type: integer
 *                 description: ERP状态 (0=全部, 1=完成erp系统操作, 2=已操作采购退料, 3=已下推费用单, 4=其他)
 *                 enum: [0, 1, 2, 3, 4]
 *     responses:
 *       200:
 *         description: ERP状态更新成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.put('/:id/erp-status', (req, res) => controller.updateErpStatus(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/reply-component-data:
 *   get:
 *     summary: 获取回复组件所需数据
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     responses:
 *       200:
 *         description: 获取回复组件数据成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取回复组件数据成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 售后申请ID
 *                     afterSalesNumber:
 *                       type: string
 *                       description: 售后编号
 *                     originalOrderNumber:
 *                       type: string
 *                       description: 原始订单号
 *                     afterSalesType:
 *                       type: string
 *                       description: 售后类型
 *                     afterSalesContent:
 *                       type: string
 *                       description: 售后内容
 *                     customerDemand:
 *                       type: string
 *                       description: 客户诉求
 *                     applicantName:
 *                       type: string
 *                       description: 申请人姓名
 *                     purchaserName:
 *                       type: string
 *                       description: 采购员姓名
 *                     afterSalesStaffName:
 *                       type: string
 *                       description: 售后员姓名
 *                     followerName:
 *                       type: string
 *                       description: 跟单员姓名
 *                     products:
 *                       type: array
 *                       description: 商品列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           productName:
 *                             type: string
 *                           productCode:
 *                             type: string
 *                           quantity:
 *                             type: integer
 *                     attachments:
 *                       type: array
 *                       description: 附件列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           fileName:
 *                             type: string
 *                           fileUrl:
 *                             type: string
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.get('/:id/reply-component-data', (req, res) => controller.getReplyComponentData(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/reply-history:
 *   get:
 *     summary: 获取回复历史记录
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取回复历史记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取回复历史记录成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 回复历史记录列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 回复记录ID
 *                           modifyTime:
 *                             type: string
 *                             description: 修改时间
 *                           modifier:
 *                             type: string
 *                             description: 修改人
 *                           replyContent:
 *                             type: string
 *                             description: 回复内容
 *                           responsibility:
 *                             type: string
 *                             description: 责任归属
 *                           companyAmount:
 *                             type: string
 *                             description: 公司承担金额
 *                           supplierAmount:
 *                             type: string
 *                             description: 供应商承担金额
 *                           totalAmount:
 *                             type: string
 *                             description: 总费用
 *                           isReturn:
 *                             type: string
 *                             description: 是否退货
 *                           replyAttachments:
 *                             type: array
 *                             description: 回复附件
 *                     total:
 *                       type: integer
 *                       description: 总记录数
 *                     page:
 *                       type: integer
 *                       description: 当前页码
 *                     pageSize:
 *                       type: integer
 *                       description: 每页数量
 *                     totalPages:
 *                       type: integer
 *                       description: 总页数
 *       404:
 *         description: 售后申请不存在
 */
/**
 * @swagger
 * /api/v1/master/csm/after-sales/order/reply-history:
 *   get:
 *     summary: 根据订单ID获取回复历史记录
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: order_id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取回复历史记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取回复历史记录成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       description: 回复历史记录列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 回复历史记录ID
 *                           afterSalesNumber:
 *                             type: string
 *                             description: 售后编号
 *                           originalOrderId:
 *                             type: string
 *                             description: 原始订单ID
 *                           originalOrderNumber:
 *                             type: string
 *                             description: 原始订单号
 *                           replyContent:
 *                             type: string
 *                             description: 回复内容
 *                           replierName:
 *                             type: string
 *                             description: 回复人姓名
 *                           createdAt:
 *                             type: string
 *                             description: 创建时间
 *                           modifyTime:
 *                             type: string
 *                             description: 修改时间
 *                           modifier:
 *                             type: string
 *                             description: 修改人
 *                           companyAmount:
 *                             type: string
 *                             description: 我司承担费用
 *                           supplierAmount:
 *                             type: string
 *                             description: 供应商承担费用
 *                           totalAmount:
 *                             type: string
 *                             description: 总费用
 *                           isReturn:
 *                             type: string
 *                             description: 是否退货
 *                     total:
 *                       type: integer
 *                       description: 总记录数
 *                     page:
 *                       type: integer
 *                       description: 当前页码
 *                     pageSize:
 *                       type: integer
 *                       description: 每页数量
 *                     totalPages:
 *                       type: integer
 *                       description: 总页数
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 订单不存在或无售后申请
 */
protectedRouter.get('/order/reply-history', (req, res) => controller.getReplyHistoryByOrderId(req, res));

protectedRouter.get('/:id/reply-history', (req, res) => controller.getReplyHistory(req, res));

/**
 * @swagger
 * /api/v1/master/csm/after-sales/{id}/change-staff:
 *   put:
 *     summary: 更换售后员
 *     tags: [售后申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 售后申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - afterSalesStaffName
 *             properties:
 *               afterSalesStaffId:
 *                 type: string
 *                 description: 新售后员ID
 *               afterSalesStaffName:
 *                 type: string
 *                 description: 新售后员姓名
 *               reason:
 *                 type: string
 *                 description: 更换原因
 *     responses:
 *       200:
 *         description: 更换售后员成功
 *       404:
 *         description: 售后申请不存在
 */
protectedRouter.put('/:id/change-staff', (req, res) => controller.changeAfterSalesStaff(req, res));

module.exports = router;
