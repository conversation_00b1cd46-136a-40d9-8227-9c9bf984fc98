const express = require('express');
const InquiryController = require('../controllers/InquiryController');
const authMiddleware = require('../../../../core/middleware/AuthMiddleware');

// 创建路由函数，接收 prisma 实例
function createInquiryRouter(prisma) {
  const router = express.Router();
  const inquiryController = new InquiryController(prisma);

  /**
   * 询价路由
   */

  // 解析Excel文件
  router.post('/parse-excel', authMiddleware, (req, res) => {
    inquiryController.parseExcel(req, res);
  });

  // 保存文件上传记录
  router.post('/file-upload-record', authMiddleware, (req, res) => {
    inquiryController.saveFileUploadRecord(req, res);
  });

  // 获取文件上传记录列表
  router.get('/file-upload-records', authMiddleware, (req, res) => {
    inquiryController.getFileUploadRecords(req, res);
  });

  // 更新文件上传记录状态
  router.put('/file-upload-record/:id', authMiddleware, (req, res) => {
    inquiryController.updateFileUploadRecord(req, res);
  });

  // 提交询价单
  router.post('/submit', authMiddleware, (req, res) => {
    inquiryController.submitInquiry(req, res);
  });

  // 获取询价单列表
  router.get('/orders', authMiddleware, (req, res) => {
    inquiryController.getInquiryOrderList(req, res);
  });

  // 获取询价单详情
  router.get('/orders/:id', authMiddleware, (req, res) => {
    inquiryController.getInquiryOrderDetail(req, res);
  });

  // 检查重复询价
  router.post('/check-duplicate', authMiddleware, (req, res) => {
    inquiryController.checkDuplicateInquiry(req, res);
  });

  // 生成报价单Excel文件
  router.post('/generate-quotation-excel', authMiddleware, (req, res) => {
    inquiryController.generateQuotationExcel(req, res);
  });

  // 批量确认询价
  router.post('/confirm', authMiddleware, (req, res) => {
    inquiryController.confirmInquiries(req, res);
  });

  return router;
}

module.exports = createInquiryRouter;
