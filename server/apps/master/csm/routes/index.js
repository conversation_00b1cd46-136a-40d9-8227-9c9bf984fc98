const express = require('express');
const MainProductRoute = require('./MainProductRoute');
const EnterpriseNatureRoute = require('./EnterpriseNatureRoute');
const PaymentTermRoute = require('./PaymentTermRoute');
const CooperationAgreementRoute = require('./CooperationAgreementRoute');
const SupplierRoute = require('./SupplierRoute');
const PurchaseOrderRoute = require('./PurchaseOrderRoute');
const PurchaseOrderSplitRoute = require('./PurchaseOrderSplitRoute');
const ExpenseOrderRoute = require('./ExpenseOrderRoute');
const AfterSalesRoute = require('./AfterSalesRoute');
const AfterSalesTypeTemplateRoute = require('./AfterSalesTypeTemplateRoute');
const createInquiryRouter = require('./InquiryRoute');
const createInquiryRecordRouter = require('./InquiryRecordRoute');
const commonRoute = require('./commonRoute');

/**
 * CSM (Customer Supplier Management) 供应商基本信息管理路由
 */
function createCsmRouter(prisma) {
  const router = express.Router();

  // 主营产品路由
  router.use('/main-products', MainProductRoute);

  // 企业性质路由
  router.use('/enterprise-nature', EnterpriseNatureRoute);

  // 付款条件路由
  router.use('/payment-terms', PaymentTermRoute);

  // 合作协议路由
  router.use('/cooperation-agreements', CooperationAgreementRoute);

  // 供应商路由
  router.use('/suppliers', SupplierRoute);

  // 采购订单路由
  router.use('/purchase-orders', PurchaseOrderRoute);

  // 采购订单拆分路由
  router.use('/purchase-orders', PurchaseOrderSplitRoute);

  // 费用单路由
  router.use('/expense-orders', ExpenseOrderRoute);

  // 售后申请路由
  router.use('/after-sales', AfterSalesRoute);

  // 售后类型内容模板路由
  router.use('/after-sales-type-templates', AfterSalesTypeTemplateRoute);


  // 拆分单详情路由（独立路径）
  const { prisma: prismaInstance } = require('../../../../core/database/prisma');
  const PurchaseOrderSplitController = require('../controllers/PurchaseOrderSplitController');
  const splitController = new PurchaseOrderSplitController(prismaInstance);
  const RouterConfig = require('../../../../core/routes/RouterConfig');
  const splitDetailRouter = RouterConfig.authRoute(express.Router());
  splitDetailRouter.get('/:splitId', splitController.getPurchaseOrderSplitDetail.bind(splitController));
  router.use('/purchase-order-splits', splitDetailRouter);

  // 询价路由
  router.use('/inquiry', createInquiryRouter(prisma));

  // 询价记录路由
  router.use('/inquiry-records', createInquiryRecordRouter(prisma));

  // 公共接口路由
  router.use('/common', commonRoute);

  return router;
}

module.exports = createCsmRouter;
