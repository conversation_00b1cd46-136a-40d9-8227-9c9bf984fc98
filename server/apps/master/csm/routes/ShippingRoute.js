/**
 * 发货路由
 * 处理发货相关的路由配置
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../../core/database/prisma');
const ShippingController = require('../controllers/ShippingController');
const RouterConfig = require('../../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new ShippingController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * 发货相关路由
 */

// 根据订单号获取配送方式列表
protectedRouter.get('/delivery-methods/:orderNo', (req, res) => {
  controller.getDeliveryMethodsByOrderNo(req, res);
});

// 创建发货信息
protectedRouter.post('/shipping-info', (req, res) => {
  controller.createShippingInfo(req, res);
});

module.exports = router;
