/**
 * 售后类型内容模板路由
 * 处理售后类型内容模板相关的路由配置
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../../core/database/prisma');
const AfterSalesTypeTemplateController = require('../controllers/AfterSalesTypeTemplateController');

// 创建控制器实例
const controller = new AfterSalesTypeTemplateController(prisma);

/**
 * @swagger
 * /api/v1/master/csm/after-sales-type-templates:
 *   get:
 *     summary: 获取售后类型内容模板列表
 *     description: 获取售后类型内容模板列表，支持层级结构返回
 *     tags: [售后类型内容模板]
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 状态筛选 (0-禁用, 1-启用)
 *       - in: query
 *         name: pid
 *         schema:
 *           type: integer
 *         description: 父级ID筛选
 *       - in: query
 *         name: flat
 *         schema:
 *           type: boolean
 *         description: 是否返回扁平化列表 (默认false，返回层级结构)
 *     responses:
 *       200:
 *         description: 成功获取售后类型内容模板列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取售后类型内容模板列表成功"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: 模板ID
 *                       pid:
 *                         type: integer
 *                         description: 父级ID
 *                       name:
 *                         type: string
 *                         description: 售后类型名称
 *                       template:
 *                         type: string
 *                         nullable: true
 *                         description: 模板内容
 *                       children:
 *                         type: array
 *                         description: 子分类列表（仅在层级结构时返回）
 *       500:
 *         description: 服务器内部错误
 */
router.get('/', (req, res) => {
  controller.getList(req, res);
});

/**
 * @swagger
 * /api/v1/master/csm/after-sales-type-templates/{id}:
 *   get:
 *     summary: 根据ID获取售后类型内容模板详情
 *     description: 根据ID获取售后类型内容模板详情
 *     tags: [售后类型内容模板]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 售后类型内容模板ID
 *     responses:
 *       200:
 *         description: 成功获取售后类型内容模板详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取售后类型内容模板详情成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 模板ID
 *                     pid:
 *                       type: integer
 *                       description: 父级ID
 *                     name:
 *                       type: string
 *                       description: 售后类型名称
 *                     template:
 *                       type: string
 *                       nullable: true
 *                       description: 模板内容
 *                     sort_order:
 *                       type: integer
 *                       description: 排序
 *                     status:
 *                       type: integer
 *                       description: 状态
 *       404:
 *         description: 售后类型内容模板不存在
 *       500:
 *         description: 服务器内部错误
 */
router.get('/:id', (req, res) => {
  controller.getById(req, res);
});

module.exports = router;
