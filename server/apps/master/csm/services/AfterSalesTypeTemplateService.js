/**
 * 售后类型内容模板服务
 */
class AfterSalesTypeTemplateService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取售后类型内容模板列表
   * @param {Object} params 查询参数
   * @param {number} params.status 状态筛选 (0-禁用, 1-启用)
   * @param {number} params.pid 父级ID筛选
   * @param {boolean} params.flat 是否返回扁平化列表 (默认false，返回层级结构)
   * @returns {Promise<Object>} 响应结果
   */
  async getList(params = {}) {
    try {
      let { status, pid, flat = false } = params;

      // 处理flat参数，URL查询参数会被解析为字符串
      if (flat === 'false' || flat === false) {
        flat = false;
      } else if (flat === 'true' || flat === true) {
        flat = true;
      }

      // 构建查询条件
      const where = {
        deleted_at: null // 只查询未删除的记录
      };

      // 状态筛选
      if (status !== undefined && status !== null && status !== '') {
        where.status = parseInt(status);
      }

      // 父级ID筛选
      if (pid !== undefined && pid !== null && pid !== '') {
        where.pid = parseInt(pid);
      }

      // 查询数据

      let templates;
      try {
        // 尝试原始模型名称
        templates = await this.prisma.csmAfterSalesTypeTemplate.findMany({
        where,
        orderBy: [
          { sort_order: 'asc' },
          { id: 'asc' }
        ],
          select: {
            id: true,
            pid: true,
            name: true,
            template: true,
            sort_order: true,
            status: true
          }
        });
      } catch (modelError) {
        // 如果模型不存在，尝试直接SQL查询
        try {
          const sqlResult = await this.prisma.$queryRaw`
            SELECT id, pid, name, template, sort_order, status
            FROM csm.csm_after_sales_type_template
            WHERE deleted_at IS NULL
            ORDER BY sort_order ASC, id ASC
          `;
          templates = sqlResult;
        } catch (sqlError) {
          throw new Error('无法查询售后类型模板数据: ' + sqlError.message);
        }
      }

      // 转换数据类型，确保id和pid是数字类型
      const formattedTemplates = templates.map(item => ({
        id: Number(item.id),
        pid: Number(item.pid),
        name: item.name,
        template: item.template,
        sort_order: item.sort_order,
        status: item.status
      }));

      // 如果需要扁平化列表，直接返回
      if (flat) {
        return {
          code: 200,
          message: '获取售后类型内容模板列表成功',
          data: formattedTemplates
        };
      }

      // 构建层级结构
      const treeData = this.buildTree(formattedTemplates);

      return {
        code: 200,
        message: '获取售后类型内容模板列表成功',
        data: treeData
      };
    } catch (error) {
      console.error('获取售后类型内容模板列表失败:', error);
      console.error('错误堆栈:', error.stack);
      throw new Error('获取售后类型内容模板列表失败: ' + error.message);
    }
  }

  /**
   * 根据ID获取售后类型内容模板详情
   * @param {number} id 售后类型内容模板ID
   * @returns {Promise<Object>} 响应结果
   */
  async getById(id) {
    try {
      let template;
      try {
        template = await this.prisma.csmAfterSalesTypeTemplate.findFirst({
          where: {
            id: id,
            deleted_at: null
          },
          select: {
            id: true,
            pid: true,
            name: true,
            template: true,
            sort_order: true,
            status: true,
            created_at: true,
            updated_at: true
          }
        });
      } catch (modelError) {
        // 如果模型不存在，尝试直接SQL查询
        const sqlResult = await this.prisma.$queryRaw`
          SELECT id, pid, name, template, sort_order, status, created_at, updated_at
          FROM csm.csm_after_sales_type_template
          WHERE id = ${id} AND deleted_at IS NULL
        `;
        template = sqlResult[0] || null;
      }

      if (!template) {
        return {
          code: 404,
          message: '售后类型内容模板不存在'
        };
      }

      return {
        code: 200,
        message: '获取售后类型内容模板详情成功',
        data: template
      };
    } catch (error) {
      console.error('获取售后类型内容模板详情失败:', error);
      throw new Error('获取售后类型内容模板详情失败');
    }
  }

  /**
   * 构建树形结构
   * @param {Array} data 扁平化数据
   * @param {number} parentId 父级ID
   * @returns {Array} 树形结构数据
   */
  buildTree(data, parentId = 0) {
    const result = [];

    for (const item of data) {
      if (item.pid === parentId) {
        const children = this.buildTree(data, item.id);

        // 构建树节点，只包含必要的字段
        const treeNode = {
          id: item.id,
          pid: item.pid,
          name: item.name,
          template: item.template
        };

        // 只有当存在子节点时才添加children字段
        if (children.length > 0) {
          treeNode.children = children;
        }

        result.push(treeNode);
      }
    }

    return result;
  }
}

module.exports = AfterSalesTypeTemplateService;
