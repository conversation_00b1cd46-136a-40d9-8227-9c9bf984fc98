/**
 * 询价单服务类
 */
class InquiryOrderService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 生成询价单号
   * @returns {string} 询价单号
   */
  generateInquiryNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.getTime().toString().slice(-6);
    return `INQ${year}${month}${day}${timestamp}`;
  }

  /**
   * 提交询价单
   * @param {Object} data 询价单数据
   * @param {Object} data.customerInfo 客户信息
   * @param {Array} data.inquiryItems 询价项目列表
   * @param {number} userId 当前用户ID
   * @returns {Promise<Object>} 提交结果
   */
  async submitInquiry(data, userId) {
    const { customerInfo, inquiryItems, attachments } = data;
    
    // 验证数据
    if (!customerInfo || !customerInfo.customerFullName) {
      throw new Error('客户信息不能为空');
    }
    
    if (!inquiryItems || !Array.isArray(inquiryItems) || inquiryItems.length === 0) {
      throw new Error('询价项目不能为空');
    }

    // 验证询价项目
    for (const item of inquiryItems) {
      if (!item.productName) {
        throw new Error('产品名称不能为空');
      }
      if (!item.quantity || item.quantity <= 0) {
        throw new Error('需求数量必须大于0');
      }
    }

    const now = Date.now();
    const inquiryNo = this.generateInquiryNo();

    try {
      // 使用事务处理
      const result = await this.prisma.$transaction(async (tx) => {
        // 创建询价单数据对象
        const inquiryData = {
          inquiry_no: inquiryNo,
          customer_id: customerInfo.customerFullName, // 这里存储客户ID
          total_items: inquiryItems.length,
          created_at: BigInt(now),
          updated_at: BigInt(now)
        };

        // 只添加非空字段
        if (customerInfo.customerContact) {
          inquiryData.contact_id = BigInt(customerInfo.customerContact);
        }
        if (customerInfo.customerAddress) {
          inquiryData.customer_address = customerInfo.customerAddress;
        }
        if (customerInfo.salesman) {
          inquiryData.salesman_id = BigInt(customerInfo.salesman);
        }
        if (customerInfo.inquirer) {
          inquiryData.inquirer_id = BigInt(customerInfo.inquirer);
        }
        if (customerInfo.orderPlatform) {
          inquiryData.channel_id = BigInt(customerInfo.orderPlatform);
        }
        if (userId) {
          inquiryData.created_by = BigInt(userId);
          inquiryData.updated_by = BigInt(userId);
        }
        if (attachments && Array.isArray(attachments) && attachments.length > 0) {
          inquiryData.attachments = attachments;
        }

        // 创建询价单
        const inquiryOrder = await tx.csm_inquiry_orders.create({
          data: inquiryData
        });

        // 创建询价详情
        const inquiryDetails = [];
        for (const item of inquiryItems) {
          const detail = await tx.csm_inquiry_details.create({
            data: {
              inquiry_id: inquiryOrder.id,
              inquiry_no: item.inquiryNo || inquiryNo,
              product_name: item.productName,
              brand: item.brand || undefined,
              model: item.model || undefined,
              specification: item.specification || undefined,
              reference_link: item.referenceLink || undefined,
              unit: item.unit || undefined,
              quantity: parseFloat(item.quantity),
              reference_price: item.referencePrice ? parseFloat(item.referencePrice) : undefined,
              customer_product_code: item.customerProductCode || undefined,
              inquiry_remark: item.inquiryRemark || undefined,
              quote_price: undefined,
              quote_status: 0, // 未报价
              inquiry_status: 0, // 未询价
              created_at: BigInt(now),
              updated_at: BigInt(now),
              created_by: userId ? BigInt(userId) : null,
              updated_by: userId ? BigInt(userId) : null,
              deleted_at: undefined
            }
          });
          inquiryDetails.push(detail);
        }

        return {
          inquiryOrder,
          inquiryDetails
        };
      });

      return {
        code: 200,
        message: '询价单提交成功',
        data: {
          inquiryNo: result.inquiryOrder.inquiry_no,
          inquiryId: result.inquiryOrder.id.toString(),
          totalItems: result.inquiryDetails.length
        }
      };
    } catch (error) {
      console.error('提交询价单失败:', error);
      throw new Error(`提交询价单失败: ${error.message}`);
    }
  }

  /**
   * 获取询价详情
   * @param {number} orderId 订单ID
   * @returns {Promise<Array>} 询价详情列表
   */
  async getInquiryDetailsByOrderId(orderId) {
    try {
      const details = await this.prisma.csm_inquiry_details.findMany({
        where: { inquiry_id: BigInt(orderId), deleted_at: null }
      });
      return details;
    } catch (error) {
      console.error('获取询价详情失败:', error);
      throw new Error(`获取询价详情失败: ${error.message}`);
    }
  }

  /**
   * 批量确认询价
   * @param {Array} inquiryDetails 询价详情列表，每个元素包含id和inquiry_status
   * @param {number} userId 当前用户ID
   * @returns {Promise<Object>} 确认结果
   */
  async confirmInquiries(inquiryDetails, userId) {
    try {
      if (!inquiryDetails || !Array.isArray(inquiryDetails) || inquiryDetails.length === 0) {
        throw new Error('询价详情列表不能为空');
      }

      const now = Date.now();
      const results = [];
      
      // 使用事务处理批量更新，确保要么全部成功，要么全部失败
      const updatedDetails = await this.prisma.$transaction(async (tx) => {
        // 第一步：验证所有数据的有效性，如果有任何无效数据，立即抛出异常
        for (const detail of inquiryDetails) {
          const { id, inquiry_status } = detail;
          
          if (!id) {
            throw new Error(`询价详情ID不能为空`);
          }

          if (inquiry_status === undefined || inquiry_status === null) {
            throw new Error(`询价详情ID ${id} 的询价状态不能为空`);
          }

          // 查询询价详情是否存在
          const existingDetail = await tx.csm_inquiry_details.findUnique({
            where: { id: BigInt(id) }
          });

          if (!existingDetail) {
            throw new Error(`询价详情ID ${id} 不存在`);
          }
        }
        
        // 第二步：所有验证通过后，执行批量更新
        const updatePromises = inquiryDetails.map(detail => {
          const { id, inquiry_status } = detail;
          
          return tx.csm_inquiry_details.update({
            where: { id: BigInt(id) },
            data: {
              inquiry_status: inquiry_status, // 使用quote_status字段存储询价状态
              updated_at: BigInt(now),
              updated_by: userId ? BigInt(userId) : null
            }
          });
        });
        
        // 等待所有更新完成
        return await Promise.all(updatePromises);
      });
      
      // 处理成功结果
      for (const detail of updatedDetails) {
        results.push({
          id: detail.id.toString(),
          inquiry_status: detail.inquiry_status,
          updated_at: detail.updated_at.toString()
        });
      }

      return {
        code: 200,
        message: '所有询价确认成功',
        data: {
          success: results,
          total: inquiryDetails.length,
          successCount: results.length
        }
      };
    } catch (error) {
      console.error('批量确认询价失败:', error);
      throw new Error(`批量确认询价失败: ${error.message}`);
    }
  }

  /**
   * 获取询价单列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 询价单列表
   */
  async getInquiryList(params = {}) {
    console.log('InquiryOrderService.getInquiryList 接收到的参数:', JSON.stringify(params, null, 2));

    const {
      page = 1,
      pageSize = 10,
      inquiryNo,
      customerId,
      inquiryStatus,
      startDate,
      endDate,
      startTime,
      endTime,
      // 新增筛选参数
      salesman,        // 业务员
      customer,        // 客户名称
      customerCode,    // 客户编码
      merchandiser,    // 跟单员
      inquirer         // 询价员
    } = params;

    // 确保分页参数是有效的数字
    const validPage = parseInt(page) || 1;
    const validPageSize = parseInt(pageSize) || 10;
    const skip = (validPage - 1) * validPageSize;

    console.log('分页参数处理:', {
      原始: { page, pageSize },
      处理后: { validPage, validPageSize, skip }
    });

    console.log('筛选参数处理:', {
      inquiryNo, customerId, inquiryStatus,
      salesman, customer, customerCode, merchandiser, inquirer,
      时间参数: { startDate, endDate, startTime, endTime }
    });

    // 构建查询条件
    const where = {
      deleted_at: null
    };

    if (inquiryNo) {
      where.inquiry_no = {
        contains: inquiryNo
      };
    }

    if (customerId) {
      where.customer_id = customerId;
    }

    // 时间筛选：优先使用 startDate/endDate，兼容 startTime/endTime
    const timeStart = startDate || startTime;
    const timeEnd = endDate || endTime;

    // 声明时间戳变量，确保在整个函数作用域内可用
    let processedTimeStart = null;
    let processedTimeEnd = null;

    if (timeStart && timeEnd) {
      try {
        console.log('开始处理时间参数:', { timeStart, timeEnd, typeStart: typeof timeStart, typeEnd: typeof timeEnd });

        // 处理不同格式的时间参数
        if (typeof timeStart === 'string' && (timeStart.includes('T') || timeStart.includes('-'))) {
          // ISO格式时间字符串或日期字符串，转换为毫秒时间戳
          processedTimeStart = new Date(timeStart).getTime();
          processedTimeEnd = new Date(timeEnd).getTime();
          console.log('ISO格式转换结果:', { processedTimeStart, processedTimeEnd });
        } else {
          // 数字格式，直接转换
          processedTimeStart = parseInt(timeStart);
          processedTimeEnd = parseInt(timeEnd);
          console.log('数字格式转换结果:', { processedTimeStart, processedTimeEnd });
        }

        if (isNaN(processedTimeStart) || isNaN(processedTimeEnd)) {
          console.error('时间戳参数无效:', {
            原始参数: { startDate, endDate, startTime, endTime },
            处理参数: { timeStart, timeEnd },
            转换结果: { processedTimeStart, processedTimeEnd }
          });
          throw new Error('时间戳参数格式无效');
        }

        // 验证转换后的数字是否为有效的时间戳
        if (processedTimeStart <= 0 || processedTimeEnd <= 0) {
          console.error('时间戳数值无效:', { processedTimeStart, processedTimeEnd });
          throw new Error('时间戳数值必须大于0');
        }

        console.log('时间筛选处理成功:', {
          原始参数: { startDate, endDate, startTime, endTime },
          使用参数: { timeStart, timeEnd },
          转换结果: { processedTimeStart, processedTimeEnd }
        });

        where.created_at = {
          gte: BigInt(processedTimeStart),
          lte: BigInt(processedTimeEnd)
        };
      } catch (error) {
        console.error('时间戳转换失败:', error, {
          原始参数: { startDate, endDate, startTime, endTime },
          使用参数: { timeStart, timeEnd }
        });
        throw new Error('时间戳参数处理失败: ' + error.message);
      }
    }

    try {
      // 获取总数
      const total = await this.prisma.csm_inquiry_orders.count({ where });

      // 构建SQL查询条件
      let whereClause = 'io.deleted_at IS NULL';
      const queryParams = [];
      let paramIndex = 1;

      if (inquiryNo) {
        whereClause += ` AND io.inquiry_no ILIKE $${paramIndex}`;
        queryParams.push(`%${inquiryNo}%`);
        paramIndex++;
      }

      if (customerId) {
        whereClause += ` AND io.customer_id = $${paramIndex}`;
        queryParams.push(customerId);
        paramIndex++;
      }

      // 询价状态筛选 - 现在需要通过询价详情表查询
      // 注意：inquiry_status已从订单表移至详情表，此处不再添加过滤条件
      // 如需按询价状态过滤，需要在后续的查询中处理

      // 业务员筛选（支持用户名或昵称模糊匹配）
      if (salesman) {
        whereClause += ` AND (su1.username ILIKE $${paramIndex} OR su1.nickname ILIKE $${paramIndex + 1})`;
        queryParams.push(`%${salesman}%`);
        queryParams.push(`%${salesman}%`);
        paramIndex += 2;
      }

      // 客户名称筛选
      if (customer) {
        whereClause += ` AND cc.customer_name ILIKE $${paramIndex}`;
        queryParams.push(`%${customer}%`);
        paramIndex++;
      }

      // 客户编码筛选
      if (customerCode) {
        whereClause += ` AND io.customer_id = $${paramIndex}`;
        queryParams.push(customerCode);
        paramIndex++;
      }

      // 询价员筛选（支持用户名或昵称模糊匹配）
      if (inquirer) {
        whereClause += ` AND (su2.username ILIKE $${paramIndex} OR su2.nickname ILIKE $${paramIndex + 1})`;
        queryParams.push(`%${inquirer}%`);
        queryParams.push(`%${inquirer}%`);
        paramIndex += 2;
      }

      // 跟单员筛选（目前使用业务员信息，因为数据库中没有单独的跟单员字段）
      if (merchandiser) {
        whereClause += ` AND (su1.username ILIKE $${paramIndex} OR su1.nickname ILIKE $${paramIndex + 1})`;
        queryParams.push(`%${merchandiser}%`);
        queryParams.push(`%${merchandiser}%`);
        paramIndex += 2;
      }

      if (processedTimeStart && processedTimeEnd && !isNaN(processedTimeStart) && !isNaN(processedTimeEnd)) {
        whereClause += ` AND io.created_at >= $${paramIndex} AND io.created_at <= $${paramIndex + 1}`;

        try {
          const startBigInt = BigInt(processedTimeStart);
          const endBigInt = BigInt(processedTimeEnd);

          queryParams.push(startBigInt);
          queryParams.push(endBigInt);
          paramIndex += 2;

          console.log('SQL时间筛选参数:', {
            processedTimeStart,
            processedTimeEnd,
            startBigInt: startBigInt.toString(),
            endBigInt: endBigInt.toString()
          });
        } catch (error) {
          console.error('SQL时间参数BigInt转换失败:', error, { processedTimeStart, processedTimeEnd });
          throw new Error('SQL时间参数转换失败: ' + error.message);
        }
      }

      // 获取列表数据，使用原生SQL查询以便关联多个表
      const sqlQuery = `
        SELECT
          io.id,
          io.inquiry_no,
          io.customer_id,
          io.contact_id,
          io.customer_address,
          io.salesman_id,
          io.inquirer_id,
          io.channel_id,
          io.attachments,
          io.total_items,
          io.remark,
          io.created_at,
          io.updated_at,
          io.created_by,
          io.updated_by,
          cc.customer_name,
          ccc.contact_name,
          su1.username as salesman_username,
          su1.nickname as salesman_nickname,
          su2.username as inquirer_username,
          su2.nickname as inquirer_nickname,
          ch.name as channel_name
        FROM csm.csm_inquiry_orders io
        LEFT JOIN crm.customers cc ON io.customer_id = cc.id::text
        LEFT JOIN crm.customer_contacts ccc ON io.contact_id = ccc.id
        LEFT JOIN base.system_user su1 ON io.salesman_id = su1.id
        LEFT JOIN base.system_user su2 ON io.inquirer_id = su2.id
        LEFT JOIN base.channel ch ON io.channel_id = ch.id
        WHERE ${whereClause}
        ORDER BY io.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      // 确保分页参数是有效的数字
      if (isNaN(validPageSize) || isNaN(skip)) {
        console.error('分页参数无效:', { pageSize, skip, validPageSize });
        throw new Error('分页参数格式无效');
      }

      queryParams.push(validPageSize, skip);

      console.log('SQL查询构建完成:', {
        whereClause,
        paramIndex,
        queryParamsCount: queryParams.length,
        queryParams: queryParams.map(p => typeof p === 'bigint' ? p.toString() : p)
      });

      console.log('执行SQL查询:', {
        sql: sqlQuery,
        params: queryParams.map(p => typeof p === 'bigint' ? p.toString() : p)
      });

      const items = await this.prisma.$queryRawUnsafe(sqlQuery, ...queryParams);

      // 获取询价详情数据
      const inquiryIds = items.map(item => item.id);
      const inquiryDetails = inquiryIds.length > 0 ? await this.prisma.csm_inquiry_details.findMany({
        where: {
          inquiry_id: { in: inquiryIds },
          deleted_at: null
        },
        orderBy: { created_at: 'asc' }
      }) : [];

      // 格式化返回数据
      const list = items.map(item => {
        const details = inquiryDetails.filter(detail => detail.inquiry_id === item.id);

        return {
          id: item.id.toString(),
          inquiryNo: item.inquiry_no,
          customerId: item.customer_id,
          contactId: item.contact_id ? item.contact_id.toString() : null,
          customerAddress: item.customer_address,
          salesmanId: item.salesman_id ? item.salesman_id.toString() : null,
          inquirerId: item.inquirer_id ? item.inquirer_id.toString() : null,
          channelId: item.channel_id ? item.channel_id.toString() : null,
          attachments: item.attachments,
          totalItems: item.total_items,
          remark: item.remark,
          createdAt: item.created_at.toString(),
          updatedAt: item.updated_at.toString(),
          createdBy: item.created_by ? item.created_by.toString() : null,
          updatedBy: item.updated_by ? item.updated_by.toString() : null,

          // 客户信息
          customer: {
            id: item.customer_id,
            name: item.customer_name || '未知客户'
          },

          // 联系人信息
          contact: item.contact_id ? {
            id: item.contact_id.toString(),
            name: item.contact_name || '未知联系人'
          } : null,

          // 业务员信息
          salesman: item.salesman_id ? {
            id: item.salesman_id,
            username: item.salesman_username || '未知',
            nickname: item.salesman_nickname || '未知业务员'
          } : null,

          // 询价员信息
          inquirer: item.inquirer_id ? {
            id: item.inquirer_id,
            username: item.inquirer_username || '未知',
            nickname: item.inquirer_nickname || '未知询价员'
          } : null,

          // 渠道信息
          channel: item.channel_id ? {
            id: item.channel_id,
            name: item.channel_name || '未知渠道'
          } : null,

          // 询价详情
          inquiryDetails: details.map(detail => ({
            id: detail.id.toString(),
            sequence: detail.sequence,
            productName: detail.product_name,
            brand: detail.brand,
            model: detail.model,
            specification: detail.specification,
            referenceLink: detail.reference_link,
            unit: detail.unit,
            quantity: detail.quantity.toString(),
            referencePrice: detail.reference_price ? detail.reference_price.toString() : null,
            customerProductCode: detail.customer_product_code,
            inquiryRemark: detail.inquiry_remark,
            quotePrice: detail.quote_price ? detail.quote_price.toString() : null,
            quoteStatus: detail.quote_status,
            inquiryStatus: detail.inquiry_status,
            createdAt: detail.created_at.toString(),
            updatedAt: detail.updated_at.toString()
          }))
        };
      });

      return {
        code: 200,
        message: '获取询价单列表成功',
        data: {
          items: list,
          total,
          page: validPage,
          pageSize: validPageSize,
          totalPages: Math.ceil(total / validPageSize)
        }
      };
    } catch (error) {
      console.error('获取询价单列表失败:', error);
      throw new Error(`获取询价单列表失败: ${error.message}`);
    }
  }

  /**
   * 获取询价单详情
   * @param {string} inquiryId 询价单ID
   * @returns {Promise<Object>} 询价单详情
   */
  async getInquiryDetail(inquiryId) {
    try {
      // 使用原生SQL查询以便关联多个表
      const sqlQuery = `
        SELECT
          io.id,
          io.inquiry_no,
          io.customer_id,
          io.contact_id,
          io.customer_address,
          io.salesman_id,
          io.inquirer_id,
          io.channel_id,
          io.attachments,
          io.total_items,
          io.remark,
          io.created_at,
          io.updated_at,
          io.created_by,
          io.updated_by,
          cc.customer_name,
          ccc.contact_name,
          su1.username as salesman_username,
          su1.nickname as salesman_nickname,
          su2.username as inquirer_username,
          su2.nickname as inquirer_nickname,
          ch.name as channel_name
        FROM csm.csm_inquiry_orders io
        LEFT JOIN crm.customers cc ON io.customer_id = cc.id::text
        LEFT JOIN crm.customer_contacts ccc ON io.contact_id = ccc.id
        LEFT JOIN base.system_user su1 ON io.salesman_id = su1.id
        LEFT JOIN base.system_user su2 ON io.inquirer_id = su2.id
        LEFT JOIN base.channel ch ON io.channel_id = ch.id
        WHERE io.id = $1 AND io.deleted_at IS NULL
      `;

      const inquiryResult = await this.prisma.$queryRawUnsafe(sqlQuery, BigInt(inquiryId));

      if (!inquiryResult || inquiryResult.length === 0) {
        throw new Error('询价单不存在');
      }

      const inquiry = inquiryResult[0];

      // 获取询价详情
      const inquiryDetails = await this.prisma.csm_inquiry_details.findMany({
        where: {
          inquiry_id: BigInt(inquiryId),
          deleted_at: null
        },
        orderBy: { created_at: 'asc' }
      });

      // 格式化返回数据
      const result = {
        id: inquiry.id.toString(),
        inquiryNo: inquiry.inquiry_no,
        customerId: inquiry.customer_id,
        contactId: inquiry.contact_id ? inquiry.contact_id.toString() : null,
        customerAddress: inquiry.customer_address,
        salesmanId: inquiry.salesman_id ? inquiry.salesman_id.toString() : null,
        inquirerId: inquiry.inquirer_id ? inquiry.inquirer_id.toString() : null,
        channelId: inquiry.channel_id ? inquiry.channel_id.toString() : null,
        attachments: inquiry.attachments,
        totalItems: inquiry.total_items,
        remark: inquiry.remark,
        createdAt: inquiry.created_at.toString(),
        updatedAt: inquiry.updated_at.toString(),
        createdBy: inquiry.created_by ? inquiry.created_by.toString() : null,
        updatedBy: inquiry.updated_by ? inquiry.updated_by.toString() : null,

        // 客户信息
        customer: {
          id: inquiry.customer_id,
          name: inquiry.customer_name || '未知客户'
        },

        // 联系人信息
        contact: inquiry.contact_id ? {
          id: inquiry.contact_id.toString(),
          name: inquiry.contact_name || '未知联系人'
        } : null,

        // 业务员信息
        salesman: inquiry.salesman_id ? {
          id: inquiry.salesman_id,
          username: inquiry.salesman_username || '未知',
          nickname: inquiry.salesman_nickname || '未知业务员'
        } : null,

        // 询价员信息
        inquirer: inquiry.inquirer_id ? {
          id: inquiry.inquirer_id,
          username: inquiry.inquirer_username || '未知',
          nickname: inquiry.inquirer_nickname || '未知询价员'
        } : null,

        // 渠道信息
        channel: inquiry.channel_id ? {
          id: inquiry.channel_id,
          name: inquiry.channel_name || '未知渠道'
        } : null,

        // 询价详情
        inquiryDetails: inquiryDetails.map(detail => ({
          id: detail.id.toString(),
          sequence: detail.sequence,
          productName: detail.product_name,
          brand: detail.brand,
          model: detail.model,
          specification: detail.specification,
          referenceLink: detail.reference_link,
          unit: detail.unit,
          quantity: detail.quantity.toString(),
          referencePrice: detail.reference_price ? detail.reference_price.toString() : null,
          customerProductCode: detail.customer_product_code,
          inquiryRemark: detail.inquiry_remark,
          quotePrice: detail.quote_price ? detail.quote_price.toString() : null,
          quoteStatus: detail.quote_status,
          inquiryStatus: detail.inquiry_status,
          createdAt: detail.created_at.toString(),
          updatedAt: detail.updated_at.toString()
        }))
      };

      return {
        code: 200,
        message: '获取询价单详情成功',
        data: result
      };
    } catch (error) {
      console.error('获取询价单详情失败:', error);
      throw new Error(`获取询价单详情失败: ${error.message}`);
    }
  }

  /**
   * 检查重复询价
   * @param {string} customerId 客户ID
   * @param {Array} inquiryItems 询价项目列表
   * @returns {Promise<Object>} 检查结果
   */
  async checkDuplicateInquiry(customerId, inquiryItems) {
    try {
      console.log('开始检查重复询价，客户ID:', customerId);
      console.log('询价项目:', inquiryItems);

      // 计算三个月前的时间戳（毫秒）
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      const threeMonthsAgoTimestamp = BigInt(threeMonthsAgo.getTime());

      console.log('三个月前时间戳:', threeMonthsAgoTimestamp.toString());

      // 查询该客户最近三个月的历史询价详情
      const existingInquiries = await this.prisma.csm_inquiry_details.findMany({
        where: {
          deleted_at: null,
          inquiry_order: {
            customer_id: customerId,
            deleted_at: null,
            created_at: {
              gte: threeMonthsAgoTimestamp
            }
          }
        },
        include: {
          inquiry_order: true
        }
      });

      console.log('找到最近三个月的历史询价记录数量:', existingInquiries.length);

      const exactDuplicates = [];
      const similarDuplicates = [];

      // 检查每个新的询价项目
      for (const newItem of inquiryItems) {
        // 与历史记录比较，收集所有匹配的记录
        for (const existing of existingInquiries) {
          // 检查完全重复（产品名称 + 型号 + 规格描述 全部相同）
          const isExactDuplicate =
            existing.product_name === newItem.productName &&
            (existing.model || '') === (newItem.model || '') &&
            (existing.specification || '') === (newItem.specification || '');

          // 检查相似重复（产品名称 OR 型号 OR 规格描述 有一个相同）
          const isSimilarDuplicate =
            existing.product_name === newItem.productName ||
            (existing.model && newItem.model && existing.model === newItem.model) ||
            (existing.specification && newItem.specification && existing.specification === newItem.specification);

          if (isExactDuplicate) {
            // 格式化创建时间
            const createdDate = new Date(Number(existing.created_at));
            exactDuplicates.push({
              productName: newItem.productName,
              brand: newItem.brand,
              model: newItem.model,
              specification: newItem.specification,
              existingInquiryNo: existing.inquiry_order.inquiry_no,
              existingCreatedAt: existing.created_at.toString(),
              existingCreatedDate: createdDate.toLocaleDateString('zh-CN'),
              daysSinceCreated: Math.floor((Date.now() - Number(existing.created_at)) / (1000 * 60 * 60 * 24)),
              // 添加历史记录的详细信息
              existingProductName: existing.product_name,
              existingBrand: existing.brand,
              existingModel: existing.model,
              existingSpecification: existing.specification
            });
          } else if (isSimilarDuplicate) {
            // 格式化创建时间
            const createdDate = new Date(Number(existing.created_at));
            similarDuplicates.push({
              productName: newItem.productName,
              brand: newItem.brand,
              model: newItem.model,
              specification: newItem.specification,
              existingInquiryNo: existing.inquiry_order.inquiry_no,
              existingCreatedAt: existing.created_at.toString(),
              existingCreatedDate: createdDate.toLocaleDateString('zh-CN'),
              daysSinceCreated: Math.floor((Date.now() - Number(existing.created_at)) / (1000 * 60 * 60 * 24)),
              matchType: existing.product_name === newItem.productName ? 'productName' :
                        (existing.model === newItem.model ? 'model' : 'specification'),
              // 添加历史记录的详细信息
              existingProductName: existing.product_name,
              existingBrand: existing.brand,
              existingModel: existing.model,
              existingSpecification: existing.specification
            });
          }
        }
      }

      console.log('完全重复项数量:', exactDuplicates.length);
      console.log('相似重复项数量:', similarDuplicates.length);

      return {
        code: 200,
        message: '重复检查完成',
        data: {
          exactDuplicates,
          similarDuplicates,
          hasExactDuplicates: exactDuplicates.length > 0,
          hasSimilarDuplicates: similarDuplicates.length > 0
        }
      };
    } catch (error) {
      console.error('检查重复询价失败:', error);
      throw new Error(`检查重复询价失败: ${error.message}`);
    }
  }
}

module.exports = InquiryOrderService;
