/**
 * 采购订单服务
 * 负责采购订单相关的业务逻辑
 */
const PurchaseOrderModel = require("../models/PurchaseOrderModel");
const OrderModel = require("../../models/OrderModel/OrderModel");
const DecimalUtils = require("../../../../core/utils/DecimalUtils");
const OrderTypeEnum = require("../../constants/OrderTypeEnum");
const OrderSourceEnum = require("../../constants/OrderSourceEnum");
const {
  getOrderStatusText,
  getErpStatusText,
  getSplitOrderStatusText,
  getAuditStatusText,
  getCancelStatusText,
  getPendingCancelStatusText,
  getLinkStatusText,
  getIsLossText,
} = require("../constants/PurchaseOrderStatus");

class PurchaseOrderService {
  constructor(prisma) {
    this.prisma = prisma;
    this.purchaseOrderModel = new PurchaseOrderModel(prisma);
    this.orderModel = new OrderModel(prisma);
  }

  /**
   * 根据采购订单号获取采购员信息
   * @param {string} purchaseOrderNumber - 采购订单号
   * @returns {Promise<Object|null>} - 采购员信息
   */
  async getPurchaserByPurchaseOrderNumber(purchaseOrderNumber) {
    try {
      // 根据采购订单号查找采购订单
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          purchase_order_number: purchaseOrderNumber,
          deleted_at: null,
        },
        select: {
          purchaser_id: true,
          purchaser_name: true,
        },
      });

      if (!purchaseOrder) {
        return null;
      }

      // 返回采购员信息
      return {
        purchaserId: purchaseOrder.purchaser_id,
        purchaserName: purchaseOrder.purchaser_name || "",
      };
    } catch (error) {
      console.error("根据采购订单号获取采购员信息失败:", error);
      throw new Error("获取采购员信息失败");
    }
  }

  /**
   * 根据原始订单号获取采购员信息和商品信息
   * @param {string} orderNumber - 原始订单号
   * @returns {Promise<Object|null>} - 采购员信息和商品信息
   */
  async getPurchaserByOrderNumber(orderNumber) {
    try {
      // 1. 根据订单号查找采购订单
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          original_order_number: orderNumber,
          deleted_at: null,
        },
        select: {
          id: true,
          purchaser_id: true,
          purchaser_name: true,
          follower: true,
          buyer_account: true,
          split_status: true,
          split_count: true,
          created_at: true,
          updated_at: true,
        },
      });

      if (!purchaseOrder) {
        // 如果没有找到采购订单，返回null
        return null;
      }

      let products = [];

      // 2. 无论是否拆分，只返回原采购订单的商品信息
      const orderItems = await this.prisma.purchase_order_item.findMany({
        where: {
          purchase_order_id: purchaseOrder.id,
          deleted_at: null,
        },
        select: {
          id: true,
          goods_spu_id: true,
          goods_sku_id: true,
          spu_code_snapshot: true,
          spu_name_snapshot: true,
          product_name: true,
          product_code: true,
          sku_code: true,
          sku: true,
          sku_specifications: true,
          specification: true,
          product_image: true,
          unit_price: true,
          quantity: true,
          available_split_quantity: true,
          item_status: true,
          shipping_info: true,
          // 供应商信息
          suggested_supplier_id: true,
          suggested_supplier_name: true,
          actual_supplier_id: true,
          actual_supplier_name: true,
          created_at: true,
          updated_at: true,
        },
        orderBy: {
          created_at: "asc",
        },
      });

      products = orderItems.map((item) => ({
        id: item.id,
        goodsSpuId: item.goods_spu_id ? item.goods_spu_id.toString() : '',
        goodsSkuId: item.goods_sku_id ? item.goods_sku_id.toString() : '',
        spuCodeSnapshot: item.spu_code_snapshot || '',
        spuNameSnapshot: item.spu_name_snapshot || '',
        productName: item.product_name || '',
        productCode: item.product_code || '',
        skuCode: item.sku_code || '',
        sku: item.sku || '',
        skuSpecifications: item.sku_specifications || null,
        specification: item.specification || '',
        productImage: item.product_image || '',
        unitPrice: item.unit_price || 0,
        quantity: item.quantity || 0,
        availableSplitQuantity: item.available_split_quantity || 0,
        itemStatus: item.item_status || 0,
        shippingInfo: item.shipping_info || '',
        // 供应商信息
        suggestedSupplierId: item.suggested_supplier_id,
        suggestedSupplierName: item.suggested_supplier_name,
        actualSupplierId: item.actual_supplier_id,
        actualSupplierName: item.actual_supplier_name,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
      }));

      // 3. 返回采购员信息和商品信息
      return {
        // 基础人员信息
        purchaser: purchaseOrder.purchaser_name || "",
        follower: purchaseOrder.follower || "",
        buyer: purchaseOrder.buyer_account || "",

        // 拆分状态信息
        splitStatus: purchaseOrder.split_status || 0,
        splitCount: purchaseOrder.split_count || 0,

        // 商品信息
        products: products,

        // 采购订单完整信息（用于售后申请）
        purchaseOrder: {
          id: purchaseOrder.id,
          purchase_order_number: purchaseOrder.purchase_order_number,
          original_order_id: purchaseOrder.original_order_id,
          purchaser_id: purchaseOrder.purchaser_id,
          purchaser_name: purchaseOrder.purchaser_name,
          supplier_id: purchaseOrder.supplier_id,
          supplier_name: purchaseOrder.supplier_name,
          order_source: purchaseOrder.order_source,
          order_type: purchaseOrder.order_type,
          follower: purchaseOrder.follower,
          buyer_account: purchaseOrder.buyer_account,
          delivery_address: purchaseOrder.delivery_address,
          actual_address: purchaseOrder.actual_address,
          recipient_address: purchaseOrder.recipient_address,
          submitter: purchaseOrder.submitter,
          created_at: purchaseOrder.created_at,
          updated_at: purchaseOrder.updated_at,
        },

        // 时间信息
        createdAt: purchaseOrder.created_at,
        updatedAt: purchaseOrder.updated_at,
      };
    } catch (error) {
      console.error("根据订单号获取采购员信息失败:", error);
      throw new Error("获取采购员信息失败");
    }
  }

  /**
   * 申请采购订单
   * @param {Object} applicationData - 申请数据
   * @param {Object} currentUser - 当前用户信息
   * @returns {Promise<Object>} - 申请结果
   */
  async applyPurchaseOrder(applicationData, currentUser) {
    try {
      const { orderId, ...formData } = applicationData;

      // 1. 获取完整的原始订单信息（包括所有关联数据）
      const originalOrder = await this.getCompleteOrderData(orderId);
      if (!originalOrder) {
        throw new Error("原始订单不存在");
      }

      // 2. 检查是否已经申请过采购订单
      const existingPurchaseOrder =
        await this.purchaseOrderModel.findByOriginalOrderId(orderId);
      if (existingPurchaseOrder) {
        throw new Error("该订单已经申请过采购订单");
      }

      // 3. 生成采购订单编号
      const purchaseOrderNumber =
        this.purchaseOrderModel.generatePurchaseOrderNumber();

      // 4. 计算总数量（允许没有商品项的情况）
      const totalQuantity = originalOrder.orderItems
        ? originalOrder.orderItems.reduce(
          (sum, item) => sum + (item.quantity || 0),
          0
        )
        : 0;

      // 6. 构建采购订单数据
      console.log("原始订单数据:", {
        totalProductAmount: originalOrder.totalProductAmount,
        shippingFee: originalOrder.shippingFee,
        totalAmount: originalOrder.totalAmount,
        orderItems: originalOrder.orderItems?.length || 0,
      });

      console.log("转换后的金额数据:", {
        totalProductAmount: this.ensureDecimal(
          originalOrder.totalProductAmount
        ),
        shippingFee: this.ensureDecimal(originalOrder.shippingFee),
        totalAmount: this.ensureDecimal(originalOrder.totalAmount),
      });

      // 记录采购员信息
      console.log("PurchaseOrderService - 采购员信息处理:", {
        原始purchaserId: formData.purchaserId,
        转换后purchaserId: formData.purchaserId
          ? BigInt(formData.purchaserId)
          : null,
        sourceSupplier: formData.sourceSupplier,
      });

      const purchaseOrderData = {
        purchaseOrderNumber,
        originalOrderId: orderId,
        originalOrderNumber: originalOrder.id.toString(),
        thirdPartyOrderSn: originalOrder.thirdPartyOrderSn,

        // 采购相关信息
        purchaserId: formData.purchaserId ? BigInt(formData.purchaserId) : null, // 采购员ID
        purchaserName: formData.sourceSupplier || null, // 采购员username
        supplierId: null,
        supplierName: null,
        supplierCode: null,

        // 订单基础信息（从原订单同步）
        orderSource: this.getOrderSourceText(originalOrder.orderSource),
        orderType: this.getOrderTypeText(originalOrder.orderType),
        follower:
          originalOrder.orderFollowers?.length > 0
            ? originalOrder.orderFollowers[0].followerName ||
            originalOrder.orderFollowers[0].followerId?.toString()
            : null,
        buyerAccount: originalOrder.userId
          ? originalOrder.userId.toString()
          : null,
        deliveryAddress: this.buildDeliveryAddress(
          originalOrder.orderShippingInfo
        ),

        // 订单状态（同步自原订单）
        purchaseStatus: 0, // 待处理
        orderStatus: originalOrder.orderStatus,
        paymentStatus: originalOrder.paymentStatus,
        shippingStatus: originalOrder.shippingStatus,

        // 扩展状态信息（初始值）
        erpStatus: 0, // 待同步
        splitOrderStatus: 0, // 未拆分
        auditStatus: 0, // 待审核
        cancelStatus: null,
        pendingCancelStatus: null,
        linkStatus: 0, // unlinked
        isLoss: 0, // no
        lossReason: null,
        linkGenerated: false,

        // 金额信息（同步自原订单）
        totalProductAmount:
          this.ensureDecimal(originalOrder.totalProductAmount) ?? 0,
        shippingFee: this.ensureDecimal(originalOrder.shippingFee) ?? 0,
        freight: this.ensureDecimal(originalOrder.shippingFee) ?? 0, // 页面字段，与shippingFee相同
        discountAmount: this.ensureDecimal(originalOrder.discountAmount) ?? 0,
        taxAmount: this.ensureDecimal(originalOrder.taxAmount) ?? 0,
        totalAmount: this.ensureDecimal(originalOrder.totalAmount) ?? 0,
        totalQuantity: totalQuantity,
        purchaseCost: null,
        costTotal: null,
        grossProfitRate: null,

        // 物流信息
        logisticsInfo: null,
        logisticsNumber: null,

        // 收货信息（从原订单的配送信息获取）
        recipientName: originalOrder.orderShippingInfo?.recipientName,
        recipientPhone: originalOrder.orderShippingInfo?.recipientPhone,
        recipientAddress: this.buildFullAddress(
          originalOrder.orderShippingInfo
        ),
        actualReceiver: formData.actualReceiver,
        actualPhone: formData.contactPhone,
        actualAddress: formData.actualAddress,

        // 备注信息
        businessRemark: formData.businessManager,
        orderRemark: formData.orderRemark || originalOrder.remark,
        customerRemark: formData.customerRemark || originalOrder.adminRemark,
        purchaseRemark: null,

        // 渠道信息（同步自原订单）
        channelId: originalOrder.channelId
          ? BigInt(originalOrder.channelId)
          : null,
        channelName: originalOrder.channel?.name,
        platformId: originalOrder.platformId
          ? BigInt(originalOrder.platformId)
          : null,
        platformName: originalOrder.platformName,
        storeId: originalOrder.storeId ? BigInt(originalOrder.storeId) : null,
        storeName: originalOrder.storeName,

        // 时间信息
        originalOrderTime: originalOrder.createdAt
          ? new Date(Number(originalOrder.createdAt))
          : null,
        purchaseTime: new Date(), // 申请采购的时间（当前时间）
        purchaseApplyTime: new Date(), // 当前时间
        purchaseAssignTime: null,
        purchaseCompleteTime: null,
        expectedDeliveryTime: null,
        actualDeliveryTime: null,

        // 系统字段
        submitter: currentUser.nickname || currentUser.username || "系统用户",
        updater: null,

        // 订单商品项（从原订单同步，包含建议供应商信息）
        items: await this.transformOrderItems(
          originalOrder.orderItems || [],
          applicationData.suggestedSuppliers || []
        ),

        // 附件信息
        attachments: this.transformAttachments(
          applicationData.attachments || []
        ),
      };

      // 6. 在事务中创建采购订单并更新原订单
      const result = await this.prisma.$transaction(async (tx) => {
        // 创建采购订单
        const purchaseOrder = await this.purchaseOrderModel.createPurchaseOrder(
          purchaseOrderData,
          tx
        );

        // 更新原订单的 purchase_order_id 字段
        await tx.orders.update({
          where: {
            id:
              typeof orderId === "string"
                ? BigInt(orderId)
                : typeof orderId === "number"
                  ? BigInt(orderId)
                  : orderId,
          },
          data: {
            purchase_order_number: purchaseOrder.purchase_order_number,
            updated_at: BigInt(Date.now()),
          },
        });

        return purchaseOrder;
      });

      return {
        success: true,
        data: {
          id: result.id,
          purchaseOrderNumber: result.purchase_order_number,
          originalOrderId: orderId,
        },
        message: "采购订单申请成功",
      };
    } catch (error) {
      console.error("申请采购订单失败:", error);
      throw error;
    }
  }

  /**
   * 获取完整的订单数据（包括所有关联信息）
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} - 完整的订单数据
   */
  async getCompleteOrderData(orderId) {
    try {
      const orderIdBigInt = BigInt(orderId);

      // 获取订单基础信息
      const order = await this.orderModel.prisma.orders.findFirst({
        where: {
          id: orderIdBigInt,
          deleted_at: null,
        },
        include: {
          // 配送信息
          order_shipping_info: true,
          // 渠道信息
          channel: {
            select: {
              id: true,
              name: true,
              icon_url: true,
            },
          },
          // 支付记录
          payment_records: true,
        },
      });

      if (!order) {
        return null;
      }

      // 获取完整的订单商品项信息
      const orderItems = await this.orderModel.prisma.order_items.findMany({
        where: {
          order_id: orderIdBigInt,
        },
      });

      // 获取订单跟单员信息
      const orderFollowers =
        await this.orderModel.prisma.order_followers.findMany({
          where: {
            order_id: orderIdBigInt,
          },
        });

      // 获取订单业务员信息
      const orderSalespeople =
        await this.orderModel.prisma.order_salespeople.findMany({
          where: {
            order_id: orderIdBigInt,
          },
        });

      // 获取店铺信息（如果有storeId）
      let storeName = null;
      if (order.store_id) {
        try {
          const storeQuery = `
            SELECT id, name, code FROM base.store
            WHERE id = '${order.store_id}' AND deleted_at IS NULL
            LIMIT 1
          `;
          const storeResult = await this.orderModel.prisma.$queryRawUnsafe(
            storeQuery
          );
          if (storeResult && storeResult.length > 0) {
            storeName = storeResult[0].name;
          }
        } catch (error) {
          console.error("查询店铺信息失败:", error);
        }
      }

      // 获取平台信息（如果有platformId）
      let platformName = null;
      if (order.platform_id) {
        try {
          const platformQuery = `
            SELECT id, name FROM base.platform
            WHERE id = '${order.platform_id}' AND deleted_at IS NULL
            LIMIT 1
          `;
          const platformResult = await this.orderModel.prisma.$queryRawUnsafe(
            platformQuery
          );
          if (platformResult && platformResult.length > 0) {
            platformName = platformResult[0].name;
          }
        } catch (error) {
          console.error("查询平台信息失败:", error);
        }
      }

      // 处理BigInt序列化并转换为camelCase
      const processedOrder = this.handleBigInt(order);
      const processedItems = orderItems.map((item) => this.handleBigInt(item));
      const processedFollowers = orderFollowers.map((follower) =>
        this.handleBigInt(follower)
      );
      const processedSalespeople = orderSalespeople.map((sp) =>
        this.handleBigInt(sp)
      );

      return {
        ...this.convertKeysToCamelCase(processedOrder),
        orderItems: processedItems.map((item) =>
          this.convertKeysToCamelCase(item)
        ),
        orderFollowers: processedFollowers.map((follower) =>
          this.convertKeysToCamelCase(follower)
        ),
        orderSalespeople: processedSalespeople.map((sp) =>
          this.convertKeysToCamelCase(sp)
        ),
        storeName,
        platformName,
      };
    } catch (error) {
      console.error("获取完整订单数据失败:", error);
      throw error;
    }
  }

  /**
   * 处理BigInt序列化问题
   * @param {Object} obj - 需要处理的对象
   * @returns {Object} - 处理后的对象
   */
  handleBigInt(obj) {
    if (!obj) return obj;

    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === "bigint") {
        result[key] = value.toString();
      } else if (value && typeof value === "object" && !Array.isArray(value)) {
        result[key] = this.handleBigInt(value);
      } else if (Array.isArray(value)) {
        result[key] = value.map((item) =>
          typeof item === "object" ? this.handleBigInt(item) : item
        );
      } else {
        result[key] = value;
      }
    }
    return result;
  }

  /**
   * 将字段名转换为camelCase格式
   * @param {Object} obj - 需要转换的对象
   * @returns {Object} - 转换后的对象
   */
  convertKeysToCamelCase(obj) {
    if (!obj || typeof obj !== "object") return obj;

    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase()
      );
      if (value && typeof value === "object" && !Array.isArray(value)) {
        result[camelKey] = this.convertKeysToCamelCase(value);
      } else if (Array.isArray(value)) {
        result[camelKey] = value.map((item) =>
          typeof item === "object" ? this.convertKeysToCamelCase(item) : item
        );
      } else {
        result[camelKey] = value;
      }
    }
    return result;
  }

  /**
   * 从 SKU 规格信息中提取规格文本
   * @param {Object|string} skuSpecifications - SKU规格信息
   * @returns {string|null} - 规格文本
   */
  getSpecificationText(skuSpecifications) {
    if (!skuSpecifications) return null;

    try {
      // 如果是字符串
      if (typeof skuSpecifications === "string") {
        // 先尝试解析为JSON对象
        try {
          const specs = JSON.parse(skuSpecifications);
          if (typeof specs === "object" && specs !== null) {
            const specArray = [];
            for (const [key, value] of Object.entries(specs)) {
              if (value) {
                specArray.push(`${key}:${value}`);
              }
            }
            return specArray.length > 0 ? specArray.join(", ") : null;
          }
        } catch (jsonError) {
          // JSON解析失败，说明是普通字符串格式的规格信息，直接返回
          console.log("规格信息为普通字符串格式:", skuSpecifications);
          return skuSpecifications.trim() || null;
        }
      }

      // 如果是对象，将其转换为文本
      if (typeof skuSpecifications === "object" && skuSpecifications !== null) {
        const specArray = [];
        for (const [key, value] of Object.entries(skuSpecifications)) {
          if (value) {
            specArray.push(`${key}:${value}`);
          }
        }
        return specArray.length > 0 ? specArray.join(", ") : null;
      }

      return null;
    } catch (error) {
      console.warn("解析SKU规格失败:", error);
      return null;
    }
  }

  /**
   * 确保 JSON 字段为 undefined 而不是 null（Prisma 要求）
   * @param {any} value - 需要处理的值
   * @returns {any|undefined} - 处理后的值或undefined
   */
  ensureJsonField(value) {
    if (value === null || value === undefined || value === "") {
      return undefined;
    }
    return value;
  }

  /**
   * 处理规格字段，确保正确的数据类型
   * @param {any} skuSpecifications - 原始规格数据
   * @returns {Object} - 包含 skuSpecifications 和 specification 的对象
   */
  processSpecificationFields(skuSpecifications) {
    if (!skuSpecifications) {
      return {
        skuSpecifications: undefined,
        specification: null,
      };
    }

    // 如果是字符串格式的规格信息（如 "颜色: 月光白"）
    if (typeof skuSpecifications === "string") {
      try {
        // 尝试解析为JSON
        const parsed = JSON.parse(skuSpecifications);
        if (typeof parsed === "object" && parsed !== null) {
          // 是有效的JSON对象，保存为JSON
          return {
            skuSpecifications: parsed,
            specification: this.getSpecificationText(parsed),
          };
        }
      } catch (error) {
        // JSON解析失败，说明是普通字符串格式
        // 对于字符串格式的规格，我们将其保存为 specification 字段
        // 而 sku_specifications 字段设为 undefined
        return {
          skuSpecifications: undefined,
          specification: skuSpecifications.trim(),
        };
      }
    }

    // 如果是对象格式
    if (typeof skuSpecifications === "object" && skuSpecifications !== null) {
      return {
        skuSpecifications: skuSpecifications,
        specification: this.getSpecificationText(skuSpecifications),
      };
    }

    return {
      skuSpecifications: undefined,
      specification: null,
    };
  }

  /**
   * 确保数值为 Decimal 类型
   * @param {any} value - 需要转换的值
   * @returns {number|null} - 转换后的数值或null
   */
  ensureDecimal(value) {
    if (value === null || value === undefined) {
      return null;
    }

    // 使用项目中的 DecimalUtils 工具类来处理 Decimal 转换
    try {
      return DecimalUtils.convertToNumber(value);
    } catch (error) {
      console.warn("DecimalUtils 转换失败:", error);
      return null;
    }
  }

  /**
   * 获取订单来源文本
   * @param {number} orderSource - 订单来源代码
   * @returns {string} - 订单来源文本
   */
  getOrderSourceText(orderSource) {
    const sourceMap = {
      0: "系统创建",
      1: "后台创建",
      2: "商城下单",
      3: "APP下单",
      4: "小程序下单",
    };
    return sourceMap[orderSource] || "未知来源";
  }

  /**
   * 获取订单类型文本
   * @param {number} orderType - 订单类型代码
   * @returns {string} - 订单类型文本
   */
  getOrderTypeText(orderType) {
    const typeMap = {
      0: "普通订单",
      1: "秒杀订单",
      2: "拼团订单",
      3: "预售订单",
      4: "积分订单",
    };
    return typeMap[orderType] || "普通订单";
  }

  /**
   * 构建配送地址
   * @param {Object} shippingInfo - 配送信息
   * @returns {string} - 完整的配送地址
   */
  buildDeliveryAddress(shippingInfo) {
    if (!shippingInfo) return null;

    const parts = [];
    if (shippingInfo.regionPathName) {
      parts.push(shippingInfo.regionPathName);
    }
    if (shippingInfo.streetAddress) {
      parts.push(shippingInfo.streetAddress);
    }

    return parts.length > 0 ? parts.join(" ") : null;
  }

  /**
   * 构建完整地址
   * @param {Object} shippingInfo - 配送信息
   * @returns {string} - 完整地址
   */
  buildFullAddress(shippingInfo) {
    if (!shippingInfo) return null;

    const parts = [];
    if (shippingInfo.regionPathName) {
      parts.push(shippingInfo.regionPathName);
    }
    if (shippingInfo.streetAddress) {
      parts.push(shippingInfo.streetAddress);
    }

    return parts.length > 0 ? parts.join(" ") : null;
  }

  /**
   * 转换订单商品项数据
   * @param {Array} orderItems - 原始订单商品项（camelCase格式）
   * @param {Array} suggestedSuppliers - 建议供应商信息
   * @returns {Array} - 转换后的采购订单商品项
   */
  async transformOrderItems(orderItems, suggestedSuppliers = []) {
    console.log("转换订单商品项，原始数据:", orderItems);
    console.log("建议供应商数据:", suggestedSuppliers);

    // 批量查询所有商品的巡检成本
    const inspectionCosts = await this.getInspectionCostsForItems(orderItems);

    return orderItems.map((item, index) => {
      console.log("处理商品项:", {
        id: item.id,
        unitPrice: item.unitPrice || item.unit_price,
        totalPrice: item.totalPrice || item.total_price,
        itemPaidAmount: item.itemPaidAmount || item.item_paid_amount,
        skuSpecifications: item.skuSpecifications || item.sku_specifications,
      });

      console.log("转换后的商品项金额:", {
        unitPrice: this.ensureDecimal(item.unitPrice ?? item.unit_price),
        totalPrice: this.ensureDecimal(item.totalPrice ?? item.total_price),
        itemPaidAmount: this.ensureDecimal(
          item.itemPaidAmount ?? item.item_paid_amount
        ),
      });

      // 详细调试规格数据处理
      const rawSkuSpecs = item.skuSpecifications ?? item.sku_specifications;
      console.log("原始规格数据:", {
        skuSpecifications: item.skuSpecifications,
        sku_specifications: item.sku_specifications,
        rawSkuSpecs: rawSkuSpecs,
        rawSkuSpecsType: typeof rawSkuSpecs,
      });

      const specFields = this.processSpecificationFields(rawSkuSpecs);

      console.log("处理后的规格数据:", {
        skuSpecifications: specFields.skuSpecifications,
        skuSpecificationsType: typeof specFields.skuSpecifications,
        specification: specFields.specification,
      });

      return {
        originalOrderItemId: item.id ? BigInt(item.id) : null,

        // 商品信息（处理camelCase字段名）
        goodsSpuId: item.goodsSpuId || item.goods_spu_id,
        goodsSkuId: item.goodsSkuId || item.goods_sku_id,
        spuCodeSnapshot: item.spuCodeSnapshot || item.spu_code_snapshot,
        spuNameSnapshot: item.spuNameSnapshot || item.spu_name_snapshot,
        productName: item.productName || item.product_name,
        productCode: item.productCode || item.product_code || null,
        skuCode: item.skuCode || item.sku_code,
        sku: item.sku ?? item.skuCode ?? item.sku_code,
        skuSpecifications: specFields.skuSpecifications,
        specification: specFields.specification,
        productImage: item.productImage || item.product_image,

        // 价格信息
        unitPrice: this.ensureDecimal(item.unitPrice ?? item.unit_price) ?? 0,
        marketPriceSnapshot: this.ensureDecimal(
          item.marketPriceSnapshot ?? item.market_price_snapshot
        ),
        costPriceSnapshot: this.ensureDecimal(
          item.costPriceSnapshot ?? item.cost_price_snapshot
        ),
        purchasePrice: null,
        supplyPrice: null,
        contractPrice: null,

        // 成本信息
        actualCost: null,
        inspectionCost: inspectionCosts[item.goodsSkuId] || null,
        profitRate: null,
        costTotal: null,

        // 数量信息
        quantity: parseInt(item.quantity) || 1,
        purchasedQuantity: 0,
        receivedQuantity: 0,
        availableSplitQuantity: parseInt(item.quantity) || 1,

        // 金额信息
        totalPrice:
          this.ensureDecimal(item.totalPrice ?? item.total_price) ?? 0,
        totalAmount:
          this.ensureDecimal(item.totalPrice ?? item.total_price) ?? 0,
        itemPaidAmount:
          this.ensureDecimal(item.itemPaidAmount ?? item.item_paid_amount) ?? 0,
        purchaseTotal: null,

        // 第三方信息
        thirdPartySpuId: item.thirdPartySpuId ?? item.third_party_spu_id,
        thirdPartySkuId: item.thirdPartySkuId ?? item.third_party_sku_id,
        thirdPartyProductCode:
          item.thirdPartyProductCode ?? item.third_party_product_code,
        thirdPartyItemSnapshot: this.ensureJsonField(
          item.thirdPartyItemSnapshot ?? item.third_party_item_snapshot
        ),

        // 供应商信息 - 使用多种策略匹配建议供应商
        ...(() => {
          return this.matchSupplierForOrderItem(
            item,
            suggestedSuppliers,
            index
          );
        })(),

        // 税收分类
        taxCategory: null,

        // 物流信息
        weightSnapshot: this.ensureDecimal(
          item.weightSnapshot ?? item.weight_snapshot
        ),
        volumeSnapshot: this.ensureDecimal(
          item.volumeSnapshot ?? item.volume_snapshot
        ),
        shippingInfo: null,

        // 状态信息
        itemStatus: 0, // 待处理
        purchaseProgress: null,

        // 时间信息
        expectedDeliveryTime: null,
        actualDeliveryTime: null,
        expiryTime: null,
        productLineExpireTime: null,

        // 备注信息
        itemRemark: null,
      };
    });
  }

  /**
   * 批量获取商品的巡检成本（最近三个月最低报价）
   * @param {Array} orderItems - 订单商品项数组
   * @returns {Promise<Object>} - SKU对应的巡检成本映射
   */
  async getInspectionCostsForItems(orderItems) {
    try {
      // 提取所有商品的SKU
      const skus = orderItems
        .map(item => item.goodsSkuId)
        .filter(sku => sku); // 过滤掉空值

      if (skus.length === 0) {
        return {};
      }

      console.log("查询巡检成本的SKU列表:", skus);

      // 计算三个月前的时间戳
      const threeMonthsAgo = Date.now() - (3 * 30 * 24 * 60 * 60 * 1000);

      // 批量查询最近三个月的询价记录，按SKU分组获取最低报价
      const inquiryRecords = await this.prisma.csm_inquiry_records.findMany({
        where: {
          product_code: {
            in: skus
          },
          inquiry_time: {
            gte: BigInt(threeMonthsAgo)
          },
          record_status: 0, // 有效记录
          deleted_at: null
        },
        select: {
          product_code: true,
          current_price: true,
          inquiry_time: true
        },
        orderBy: {
          current_price: 'asc' // 按价格升序排列，方便获取最低价
        }
      });

      console.log("查询到的询价记录数量:", inquiryRecords.length);

      // 按SKU分组，获取每个SKU的最低报价
      const inspectionCosts = {};
      const skuPriceMap = {};

      inquiryRecords.forEach(record => {
        const sku = record.product_code;
        const price = parseFloat(record.current_price);

        if (!skuPriceMap[sku] || price < skuPriceMap[sku]) {
          skuPriceMap[sku] = price;
          inspectionCosts[sku] = price;
        }
      });

      console.log("计算得到的巡检成本映射:", inspectionCosts);

      return inspectionCosts;
    } catch (error) {
      console.error("获取巡检成本失败:", error);
      return {};
    }
  }

  /**
   * 为订单商品项匹配供应商
   * @param {Object} item - 订单商品项
   * @param {Array} suggestedSuppliers - 建议供应商列表
   * @param {number} itemIndex - 商品项在订单中的索引位置
   * @returns {Object} - 供应商匹配结果
   */
  matchSupplierForOrderItem(item, suggestedSuppliers, itemIndex) {
    console.log(`\n=== 开始匹配供应商 (商品项索引: ${itemIndex}) ===`);

    // 获取商品编码，尝试多个可能的字段
    const itemProductCode =
      item.goodsSkuId ?? item.goods_sku_id ?? item.skuCode ?? item.sku;

    console.log(`商品项信息:`, {
      itemId: item.id,
      itemIndex,
      productName: item.productName,
      goodsSkuId: item.goodsSkuId,
      goods_sku_id: item.goods_sku_id,
      skuCode: item.skuCode,
      sku: item.sku,
      finalProductCode: itemProductCode,
    });

    console.log(
      `建议供应商列表:`,
      suggestedSuppliers.map((s, idx) => ({
        index: idx,
        productId: s.productId,
        productCode: s.productCode,
        productName: s.productName,
        supplierId: s.supplierId,
        supplierName: s.supplierName,
      }))
    );

    let matchedSupplier = null;
    let matchMethod = "";

    // 策略1: 优先根据商品编码(productCode)精确匹配
    if (itemProductCode && itemProductCode.toString().trim() !== "") {
      matchedSupplier = suggestedSuppliers.find((supplier) => {
        const supplierProductCode = supplier.productCode;
        if (
          !supplierProductCode ||
          supplierProductCode.toString().trim() === ""
        ) {
          return false;
        }

        const match =
          supplierProductCode.toString().trim() ===
          itemProductCode.toString().trim();
        if (match) {
          console.log(
            `✅ 策略1成功: 商品编码精确匹配 "${supplierProductCode}" === "${itemProductCode}"`
          );
        }
        return match;
      });
      if (matchedSupplier) {
        matchMethod = "商品编码精确匹配";
      }
    }

    // 策略2: 如果商品编码匹配失败，使用商品名称去除空格后进行精确匹配
    if (!matchedSupplier && item.productName) {
      // 去除所有空格后进行比较
      const itemProductNameNoSpaces = item.productName
        .replace(/\s+/g, "")
        .toLowerCase();

      console.log(
        `尝试商品名称匹配，订单商品名称(去空格): "${itemProductNameNoSpaces}"`
      );

      matchedSupplier = suggestedSuppliers.find((supplier) => {
        if (!supplier.productName) return false;

        const supplierProductNameNoSpaces = supplier.productName
          .replace(/\s+/g, "")
          .toLowerCase();
        const match = itemProductNameNoSpaces === supplierProductNameNoSpaces;

        console.log(
          `商品名称匹配检查: "${supplierProductNameNoSpaces}" === "${itemProductNameNoSpaces}" = ${match}`
        );

        if (match) {
          console.log(
            `✅ 策略2成功: 商品名称精确匹配(去空格) "${supplier.productName}" ≈ "${item.productName}"`
          );
        }
        return match;
      });
      if (matchedSupplier) {
        matchMethod = "商品名称精确匹配(去空格)";
      }
    }

    // 策略3: 基于索引位置匹配（作为备选方案）
    if (!matchedSupplier && itemIndex < suggestedSuppliers.length) {
      const supplierByIndex = suggestedSuppliers[itemIndex];
      if (supplierByIndex && supplierByIndex.supplierId) {
        matchedSupplier = supplierByIndex;
        matchMethod = "索引位置匹配";
        console.log(`✅ 策略3成功: 基于索引位置匹配 (索引: ${itemIndex})`);
      }
    }

    // 策略4: 如果只有一个供应商，直接使用
    if (!matchedSupplier && suggestedSuppliers.length === 1) {
      const singleSupplier = suggestedSuppliers[0];
      if (singleSupplier && singleSupplier.supplierId) {
        matchedSupplier = singleSupplier;
        matchMethod = "单一供应商默认匹配";
        console.log(`✅ 策略4成功: 单一供应商默认匹配`);
      }
    }

    console.log(`匹配结果:`, {
      itemProductCode,
      itemIndex,
      matchMethod,
      matchedSupplier: matchedSupplier
        ? {
          productCode: matchedSupplier.productCode,
          productName: matchedSupplier.productName,
          supplierId: matchedSupplier.supplierId,
          supplierName: matchedSupplier.supplierName,
        }
        : null,
      hasSupplier: !!matchedSupplier,
    });

    if (matchedSupplier && matchedSupplier.supplierId) {
      console.log(
        `🎯 最终使用供应商: ${matchedSupplier.supplierName} (ID: ${matchedSupplier.supplierId}) - 匹配方式: ${matchMethod}`
      );
      return {
        suggestedSupplierId: BigInt(matchedSupplier.supplierId),
        suggestedSupplierName: matchedSupplier.supplierName || null,
        actualSupplierId: null,
        actualSupplierName: null,
      };
    } else {
      console.log("❌ 所有匹配策略都失败，使用默认值");
      return {
        suggestedSupplierId: null,
        suggestedSupplierName: null,
        actualSupplierId: null,
        actualSupplierName: null,
      };
    }
  }

  /**
   * 转换附件数据
   * @param {Array} attachments - 原始附件数据
   * @returns {Array} - 转换后的附件数据
   */
  transformAttachments(attachments) {
    return attachments.map((attachment) => ({
      fileName: attachment.name,
      filePath: attachment.url || "",
      fileUrl: attachment.url || "",
      fileSize: attachment.size,
      fileType: attachment.type,
      mimeType: attachment.type,
      attachmentType: "other",
      attachmentCategory: "申请材料",
      description: attachment.description || "",
      remark: attachment.remark || "",
    }));
  }

  /**
   * 获取采购订单列表
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Promise<Object>} - 采购订单列表
   */
  async getPurchaseOrders(filters = {}, page = 1, pageSize = 10) {
    try {
      console.log(
        "PurchaseOrderService.getPurchaseOrders 接收到的filters:",
        filters
      );

      // 处理商品相关的筛选条件
      const itemFilters = {};
      const orderFilters = { ...filters };

      // 提取商品相关的筛选条件
      if (filters.productCode) {
        // 商品编码对应 goods_spu_id 字段
        itemFilters.goods_spu_id = BigInt(filters.productCode);
        delete orderFilters.productCode;
      }

      if (filters.productName) {
        itemFilters.product_name = { contains: filters.productName };
        delete orderFilters.productName;
      }

      if (filters.productLineExpireStatus) {
        const now = new Date();
        if (filters.productLineExpireStatus === "expired") {
          itemFilters.product_line_expire_time = { lt: now };
        } else if (filters.productLineExpireStatus === "valid") {
          itemFilters.product_line_expire_time = { gte: now };
        } else if (filters.productLineExpireStatus === "expiring") {
          // 即将过期：30天内过期
          const thirtyDaysLater = new Date(
            now.getTime() + 30 * 24 * 60 * 60 * 1000
          );
          itemFilters.product_line_expire_time = {
            gte: now,
            lte: thirtyDaysLater,
          };
        }
        delete orderFilters.productLineExpireStatus;
      }

      if (filters.costPrice) {
        console.log("成本价范围筛选参数:", filters.costPrice);
        // 成本总价范围筛选 - 需要计算订单中所有商品的成本总和
        if (
          Array.isArray(filters.costPrice) &&
          filters.costPrice.length === 2
        ) {
          const minCost = parseFloat(filters.costPrice[0]) || 0;
          const maxCost = parseFloat(filters.costPrice[1]) || 999999;

          console.log("成本总价筛选条件:", { minCost, maxCost });

          // 使用原生SQL查询来计算每个订单的商品成本总和并过滤
          // 计算每个订单中所有商品项的cost_total字段总和
          const ordersWithCostInRange = await this.prisma.$queryRaw`
            SELECT DISTINCT poi.purchase_order_id
            FROM csm.purchase_order_item poi
            WHERE poi.deleted_at IS NULL
            GROUP BY poi.purchase_order_id
            HAVING SUM(COALESCE(poi.cost_total, 0)) >= ${minCost}
               AND SUM(COALESCE(poi.cost_total, 0)) <= ${maxCost}
          `;

          console.log("符合成本范围的订单ID:", ordersWithCostInRange);

          if (ordersWithCostInRange.length > 0) {
            const orderIds = ordersWithCostInRange.map(
              (row) => row.purchase_order_id
            );
            // 将订单ID过滤条件添加到主查询
            orderFilters.id = { in: orderIds };
          } else {
            // 如果没有符合条件的订单，设置一个不可能的ID来返回空结果
            orderFilters.id = { in: [] };
          }
        }
        delete orderFilters.costPrice;
      }

      // 处理手机号码搜索 - 将 contact_phone 或 contactPhone 映射到正确的字段名
      if (filters.contact_phone || filters.contactPhone) {
        const phoneNumber = filters.contact_phone || filters.contactPhone;
        orderFilters.recipient_phone = { contains: phoneNumber };
        delete orderFilters.contact_phone;
        delete orderFilters.contactPhone;
      }

      // 构建查询条件
      const where = {
        deleted_at: null,
        ...orderFilters,
      };

      // 如果有商品筛选条件，需要通过关联查询
      if (Object.keys(itemFilters).length > 0) {
        where.purchase_order_items = {
          some: {
            deleted_at: null,
            ...itemFilters,
          },
        };
      }

      console.log("最终的查询条件 where:", JSON.stringify(where, null, 2));
      console.log("商品筛选条件 itemFilters:", itemFilters);

      // 如果有商品编码筛选，先测试是否存在匹配的商品项
      if (filters.productCode) {
        try {
          const productCodeBigInt = BigInt(filters.productCode);

          // 测试1: 精确匹配 goods_spu_id
          const exactMatch = await this.prisma.purchase_order_item.findMany({
            where: {
              deleted_at: null,
              goods_spu_id: productCodeBigInt,
            },
            select: {
              id: true,
              goods_spu_id: true,
              goods_sku_id: true,
              product_code: true,
              product_name: true,
              purchase_order_id: true,
            },
            take: 3,
          });
          console.log("测试查询1 - 精确匹配 goods_spu_id:", exactMatch);

          // 测试2: 查看前几条数据的实际结构
          const sampleData = await this.prisma.purchase_order_item.findMany({
            where: { deleted_at: null },
            select: {
              id: true,
              goods_spu_id: true,
              goods_sku_id: true,
              product_code: true,
              product_name: true,
            },
            take: 5,
            orderBy: { created_at: "desc" },
          });
          console.log("测试查询2 - 最新的5条商品项数据样本:", sampleData);
        } catch (error) {
          console.error("BigInt转换失败:", error);
          console.log(
            "原始productCode值:",
            filters.productCode,
            "类型:",
            typeof filters.productCode
          );
        }
      }

      const skip = (page - 1) * pageSize;

      const [orders, total] = await Promise.all([
        this.prisma.purchase_order.findMany({
          where,
          skip,
          take: pageSize,
          include: {
            purchase_order_items: {
              where: { deleted_at: null },
              orderBy: { created_at: "asc" },
            },
            purchase_order_attachments: {
              where: { deleted_at: null },
            },
            purchase_order_expenses: {
              select: { id: true }, // 只需要知道是否存在费用单
            },
            // 关联查询原订单信息，获取原订单状态
            original_order: {
              select: {
                id: true,
                order_status: true,
                payment_status: true,
                shipping_status: true,
                invoice_status: true,
                order_source: true,
                order_type: true,
                created_at: true,
                updated_at: true,
              },
            },
          },
          orderBy: {
            created_at: "desc",
          },
        }),
        this.prisma.purchase_order.count({ where }),
      ]);

      console.log("查询结果 - 订单数量:", orders.length, "总数:", total);

      // 转换数据格式以匹配页面需求
      const transformedOrders = orders.map((order) =>
        this.transformOrderForDisplay(order)
      );

      return {
        items: transformedOrders,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      };
    } catch (error) {
      console.error("获取采购订单列表失败:", error);
      throw error;
    }
  }

  /**
   * 转换订单数据格式以匹配页面显示需求
   * @param {Object} order - 原始订单数据
   * @returns {Object} - 转换后的订单数据
   */
  transformOrderForDisplay(order) {
    // 计算商品总数量
    const totalQuantity =
      order.purchase_order_items?.reduce(
        (sum, item) => sum + (item.quantity || 0),
        0
      ) || 0;

    // 计算总金额
    const totalAmount = order.total_amount || 0;

    // 计算成本总价
    const costTotal =
      order.purchase_order_items?.reduce(
        (sum, item) =>
          sum + parseFloat(item.cost_total || item.actual_cost || 0),
        0
      ) || 0;

    // 计算毛利率
    let grossProfitRate = "0.0%";
    if (totalAmount > 0) {
      const profitRate = (
        ((totalAmount - costTotal) / totalAmount) *
        100
      ).toFixed(1);
      grossProfitRate = `${profitRate}%`;
    }

    // 转换商品数据
    const products =
      order.purchase_order_items?.map((item) => ({
        id: item.id,
        productImage: item.product_image || "https://via.placeholder.com/60x60",
        productName: item.product_name,
        sku: item.sku || item.goods_sku_id,
        productCode: item.goods_spu_id || item.spu_code_snapshot,
        specification: item.specification,
        taxCategory: item.tax_category,
        taxClassification: item.tax_category
          ? {
            code: item.tax_category,
            label: item.tax_category,
          }
          : null,
        unitPrice: parseFloat(item.unit_price || 0).toFixed(2),
        quantity: item.quantity || 0,
        totalAmount: parseFloat(
          item.total_amount || item.total_price || 0
        ).toFixed(2),
        actualCost: parseFloat(item.actual_cost || 0).toFixed(2),
        inspectionCost: parseFloat(item.inspection_cost || 0).toFixed(2),
        profitRate: item.profit_rate || "0.0%",
        productLineExpireTime: item.product_line_expire_time,
        actualSupplier: item.actual_supplier_name,
        actualSupplierId: item.actual_supplier_id,
        suggestedSupplier: item.suggested_supplier_name,
        suggestedSupplierId: item.suggested_supplier_id,
        purchaseProgress: item.purchase_progress || null,
        costTotal: parseFloat(item.cost_total || item.actual_cost || 0).toFixed(
          2
        ),
        lossReason: item.loss_reason,
        // 商品项货期时间
        expectedDeliveryTime: item.expected_delivery_time,
        deliveryTime: item.expected_delivery_time
          ? item.expected_delivery_time.getTime()
          : null,
      })) || [];

    const transformedOrder = {
      id: order.id,
      orderNumber: order.purchase_order_number,
      originalOrderNumber: order.original_order_number,
      thirdPartyOrderSn: order.third_party_order_sn,
      purchaseTime: order.purchase_time,
      orderTime: order.original_order_time,
      orderSource: order.order_source,
      storeName: order.store_name,
      deliveryAddress: order.delivery_address,
      follower: order.follower,
      purchaser: order.purchaser_name,
      orderType: order.order_type,
      products: products,
      freight: parseFloat(order.freight || order.shipping_fee || 0).toFixed(2),
      totalQuantity: totalQuantity,
      totalAmount: order.total_amount,
      grossProfitRate: order.gross_profit_rate || grossProfitRate,
      costTotal: (order.purchase_cost || order.cost_total || costTotal).toFixed(
        2
      ),
      buyerAccount: order.buyer_account,
      businessRemark: order.business_remark,
      logisticsInfo: order.logistics_info,
      purchaseProgress:
        order.purchase_order_items?.[0]?.purchase_progress || null,
      purchaseRemark: order.purchase_remark,
      // 渠道信息
      channelId: order.channel_id ? order.channel_id.toString() : null,
      channelName: order.channel_name || null,
      // 供应商信息从商品维度获取，订单级别不显示
      actualSupplier: null,
      actualSupplierId: null,
      suggestedSupplier: null,
      // 检查是否所有商品都已设置税收分类
      allItemsHaveTaxCategory: products.every(
        (product) => product.taxCategory && product.taxCategory.trim() !== ""
      ),
      erpStatus: getErpStatusText(order.erp_status),
      logisticsNumber: order.logistics_number,
      splitOrderStatus: getSplitOrderStatusText(order.split_order_status),
      auditStatus: getAuditStatusText(order.audit_status),
      orderStatus: getOrderStatusText(order.order_status),
      purchaseStatus: this.getPurchaseStatusText(order.purchase_status),
      cancelStatus: getCancelStatusText(order.cancel_status),
      pendingCancelStatus: getPendingCancelStatusText(
        order.pending_cancel_status
      ),
      linkStatus: getLinkStatusText(order.link_status),
      recipientName: order.recipient_name || order.actual_receiver,
      contactPhone: order.recipient_phone || order.actual_phone,
      orderAddress: order.recipient_address || order.actual_address,
      // 实际收货信息
      actualReceiver: order.actual_receiver,
      actualPhone: order.actual_phone,
      actualAddress: order.actual_address,
      isLoss: getIsLossText(order.is_loss),
      lossReason: order.loss_reason,
      linkGenerated: order.link_generated,
      // 费用单存在标识
      hasExpenseOrders:
        order.purchase_order_expenses &&
        order.purchase_order_expenses.length > 0,
      expenseOrderCount: order.purchase_order_expenses
        ? order.purchase_order_expenses.length
        : 0,
      // 原订单信息对象
      originOrderInfo: order.original_order
        ? {
          orderStatus: order.original_order.order_status,
          paymentStatus: order.original_order.payment_status,
          shippingStatus: order.original_order.shipping_status,
          invoiceStatus: order.original_order.invoice_status,
          orderSource: order.original_order.order_source,
          orderType: order.original_order.order_type,
          createdAt: order.original_order.created_at,
          updatedAt: order.original_order.updated_at,
        }
        : null,
      // 货期时间
      expectedDeliveryTime: order.expected_delivery_time,
      deliveryTime: order.expected_delivery_time
        ? order.expected_delivery_time.getTime()
        : null,
    };

    // 调试日志：检查费用单数据
    if (
      order.purchase_order_expenses &&
      order.purchase_order_expenses.length > 0
    ) {
      console.log(
        `订单 ${order.purchase_order_number} 有 ${order.purchase_order_expenses.length} 个费用单`
      );
    }

    return transformedOrder;
  }

  /**
   * 获取采购状态文本
   * @param {number} status - 采购状态
   * @returns {string} - 状态文本
   */
  getPurchaseStatusText(status) {
    const statusMap = {
      0: "待处理",
      1: "已分配",
      2: "采购中",
      3: "已完成",
      4: "已取消",
    };
    return statusMap[status] || "未知状态";
  }

  /**
   * 获取订单状态文本
   * @param {number} status - 订单状态
   * @returns {string} - 状态文本
   */
  getOrderStatusText(status) {
    const statusMap = {
      0: "待发货",
      1: "发货未完成",
      2: "待收货",
      3: "已废止",
      4: "已关闭",
      5: "废止待确认",
    };
    return statusMap[status] || "未知状态";
  }

  /**
   * 获取支付状态文本
   * @param {number} status - 支付状态
   * @returns {string} - 状态文本
   */
  getPaymentStatusText(status) {
    const statusMap = {
      0: "未支付",
      1: "部分支付",
      2: "已支付",
      3: "退款中",
      4: "已退款",
    };
    return statusMap[status] || "未知状态";
  }

  /**
   * 获取配送状态文本
   * @param {number} status - 配送状态
   * @returns {string} - 状态文本
   */
  getShippingStatusText(status) {
    const statusMap = {
      0: "待发货",
      1: "部分发货",
      2: "已发货",
      3: "运输中",
      4: "已送达",
      5: "配送异常",
    };
    return statusMap[status] || "未知状态";
  }

  /**
   * 获取分离格式的采购订单详情（采购订单信息、原订单信息、关联数据分离）
   * @param {number} id - 采购订单ID
   * @returns {Promise<Object>} - 分离格式的采购订单详情
   */
  async getPurchaseOrderDetailSeparated(id) {
    try {
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          id: parseInt(id),
          deleted_at: null,
        },
        include: {
          purchase_order_items: {
            where: { deleted_at: null },
            orderBy: { created_at: "asc" },
          },
          purchase_order_attachments: {
            where: { deleted_at: null },
            orderBy: { created_at: "desc" },
          },
          purchase_order_logs: {
            orderBy: { created_at: "desc" },
            take: 100,
          },
          purchase_order_expenses: {
            include: {
              expense_items: true,
            },
            orderBy: { created_at: "desc" },
          },
        },
      });

      if (!purchaseOrder) {
        throw new Error("采购订单不存在");
      }

      // 获取原订单完整信息，包含所有关联数据
      const originalOrder = await this.orderModel.prisma.orders.findFirst({
        where: {
          id: purchaseOrder.original_order_id,
          deleted_at: null,
        },
        include: {
          // 订单项信息
          order_items: {
            orderBy: { created_at: "asc" },
          },
          // 配送信息
          order_shipping_info: true,
          // 渠道信息
          channel: {
            select: {
              id: true,
              name: true,
              icon_url: true,
              is_built_in: true,
            },
          },
          // 支付记录
          payment_records: {
            orderBy: { created_at: "desc" },
          },
        },
      });

      // 注意：platform_id 和 store_id 只是外键字段，没有直接的关联关系
      // 如果需要获取平台和店铺的详细信息，需要单独查询对应的表

      // 构建分离格式的数据
      return this.buildSeparatedOrderDetail(purchaseOrder, originalOrder);
    } catch (error) {
      console.error("获取分离格式采购订单详情失败:", error);
      throw error;
    }
  }

  /**
   * 根据ID获取采购订单详情
   * @param {number} id - 采购订单ID
   * @returns {Promise<Object>} - 采购订单详情
   */
  async getPurchaseOrderById(id) {
    try {
      const purchaseOrder = await this.prisma.purchase_order.findFirst({
        where: {
          id: parseInt(id),
          deleted_at: null,
        },
        include: {
          purchase_order_items: {
            where: { deleted_at: null },
            orderBy: { created_at: "asc" },
            select: {
              // 基本信息
              id: true,
              purchase_order_id: true,
              original_order_item_id: true,

              // 商品信息
              goods_spu_id: true,
              goods_sku_id: true,
              spu_code_snapshot: true,
              spu_name_snapshot: true,
              product_name: true,
              product_code: true,
              sku_code: true,
              sku: true,
              sku_specifications: true,
              specification: true,
              product_image: true,

              // 价格信息
              unit_price: true,
              market_price_snapshot: true,
              cost_price_snapshot: true,
              purchase_price: true,
              supply_price: true,
              contract_price: true,

              // 成本信息
              actual_cost: true,
              inspection_cost: true,
              profit_rate: true,
              cost_total: true,

              // 数量信息
              quantity: true,
              purchased_quantity: true,
              received_quantity: true,
              split_quantity: true,
              available_split_quantity: true,

              // 金额信息
              total_price: true,
              total_amount: true,
              item_paid_amount: true,
              purchase_total: true,

              // 第三方信息
              third_party_spu_id: true,
              third_party_sku_id: true,
              third_party_product_code: true,
              third_party_item_snapshot: true,

              // 供应商信息
              suggested_supplier_id: true,
              suggested_supplier_name: true,
              actual_supplier_id: true,
              actual_supplier_name: true,

              // 税收分类
              tax_category: true,

              // 物流信息
              weight_snapshot: true,
              volume_snapshot: true,
              shipping_info: true,

              // 状态信息
              item_status: true,
              purchase_progress: true,

              // 时间信息
              expected_delivery_time: true,
              actual_delivery_time: true,
              expiry_time: true,
              product_line_expire_time: true,

              // 备注信息
              item_remark: true,
              loss_reason: true,

              // 系统字段
              created_at: true,
              updated_at: true,
            },
          },
          purchase_order_attachments: {
            where: { deleted_at: null },
            orderBy: { created_at: "desc" },
          },
          purchase_order_logs: {
            orderBy: { created_at: "desc" },
            take: 100, // 限制日志数量，避免数据过大
          },
          purchase_order_expenses: {
            include: {
              expense_items: true,
            },
            orderBy: { created_at: "desc" },
          },
        },
      });

      if (!purchaseOrder) {
        throw new Error("采购订单不存在");
      }

      // 转换为详情页面需要的数据格式
      return this.transformOrderDetailForDisplay(purchaseOrder);
    } catch (error) {
      console.error("获取采购订单详情失败:", error);
      throw error;
    }
  }

  /**
   * 构建分离格式的订单详情数据
   * @param {Object} purchaseOrder - 采购订单数据
   * @param {Object} originalOrder - 原订单数据
   * @returns {Object} - 分离格式的订单详情数据
   */
  buildSeparatedOrderDetail(purchaseOrder, originalOrder) {
    try {
      // 1. 采购订单信息（排除原订单相关字段）
      const purchaseOrderInfo = {
        // 基本信息
        id: purchaseOrder.id,
        purchaseOrderNumber: purchaseOrder.purchase_order_number,
        thirdPartyOrderSn: purchaseOrder.third_party_order_sn,

        // 采购相关信息
        purchaserId: purchaseOrder.purchaser_id,
        purchaserName: purchaseOrder.purchaser_name,
        supplierId: purchaseOrder.supplier_id,
        supplierName: purchaseOrder.supplier_name,
        supplierCode: purchaseOrder.supplier_code,

        // 订单基础信息
        orderSource: purchaseOrder.order_source,
        orderType: purchaseOrder.order_type,
        follower: purchaseOrder.follower,
        buyerAccount: purchaseOrder.buyer_account,
        deliveryAddress: purchaseOrder.delivery_address,

        // 状态信息
        purchaseStatus: purchaseOrder.purchase_status,
        erpStatus: purchaseOrder.erp_status,
        splitOrderStatus: purchaseOrder.split_order_status,
        splitStatus: purchaseOrder.split_status,
        auditStatus: purchaseOrder.audit_status,
        cancelStatus: purchaseOrder.cancel_status,
        pendingCancelStatus: purchaseOrder.pending_cancel_status,
        linkStatus: purchaseOrder.link_status,
        isLoss: purchaseOrder.is_loss,

        // 金额信息
        totalAmount: parseFloat(purchaseOrder.total_amount || 0),
        paidAmount: parseFloat(purchaseOrder.paid_amount || 0),
        discountAmount: parseFloat(purchaseOrder.discount_amount || 0),
        shippingFee: parseFloat(purchaseOrder.shipping_fee || 0),
        taxAmount: parseFloat(purchaseOrder.tax_amount || 0),
        purchaseCost: parseFloat(purchaseOrder.purchase_cost || 0),
        actualCost: parseFloat(purchaseOrder.actual_cost || 0),
        profitAmount: parseFloat(purchaseOrder.profit_amount || 0),
        profitRate: purchaseOrder.profit_rate,

        // 数量信息
        totalQuantity: purchaseOrder.total_quantity || 0,
        purchasedQuantity: purchaseOrder.purchased_quantity || 0,
        receivedQuantity: purchaseOrder.received_quantity || 0,
        splitCount: purchaseOrder.split_count || 0,

        // 渠道信息
        channelId: purchaseOrder.channel_id,
        channelName: purchaseOrder.channel_name,
        platformId: purchaseOrder.platform_id,
        platformName: purchaseOrder.platform_name,
        storeId: purchaseOrder.store_id,
        storeName: purchaseOrder.store_name,

        // 时间信息
        originalOrderTime: purchaseOrder.original_order_time,
        purchaseTime: purchaseOrder.purchase_time,
        purchaseApplyTime: purchaseOrder.purchase_apply_time,
        purchaseAssignTime: purchaseOrder.purchase_assign_time,
        purchaseCompleteTime: purchaseOrder.purchase_complete_time,
        expectedDeliveryTime: purchaseOrder.expected_delivery_time,
        actualDeliveryTime: purchaseOrder.actual_delivery_time,

        // 备注信息
        purchaseRemark: purchaseOrder.purchase_remark,
        orderRemark: purchaseOrder.order_remark,
        customerRemark: purchaseOrder.customer_remark,
        businessRemark: purchaseOrder.business_remark,

        // 收货信息
        recipientName: purchaseOrder.recipient_name,
        recipientPhone: purchaseOrder.recipient_phone,
        recipientAddress: purchaseOrder.recipient_address,
        actualReceiver: purchaseOrder.actual_receiver,
        actualPhone: purchaseOrder.actual_phone,
        actualAddress: purchaseOrder.actual_address,

        // 物流信息
        logisticsInfo: purchaseOrder.logistics_info,
        logisticsNumber: purchaseOrder.logistics_number,

        // 系统字段
        createdAt: purchaseOrder.created_at,
        updatedAt: purchaseOrder.updated_at,
      };

      // 2. 原订单信息
      const originalOrderInfo = originalOrder
        ? {
          // 基本信息
          id: originalOrder.id,
          orderSn: originalOrder.order_sn,
          userId: originalOrder.user_id,
          thirdPartyPlatformId: originalOrder.third_party_platform_id,
          thirdPartyOrderSn: originalOrder.third_party_order_sn,

          // 订单状态信息
          orderStatus: originalOrder.order_status,
          orderStatusText: this.getOrderStatusText(
            originalOrder.order_status
          ),
          paymentStatus: originalOrder.payment_status,
          paymentStatusText: this.getPaymentStatusText(
            originalOrder.payment_status
          ),
          shippingStatus: originalOrder.shipping_status,
          shippingStatusText: this.getShippingStatusText(
            originalOrder.shipping_status
          ),
          invoiceStatus: originalOrder.invoice_status,

          // 订单类型和来源
          orderType: originalOrder.order_type,
          orderTypeText: OrderTypeEnum.getTypeText(originalOrder.order_type),
          orderSource: originalOrder.order_source,
          orderSourceText: OrderSourceEnum.getSourceText(
            originalOrder.order_source
          ),

          // 渠道信息
          channelId: originalOrder.channel_id,
          channelName: originalOrder.channel?.name || null,
          channelIconUrl: originalOrder.channel?.icon_url || null,
          channelIsBuiltIn: originalOrder.channel?.is_built_in || null,

          // 平台信息（仅ID，名称需要单独查询）
          platformId: originalOrder.platform_id,
          platformName: null, // 需要单独查询平台表获取
          platformCode: null,

          // 店铺信息（仅ID，名称需要单独查询）
          storeId: originalOrder.store_id,
          storeName: null, // 需要单独查询店铺表获取
          storeCode: null,

          // 订单编号和时间
          orderNumber: originalOrder.order_sn,
          orderTime: originalOrder.created_at,
          createdAt: originalOrder.created_at,
          updatedAt: originalOrder.updated_at,

          // 收货人信息
          recipientInfo: originalOrder.order_shipping_info
            ? {
              recipientName:
                originalOrder.order_shipping_info.recipient_name,
              recipientPhone:
                originalOrder.order_shipping_info.recipient_phone,
              regionProvinceId:
                originalOrder.order_shipping_info.region_province_id,
              regionCityId:
                originalOrder.order_shipping_info.region_city_id,
              regionDistrictId:
                originalOrder.order_shipping_info.region_district_id,
              regionPathName:
                originalOrder.order_shipping_info.region_path_name,
              streetAddress:
                originalOrder.order_shipping_info.street_address,
              postalCode: originalOrder.order_shipping_info.postal_code,
              fullAddress: `${originalOrder.order_shipping_info.region_path_name || ""
                } ${originalOrder.order_shipping_info.street_address || ""
                }`.trim(),
            }
            : null,

          // 付款信息
          paymentInfo: {
            paymentMethodId: originalOrder.payment_method_id,
            paymentMethod: originalOrder.payment_method,
            totalAmount: parseFloat(originalOrder.total_amount || 0),
            totalProductAmount: parseFloat(
              originalOrder.total_product_amount || 0
            ),
            paidAmount: parseFloat(originalOrder.paid_amount || 0),
            discountAmount: parseFloat(originalOrder.discount_amount || 0),
            shippingFee: parseFloat(originalOrder.shipping_fee || 0),
            taxAmount: parseFloat(originalOrder.tax_amount || 0),
            unpaidAmount:
              parseFloat(originalOrder.total_amount || 0) -
              parseFloat(originalOrder.paid_amount || 0),
            paymentRecords: (originalOrder.payment_records || []).map(
              (record) => ({
                id: record.id,
                paymentMethod: record.payment_method,
                paymentAmount: parseFloat(record.payment_amount || 0),
                paymentTime: record.payment_time,
                paymentStatus: record.payment_status,
                transactionId: record.transaction_id,
                remark: record.remark,
              })
            ),
          },

          // 订单备注信息
          remarkInfo: {
            orderRemark: originalOrder.remark,
            customerRemark: originalOrder.customer_remark || null,
            merchantRemark: originalOrder.merchant_remark || null,
            systemRemark: originalOrder.system_remark || null,
          },

          // 订单项信息
          orderItems: (originalOrder.order_items || []).map((item) => ({
            id: item.id,
            productName: item.product_name,
            productImage: item.product_image,
            unitPrice: parseFloat(item.unit_price || 0),
            quantity: item.quantity,
            totalPrice: parseFloat(item.total_price || 0),
            skuCode: item.sku_code,
            skuSpecifications: item.sku_specifications,
            goodsSpuId: item.goods_spu_id,
            goodsSkuId: item.goods_sku_id,
            thirdPartySpuId: item.third_party_spu_id,
            thirdPartySkuId: item.third_party_sku_id,
            thirdPartyProductCode: item.third_party_product_code,
            spuNameSnapshot: item.spu_name_snapshot,
            itemPaidAmount: parseFloat(item.item_paid_amount || 0),
          })),

          // 完整的配送信息
          shippingInfo: originalOrder.order_shipping_info,

          // 完整的渠道信息
          channel: originalOrder.channel,
        }
        : null;

      // 3. 关联数据
      const relatedData = {
        // 采购订单商品项
        purchaseOrderItems: this.transformPurchaseOrderItems(
          purchaseOrder.purchase_order_items || []
        ),

        // 附件信息
        attachments: (purchaseOrder.purchase_order_attachments || []).map(
          (att) => ({
            id: att.id,
            fileName: att.file_name,
            fileUrl: att.file_url,
            fileSize: att.file_size,
            fileType: att.file_type,
            uploadTime: att.created_at,
          })
        ),

        // 操作日志
        operationLogs: (purchaseOrder.purchase_order_logs || []).map((log) => ({
          id: log.id,
          operationType: log.operation_type,
          operationContent: log.operation_content,
          operatorId: log.operator_id,
          operatorName: log.operator_name,
          operationTime: log.created_at,
          remark: log.remark || "",
        })),

        // 费用单信息
        expenseOrders: (purchaseOrder.purchase_order_expenses || []).map(
          (expense) => ({
            id: expense.id,
            expenseNumber: expense.expense_number,
            expenseType: expense.expense_type,
            expenseTypeName: expense.expense_type_name,
            expenseAmount: parseFloat(expense.expense_amount || 0),
            expenseItems: expense.expense_items || [],
            createdAt: expense.created_at,
          })
        ),
      };

      return {
        purchaseOrderInfo,
        originalOrderInfo,
        relatedData,
      };
    } catch (error) {
      console.error("构建分离格式订单详情数据失败:", error);
      throw error;
    }
  }

  /**
   * 转换采购订单商品项数据
   * @param {Array} items - 采购订单商品项数组
   * @returns {Array} - 转换后的商品项数组
   */
  transformPurchaseOrderItems(items) {
    return items.map((item) => ({
      // 基本信息
      id: item.id,
      purchaseOrderId: item.purchase_order_id,
      originalOrderItemId: item.original_order_item_id,

      // 商品信息
      goodsSpuId: item.goods_spu_id,
      goodsSkuId: item.goods_sku_id,
      spuCodeSnapshot: item.spu_code_snapshot,
      spuNameSnapshot: item.spu_name_snapshot,
      productName: item.product_name,
      productCode: item.product_code,
      skuCode: item.sku_code,
      sku: item.sku,
      skuSpecifications: item.sku_specifications,
      specification: item.specification,
      productImage: item.product_image,

      // 价格信息
      unitPrice: parseFloat(item.unit_price || 0),
      marketPriceSnapshot: item.market_price_snapshot
        ? parseFloat(item.market_price_snapshot)
        : null,
      costPriceSnapshot: item.cost_price_snapshot
        ? parseFloat(item.cost_price_snapshot)
        : null,
      purchasePrice: item.purchase_price
        ? parseFloat(item.purchase_price)
        : null,
      supplyPrice: item.supply_price ? parseFloat(item.supply_price) : null,
      contractPrice: item.contract_price
        ? parseFloat(item.contract_price)
        : null,

      // 成本信息
      actualCost: parseFloat(item.actual_cost || 0),
      inspectionCost: parseFloat(item.inspection_cost || 0),
      profitRate: item.profit_rate,
      costTotal: parseFloat(item.cost_total || 0),

      // 数量信息
      quantity: item.quantity || 0,
      purchasedQuantity: item.purchased_quantity || 0,
      receivedQuantity: item.received_quantity || 0,
      splitQuantity: item.split_quantity || 0,
      availableSplitQuantity: item.available_split_quantity || 0,

      // 金额信息
      totalPrice: parseFloat(item.total_price || 0),
      totalAmount: parseFloat(item.total_amount || 0),
      itemPaidAmount: parseFloat(item.item_paid_amount || 0),
      purchaseTotal: item.purchase_total
        ? parseFloat(item.purchase_total)
        : null,

      // 第三方信息
      thirdPartySpuId: item.third_party_spu_id,
      thirdPartySkuId: item.third_party_sku_id,
      thirdPartyProductCode: item.third_party_product_code,
      thirdPartyItemSnapshot: item.third_party_item_snapshot,

      // 供应商信息
      suggestedSupplierId: item.suggested_supplier_id,
      suggestedSupplierName: item.suggested_supplier_name,
      actualSupplierId: item.actual_supplier_id,
      actualSupplierName: item.actual_supplier_name,

      // 税收分类
      taxCategory: item.tax_category,

      // 物流信息
      weightSnapshot: item.weight_snapshot,
      volumeSnapshot: item.volume_snapshot,
      shippingInfo: item.shipping_info,

      // 状态信息
      itemStatus: item.item_status,
      purchaseProgress: item.purchase_progress,

      // 时间信息
      expectedDeliveryTime: item.expected_delivery_time,
      actualDeliveryTime: item.actual_delivery_time,
      expiryTime: item.expiry_time,
      productLineExpireTime: item.product_line_expire_time,

      // 备注信息
      itemRemark: item.item_remark,
      lossReason: item.loss_reason,

      // 系统字段
      createdAt: item.created_at,
      updatedAt: item.updated_at,
    }));
  }

  /**
   * 转换订单详情数据格式以匹配详情页面显示需求
   * @param {Object} order - 原始订单数据
   * @returns {Object} - 转换后的订单详情数据
   */
  async transformOrderDetailForDisplay(order) {
    try {
      // 计算商品总数量和总金额
      const totalQuantity =
        order.purchase_order_items?.reduce(
          (sum, item) => sum + (item.quantity || 0),
          0
        ) || 0;
      const totalAmount = parseFloat(order.total_amount || 0);
      const costTotal = parseFloat(order.purchase_cost || 0);

      // 计算毛利率
      let grossProfitRate = "0.0%";
      if (totalAmount > 0 && costTotal > 0) {
        const profitRate = (
          ((totalAmount - costTotal) / totalAmount) *
          100
        ).toFixed(1);
        grossProfitRate = `${profitRate}%`;
      }

      // 转换商品数据 - 包含所有字段
      const products =
        order.purchase_order_items?.map((item) => ({
          // 基本信息
          id: item.id,
          purchaseOrderId: item.purchase_order_id,
          originalOrderItemId: item.original_order_item_id,

          // 商品信息
          goodsSpuId: item.goods_spu_id,
          goodsSkuId: item.goods_sku_id,
          spuCodeSnapshot: item.spu_code_snapshot,
          spuNameSnapshot: item.spu_name_snapshot,
          productName: item.product_name || item.spu_name_snapshot,
          productCode: item.product_code || item.spu_code_snapshot,
          skuCode: item.sku_code,
          sku: item.sku || item.sku_code,
          skuSpecifications: item.sku_specifications,
          specification:
            item.specification ||
            this.getSpecificationText(item.sku_specifications),
          productImage:
            item.product_image || "https://via.placeholder.com/120x120",

          // 价格信息
          unitPrice: parseFloat(item.unit_price || 0).toFixed(2),
          marketPriceSnapshot: item.market_price_snapshot
            ? parseFloat(item.market_price_snapshot).toFixed(2)
            : null,
          costPriceSnapshot: item.cost_price_snapshot
            ? parseFloat(item.cost_price_snapshot).toFixed(2)
            : null,
          purchasePrice: item.purchase_price
            ? parseFloat(item.purchase_price).toFixed(2)
            : null,
          supplyPrice: item.supply_price
            ? parseFloat(item.supply_price).toFixed(2)
            : null,
          contractPrice: item.contract_price
            ? parseFloat(item.contract_price).toFixed(2)
            : null,

          // 成本信息
          actualCost: parseFloat(item.actual_cost || 0).toFixed(2),
          inspectionCost: parseFloat(item.inspection_cost || 0).toFixed(2),
          profitRate: item.profit_rate || this.calculateItemProfitRate(item),
          costTotal: parseFloat(
            item.cost_total || item.actual_cost || 0
          ).toFixed(2),

          // 数量信息
          quantity: item.quantity || 0,
          purchasedQuantity: item.purchased_quantity || 0,
          receivedQuantity: item.received_quantity || 0,
          splitQuantity: item.split_quantity || 0,
          availableSplitQuantity: item.available_split_quantity || 0,

          // 金额信息
          totalPrice: parseFloat(item.total_price || 0).toFixed(2),
          totalAmount: parseFloat(
            item.total_amount || item.total_price || 0
          ).toFixed(2),
          itemPaidAmount: parseFloat(item.item_paid_amount || 0).toFixed(2),
          purchaseTotal: item.purchase_total
            ? parseFloat(item.purchase_total).toFixed(2)
            : null,

          // 第三方信息
          thirdPartySpuId: item.third_party_spu_id,
          thirdPartySkuId: item.third_party_sku_id,
          thirdPartyProductCode: item.third_party_product_code,
          thirdPartyItemSnapshot: item.third_party_item_snapshot,

          // 供应商信息
          suggestedSupplierId: item.suggested_supplier_id,
          suggestedSupplier: item.suggested_supplier_name,
          actualSupplierId: item.actual_supplier_id,
          actualSupplier: item.actual_supplier_name,

          // 税收分类
          taxCategory: item.tax_category || "未分类",

          // 物流信息
          weightSnapshot: item.weight_snapshot,
          volumeSnapshot: item.volume_snapshot,
          shippingInfo: item.shipping_info,

          // 状态信息
          itemStatus: item.item_status,
          purchaseProgress: item.purchase_progress || null,

          // 时间信息
          expectedDeliveryTime: item.expected_delivery_time,
          actualDeliveryTime: item.actual_delivery_time,
          expiryTime: item.expiry_time,
          productLineExpireTime: item.product_line_expire_time,

          // 备注信息
          itemRemark: item.item_remark,
          lossReason: item.loss_reason,

          // 系统字段
          createdAt: item.created_at,
          updatedAt: item.updated_at,
        })) || [];

      // 构建物流信息
      const logisticsData = await this.buildLogisticsData(order);

      // 构建信息记录
      const recordsData = this.buildRecordsData(order);

      // 构建订单流转记录
      const flowRecords = this.buildFlowRecords(
        order.purchase_order_logs || []
      );

      // 构建操作日志
      const operationLogs = this.buildOperationLogs(
        order.purchase_order_logs || []
      );

      return {
        // 基本信息
        id: order.id,
        orderNumber: order.purchase_order_number,
        originalOrderNumber: order.original_order_number,
        purchaseTime: order.purchase_time,
        orderTime: order.original_order_time,
        orderSource: order.order_source || "商城",
        storeName: order.store_name,
        deliveryAddress: order.delivery_address,
        follower: order.follower,
        purchaser: order.purchaser_name,
        orderType: order.order_type || "商城自然单",

        // 渠道信息
        channelId: order.channel_id ? order.channel_id.toString() : null,
        channelName: order.channel_name || null,

        // 商品信息
        products: products,
        freight: parseFloat(order.freight || order.shipping_fee || 0).toFixed(
          2
        ),
        totalQuantity: totalQuantity,
        totalAmount: totalAmount.toFixed(2),
        grossProfitRate: grossProfitRate,
        costTotal: costTotal.toFixed(2),
        buyerAccount: order.buyer_account,
        businessRemark: order.business_remark,

        // 物流信息
        logisticsInfo: order.logistics_info,
        logisticsNumber: order.logistics_number,
        purchaseProgress: products[0]?.purchaseProgress || "待处理",
        logisticsData: logisticsData,

        // 供应商信息（从商品维度获取第一个商品的供应商信息作为示例）
        actualSupplier: products[0]?.actualSupplier || null,
        suggestedSupplier: products[0]?.suggestedSupplier || null,

        // 状态信息
        orderStatus: getOrderStatusText(order.order_status),
        auditStatus: getAuditStatusText(order.audit_status),
        erpStatus: getErpStatusText(order.erp_status),
        splitOrderStatus: getSplitOrderStatusText(order.split_order_status),
        linkStatus: getLinkStatusText(order.link_status),

        // 信息记录
        logisticsRecords: recordsData.logisticsRecords,
        receiptRecords: recordsData.receiptRecords,
        delayRecords: recordsData.delayRecords,

        // 订单流转记录
        flowRecords: flowRecords,

        // 操作日志
        operationLogs: operationLogs,

        // 备注信息
        purchaseRemark: order.purchase_remark,
        orderRemark: order.order_remark,
        customerRemark: order.customer_remark,

        // 收货信息
        recipientName: order.recipient_name,
        recipientPhone: order.recipient_phone,
        recipientAddress: order.recipient_address,
        actualReceiver: order.actual_receiver,
        actualPhone: order.actual_phone,
        actualAddress: order.actual_address,

        // 附件信息
        attachments:
          order.purchase_order_attachments?.map((att) => ({
            id: att.id,
            name: att.file_name,
            url: att.file_url,
            size: att.file_size,
            type: att.file_type,
            uploadTime: att.created_at,
          })) || [],
      };
    } catch (error) {
      console.error("转换订单详情数据失败:", error);
      throw error;
    }
  }
  /**
   * 计算商品项毛利率
   * @param {Object} item - 商品项数据
   * @returns {string} - 毛利率字符串
   */
  calculateItemProfitRate(item) {
    const totalAmount = parseFloat(item.total_amount || item.total_price || 0);
    const actualCost = parseFloat(item.actual_cost || 0);

    if (totalAmount > 0 && actualCost > 0) {
      const profitRate = (
        ((totalAmount - actualCost) / totalAmount) *
        100
      ).toFixed(1);
      return `${profitRate}%`;
    }

    return "0.0%";
  }

  /**
   * 构建物流数据
   * @param {Object} order - 订单数据
   * @returns {Object} - 物流数据
   */
  async buildLogisticsData(order) {
    // 这里可以根据实际需求从其他表获取物流信息
    // 目前返回模拟数据结构，实际项目中需要根据业务需求调整
    return {
      // 平台物流
      platform: {
        type: "platform",
        typeName: "平台物流",
        shippingCompany: order.logistics_info || "顺丰快递",
        trackingNumber: order.logistics_number || "",
        shippingStatus: this.getShippingStatusFromOrderStatus(
          order.order_status
        ),
        shippedAt: order.purchase_time,
        estimatedDelivery: null,
        trackingData: [],
      },
      // 拆分单物流（如果有拆分）
      split: [],
      // 补发物流（如果有补发）
      reissue: [],
    };
  }

  /**
   * 根据订单状态获取发货状态
   * @param {number} orderStatus - 订单状态
   * @returns {string} - 发货状态
   */
  getShippingStatusFromOrderStatus(orderStatus) {
    const statusMap = {
      0: "待发货",
      1: "发货未完成",
      2: "待收货",
      3: "已废止",
      4: "已关闭",
      5: "废止待确认",
    };
    return statusMap[orderStatus] || "待发货";
  }

  /**
   * 构建信息记录数据
   * @param {Object} order - 订单数据
   * @returns {Object} - 信息记录数据
   */
  buildRecordsData(order) {
    return {
      logisticsRecords: [
        // 可以从物流相关表获取真实数据
        // 目前返回基础结构
      ],
      receiptRecords: [
        // 可以从签收记录表获取真实数据
      ],
      delayRecords: [
        // 可以从延迟记录表获取真实数据
      ],
    };
  }

  /**
   * 构建订单流转记录
   * @param {Array} logs - 日志数据
   * @returns {Array} - 流转记录
   */
  buildFlowRecords(logs) {
    return logs
      .filter(
        (log) =>
          log.action_type &&
          [
            "create",
            "assign",
            "update",
            "change_purchaser",
            "update_progress",
          ].includes(log.action_type)
      )
      .map((log) => ({
        title: this.getFlowRecordTitle(log.action_type, log.action_description),
        createTime: this.formatDateTime(log.created_at),
        status: "success", // 可以根据实际情况调整
        operator: log.operator_name,
        receiver: log.details?.newPurchaser || log.details?.receiver || "",
        result: log.status_description || "",
        remark: log.remark || "",
        description: log.details?.description || "",
      }));
  }

  /**
   * 获取流转记录标题
   * @param {string} actionType - 操作类型
   * @param {string} actionDescription - 操作描述
   * @returns {string} - 标题
   */
  getFlowRecordTitle(actionType, actionDescription) {
    const titleMap = {
      create: "订单录入",
      assign: "订单指派",
      update: "状态更新",
      change_purchaser: "更换采购员",
      update_progress: "进度更新",
    };
    return titleMap[actionType] || actionDescription || "操作记录";
  }

  /**
   * 构建操作日志
   * @param {Array} logs - 日志数据
   * @returns {Array} - 操作日志
   */
  buildOperationLogs(logs) {
    return logs.map((log) => ({
      action: log.action_description || "操作记录",
      createTime: this.formatDateTime(log.created_at),
      operator: log.operator_name || "系统",
      module: this.getModuleFromActionType(log.action_type),
      description: this.buildLogDescription(log),
    }));
  }

  /**
   * 根据操作类型获取模块名称
   * @param {string} actionType - 操作类型
   * @returns {string} - 模块名称
   */
  getModuleFromActionType(actionType) {
    const moduleMap = {
      create: "订单管理",
      update: "订单管理",
      change_purchaser: "采购管理",
      update_remark: "备注管理",
      update_progress: "进度管理",
      set_cost_total: "成本管理",
      set_item_supplier: "供应商管理",
      update_tax_classification: "税务管理",
      save_loss_reason: "风险管理",
    };
    return moduleMap[actionType] || "系统管理";
  }

  /**
   * 构建日志描述
   * @param {Object} log - 日志对象
   * @returns {string} - 描述文本
   */
  buildLogDescription(log) {
    if (log.remark) {
      return log.remark;
    }

    if (log.status_description) {
      return log.status_description;
    }

    return log.action_description || "执行了操作";
  }

  /**
   * 格式化日期时间
   * @param {Date} date - 日期对象
   * @returns {string} - 格式化后的日期时间字符串
   */
  formatDateTime(date) {
    if (!date) return "";

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    const hours = String(d.getHours()).padStart(2, "0");
    const minutes = String(d.getMinutes()).padStart(2, "0");
    const seconds = String(d.getSeconds()).padStart(2, "0");

    return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
  }
}

module.exports = PurchaseOrderService;
