-- 初始化配送方式数据
-- 插入通用配送方式（channel_id为NULL表示所有渠道都可用）

INSERT INTO csm.csm_delivery_method (method_name, method_code, description, channel_id, is_enabled) VALUES
('京东大件物流', 'JD_LARGE', '适用于大件商品的物流配送', NULL, 1),
('京东快运', 'JD_EXPRESS', '京东快运物流服务', NULL, 1),
('京东快递', 'JD_COURIER', '京东快递配送服务', NULL, 1),
('德邦物流', 'DEPPON', '德邦物流配送服务', NULL, 1),
('德邦快递', 'DEPPON_EXPRESS', '德邦快递配送服务', NULL, 1),
('德邦快运', 'DEPPON_FREIGHT', '德邦快运物流服务', NULL, 1),
('百世快递', 'BEST_EXPRESS', '百世快递配送服务', NULL, 1),
('顺丰速运', 'SF_EXPRESS', '顺丰快递配送服务', NULL, 1),
('圆通速递', 'YTO_EXPRESS', '圆通快递配送服务', NULL, 1),
('中通快递', 'ZTO_EXPRESS', '中通快递配送服务', NULL, 1),
('申通快递', 'STO_EXPRESS', '申通快递配送服务', NULL, 1),
('韵达速递', 'YD_EXPRESS', '韵达快递配送服务', NULL, 1),
('天天快递', 'TT_EXPRESS', '天天快递配送服务', NULL, 1),
('宅急送', 'ZJS_EXPRESS', '宅急送配送服务', NULL, 1),
('邮政EMS', 'EMS_EXPRESS', '中国邮政EMS配送服务', NULL, 1),
('安能物流', 'ANE_EXPRESS', '安能物流配送服务', NULL, 1),
('壹米滴答', 'YMDD_EXPRESS', '壹米滴答物流服务', NULL, 1),
('优速快递', 'UC_EXPRESS', '优速快递配送服务', NULL, 1),
('国通快递', 'GTO_EXPRESS', '国通快递配送服务', NULL, 1),
('其他', 'OTHER', '其他配送方式', NULL, 1);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_delivery_method_enabled ON csm.csm_delivery_method(is_enabled);
CREATE INDEX IF NOT EXISTS idx_delivery_method_code ON csm.csm_delivery_method(method_code);
