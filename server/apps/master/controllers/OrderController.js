/**
 * 订单控制器
 * 负责处理订单相关的请求和响应
 */
const BaseController = require('../../../core/controllers/BaseController');
const OrderService = require('../services/OrderService');
const ManualOrderService = require('../services/ManualOrderService');
const OrderDto = require('../dto/OrderDto');
const OrderFollowerDto = require('../dto/OrderFollowerDto');
const GoodsSkuModel = require('../models/GoodsSkuModel');
const StoreService = require('../platformManagement/store/services/StoreService');
const PurchaseOrderService = require('../csm/services/PurchaseOrderService');
const { prisma } = require('../../../core/database/prisma');
class OrderController extends BaseController {

  /**
   * 构造函数
   */
  constructor() {
    super();
    this.orderService = new OrderService();
    this.manualOrderService = new ManualOrderService();
    this.purchaseOrderService = new PurchaseOrderService(prisma);

    // 初始化Prisma客户端
    // const prismaManager = require('../../../core/prisma');
    // this.prisma = prismaManager.getClient('base');
    this.prisma = prisma;
  }

  /**
   * 获取订单列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getOrders(req, res) {
    try {
      // 获取查询参数
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      const sortField = req.query.sortField || 'created_at';
      const sortOrder = req.query.sortOrder || 'desc';

      // 构建过滤条件
      const filters = {};
      const shippingFilters = {}; // 收货信息过滤条件
      const itemFilters = {}; // 订单项过滤条件

      // 内部订单编号筛选
      if (req.query.orderNumber) {
        try {
          // 将订单号转换为BigInt类型
          filters.id = BigInt(req.query.orderNumber);
        } catch (error) {
          console.error('订单号格式无效:', req.query.orderNumber, error.message);
          // 如果转换失败，设置一个不可能存在的ID，确保查询返回空结果
          filters.id = BigInt(0);
        }
      }

      // 三方订单编号筛选
      if (req.query.thirdPartyOrderSn) {
        filters.third_party_order_sn = { contains: req.query.thirdPartyOrderSn };
      }

      // 订单状态筛选
      if (req.query.orderStatus) {
        filters.order_status = parseInt(req.query.orderStatus);
      }

      // 支付状态筛选
      if (req.query.paymentStatus) {
        filters.payment_status = parseInt(req.query.paymentStatus);
      }

      // 配送状态筛选
      if (req.query.shippingStatus) {
        filters.shipping_status = parseInt(req.query.shippingStatus);
      }

      // 支付方式筛选 - 将paymentMethod参数视为支付方式ID
      if (req.query.paymentMethod && !isNaN(parseInt(req.query.paymentMethod))) {
        filters.payment_method_id = parseInt(req.query.paymentMethod);
      }

      // 支付方式ID筛选
      if (req.query.paymentMethodId) {
        filters.payment_method_id = parseInt(req.query.paymentMethodId);
      }

      // 客户名筛选 (当前可能不在数据库中，保留为了兼容性)
      if (req.query.customerName) {
        filters.customer_name = { contains: req.query.customerName };
      }

      // 买家手机筛选
      if (req.query.buyerPhone) {
        shippingFilters.recipient_phone = { contains: req.query.buyerPhone };
      }

      // 收货地址筛选
      if (req.query.address) {
        shippingFilters.OR = [
          { street_address: { contains: req.query.address } },
          { region_path_name: { contains: req.query.address } }
        ];
      }

      // 订单类型筛选
      if (req.query.orderType) {
        filters.order_type = parseInt(req.query.orderType);
      }

      // 订单来源筛选
      if (req.query.orderSource) {
        filters.order_source = parseInt(req.query.orderSource);
      }

      // 第三方子实体ID筛选
      if (req.query.thirdPartySubEntityId) {
        filters.third_party_sub_entity_id = BigInt(req.query.thirdPartySubEntityId);
      }

      // 渠道ID筛选
      if (req.query.channelId) {
        try {
          filters.channel_id = BigInt(req.query.channelId);
        } catch (error) {
          console.error('渠道ID格式无效:', req.query.channelId, error.message);
          // 如果转换失败，设置一个不可能存在的ID，确保查询返回空结果
          filters.channel_id = BigInt(0);
        }
      }

      // 商品名称筛选
      if (req.query.productName) {
        itemFilters.product_name = { contains: req.query.productName };
      }

      // 时间范围筛选（通过毫秒时间戳）
      // 优先使用特定的创建时间参数，其次是通用时间参数
      if (req.query.createStartTime || req.query.createEndTime) {
        // 创建订单时间范围筛选（毫秒时间戳）
        filters.created_at = {};

        if (req.query.createStartTime) {
          filters.created_at.gte = BigInt(req.query.createStartTime);
        }

        if (req.query.createEndTime) {
          filters.created_at.lte = BigInt(req.query.createEndTime);
        }
      } else if (req.query.startTime || req.query.endTime) {
        // 兼容原有的时间范围筛选
        filters.created_at = {};

        if (req.query.startTime) {
          filters.created_at.gte = BigInt(req.query.startTime);
        }

        if (req.query.endTime) {
          filters.created_at.lte = BigInt(req.query.endTime);
        }
      }

      // 跟单员关联筛选
      let followerFilter = null;
      if (req.query.hasFollower !== undefined) {
        // hasFollower: 'true' 或 '1' 表示有跟单员，'false' 或 '0' 表示无跟单员
        const hasFollower = req.query.hasFollower === 'true' || req.query.hasFollower === '1';
        followerFilter = hasFollower;
      }

      // 处理timeRange参数（recent6m: 近6个月）
      if (req.query.timeRange) {
        const now = Date.now(); // 当前毫秒时间戳
        const sixMonthsAgo = now - (6 * 30 * 24 * 60 * 60 * 1000); // 大约6个月前的毫秒时间戳
        const sixMonthsAgoBigInt = BigInt(sixMonthsAgo);

        if (req.query.timeRange === 'recent6m') {
          // 近6个月的订单
          if (!filters.AND) filters.AND = [];
          filters.AND.push({
            created_at: {
              gte: sixMonthsAgoBigInt
            }
          });
        } else if (req.query.timeRange === 'before6m') {
          // 6个月前的订单
          if (!filters.AND) filters.AND = [];
          filters.AND.push({
            created_at: {
              lt: sixMonthsAgoBigInt
            }
          });
        }
      }

      // 构建排序条件
      const orderBy = {};
      orderBy[sortField] = sortOrder;

      // 直接使用OrderModel查询数据
      const orderModel = this.orderService.orderModel;

      // 根据过滤条件查询订单
      // 注意：我们传递了主订单过滤条件，发货信息过滤条件、订单项过滤条件和跟单员筛选条件
      const result = await orderModel.getOrders(filters, page, pageSize, orderBy, shippingFilters, itemFilters, followerFilter);

      // 从结果中提取订单列表和总数
      const orders = result.items;
      const total = result.total;

      // 处理订单中的shipping信息，确保返回收货人和地址信息
      if (orders && orders.length > 0) {
        for (const order of orders) {
          if (order.shipping && order.shipping.id) {
            try {
              // 查询完整的收货信息
              const shippingInfo = await this.prisma.order_shipping_info.findUnique({
                where: { id: BigInt(order.shipping.id) }
              });

              if (shippingInfo) {
                // 获取发货方式文本描述
                const shippingMethodText = this.getShippingMethodText(shippingInfo.shipping_method);

                // 添加收货人信息和地址信息
                order.shipping = {
                  ...order.shipping,
                  recipientName: shippingInfo.recipient_name,
                  recipientPhone: shippingInfo.recipient_phone,
                  regionPathName: shippingInfo.region_path_name,
                  streetAddress: shippingInfo.street_address,
                  postalCode: shippingInfo.postal_code,
                  shippingMethod: shippingInfo.shipping_method,
                  shippingMethodText: shippingMethodText,
                  shippingCompanyName: shippingInfo.shipping_company_name,
                  trackingNumber: shippingInfo.tracking_number
                };
              }
            } catch (error) {
              console.error('获取订单收货信息失败:', error);
            }
          }
        }
      }

      // 为有采购订单号的订单获取采购员信息
      if (orders && orders.length > 0) {
        for (const order of orders) {
          if (order.purchaseOrderNumber) {
            try {
              // 调用采购订单服务获取采购员信息
              const purchaserInfo = await this.purchaseOrderService.getPurchaserByPurchaseOrderNumber(order.purchaseOrderNumber);
              if (purchaserInfo) {
                // 将采购员信息添加到订单对象中
                order.purchaserInfo = purchaserInfo;
              }
            } catch (error) {
              console.error(`获取采购员信息失败 (采购订单号: ${order.purchaseOrderNumber}):`, error);
              // 获取失败时不影响订单列表的正常返回，只记录错误日志
            }
          }
        }
      }

      // 构建标准分页响应格式
      const response = {
        items: orders || [],
        pageInfo: {
          total: total || 0,
          currentPage: page,
          totalPage: Math.ceil((total || 0) / pageSize)
        }
      };

      // 直接使用success方法返回数据
      this.success(res, response, '获取订单列表成功');
    } catch (error) {
      console.error('获取订单列表失败:', error);
      this.fail(res, '获取订单列表失败: ' + error.message);
    }
  }

  /**
   * 获取发货方式的文本描述
   * @param {number} methodCode - 发货方式代码
   * @returns {string} - 发货方式文本描述
   */
  getShippingMethodText(methodCode) {
    if (!methodCode) return '未设置';

    switch (parseInt(methodCode)) {
      case 1:
        return '快递物流';
      case 2:
        return '自定义物流';
      case 3:
        return '商家自送';
      case 4:
        return '线下自取';
      case 5:
        return '无需物流';
      default:
        return '未知方式';
    }
  }

  /**
   * 创建订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async createOrder(req, res) {
    try {
      // 验证请求数据，添加上下文标记 isThirdParty: false，表示这是普通订单接口
      const { error, value } = OrderDto.validateCreate(req.body, { context: { isThirdParty: false } });
      if (error) {
        return this.fail(res, '请求数据验证失败: ' + error.message, 400);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 401);
      }

      // 调用服务创建订单
      const result = await this.orderService.createOrder(value, currentUser);

      // 返回成功响应
      this.success(res, result.data, '订单创建成功', 200);
    } catch (error) {
      // 记录错误日志
      console.error('创建订单失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在') || error.message.includes('无效')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('库存不足')) {
        return this.fail(res, error.message, 400);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 第三方创建订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async createThirdPartyOrder(req, res) {
    try {
      // 使用专门的第三方订单验证方法，该方法已经设置了goodsSpuId和goodsSkuId为可选字段
      const { error, value } = OrderDto.validateThirdPartyCreate(req.body);
      if (error) {
        return this.fail(res, '请求数据验证失败: ' + error.message, 400);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 401);
      }

      // 如果传入了storeId但未传入platformId或channelId，则查询店铺信息并补全
      if (value.storeId && (!value.platformId || !value.channelId)) {
        try {
          console.log(`[订单创建] 传入storeId: ${value.storeId}，开始查询店铺关联的平台和渠道信息`);
          const storeResult = await StoreService.getStoreById(value.storeId);

          if (storeResult && storeResult.code === 200 && storeResult.data) {
            const storeData = storeResult.data;

            // 如果未传入platformId但店铺有关联平台，则使用店铺关联的平台ID
            if (!value.platformId && storeData.platformId) {
              value.platformId = storeData.platformId;
              console.log(`[订单创建] 自动补全platformId: ${value.platformId}`);
            }

            // 如果未传入channelId但店铺有关联渠道，则使用店铺关联的渠道ID
            if (!value.channelId && storeData.channelId) {
              value.channelId = storeData.channelId;
              console.log(`[订单创建] 自动补全channelId: ${value.channelId}`);
            }
          } else {
            console.warn(`[订单创建] 无法获取店铺信息或店铺不存在，storeId: ${value.storeId}`);
          }
        } catch (storeError) {
          console.error(`[订单创建] 查询店铺信息失败:`, storeError);
          // 查询店铺信息失败不阻止订单创建流程，继续使用传入的数据
        }
      }

      // 调用服务创建第三方订单
      const result = await this.orderService.createThirdPartyOrder(value, currentUser);

      // 返回成功响应
      this.success(res, result.data, '第三方订单创建成功', 200);
    } catch (error) {
      // 记录错误日志
      console.error('创建第三方订单失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在') || error.message.includes('无效')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('库存不足')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('渠道') || error.message.includes('渠道ID')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('商品信息')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('收货信息')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('已存在')) {
        return this.fail(res, error.message, 400); // 业务错误码400，HTTP状态码200
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取订单详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getOrderDetail(req, res) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 调用服务获取订单详情
      const order = await this.orderService.getOrderDetail(orderId);
      if (!order) {
        return this.fail(res, '订单不存在', 400);
      }

      // 返回成功响应
      this.success(res, order, '获取订单详情成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取订单详情失败', error);

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 订单发货
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async shipOrder(req, res) {
    try {
      // 确保订单ID以字符串形式传递，避免雪花ID精度问题
      const orderId = String(req.params.id);
      if (!orderId) {
        return this.fail(res, '订单ID不能为空');
      }

      // 验证请求数据
      const { error, value } = OrderDto.validateShippingUpdate(req.body);
      if (error) {
        return this.fail(res, '请求数据验证失败: ' + error.message);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 401);
      }

      // 调用服务更新订单物流信息并发货
      const result = await this.orderService.shipOrder(
        orderId,
        value,
        {
          id: currentUser.id,
          name: currentUser.username || currentUser.name,
          role: currentUser.role,
          ip: req.ip,
          platform: 'admin'
        }
      );

      // 返回成功响应
      this.success(res, result.data, '订单发货成功');
    } catch (error) {
      // 记录错误日志
      console.error('订单发货失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message);
      }
      if (error.message.includes('状态不允许') || error.message.includes('已发货')) {
        return this.fail(res, error.message);
      }

      // 返回通用错误响应
      this.fail(res, '订单发货失败: ' + error.message);
    }
  }

  /**
   * 取消订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async cancelOrder(req, res) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 验证请求数据
      const { error, value } = OrderDto.validateCancelOrder(req.body);
      if (error) {
        return this.fail(res, '请求数据验证失败: ' + error.message, 400);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 401);
      }

      // 调用服务取消订单
      const result = await this.orderService.cancelOrder(
        orderId,
        value.cancelReason,
        {
          id: currentUser.id,
          name: currentUser.username || currentUser.name,
          role: currentUser.role,
          ip: req.ip || '127.0.0.1',
          platform: 'admin'
        }
      );

      // 返回成功响应
      this.success(res, result.data, '订单取消成功');
    } catch (error) {
      // 记录错误日志
      console.error('取消订单失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('状态不允许') || error.message.includes('无法取消')) {
        return this.fail(res, error.message, 400);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 确认收货
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async confirmReceipt(req, res) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 400);
      }

      // 调用服务确认收货
      const result = await this.orderService.confirmReceipt(
        orderId,
        {
          id: currentUser.id,
          name: currentUser.username || currentUser.name,
          role: currentUser.role,
          ip: req.ip,
          platform: 'admin'
        }
      );

      // 返回成功响应
      this.success(res, result.data, '确认收货成功');
    } catch (error) {
      // 记录错误日志
      console.error('确认收货失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('状态不允许') || error.message.includes('未发货')) {
        return this.fail(res, error.message, 400);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 订单退款
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async refundOrder(req, res) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      const { refundReason, refundAmount, refundType } = req.body;

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 401);
      }

      // 验证退款参数
      if (refundType === 'partial' && (!refundAmount || refundAmount <= 0)) {
        return this.fail(res, '部分退款时退款金额必须大于0', 400);
      }

      // 调用支付记录服务处理退款
      const PaymentRecordService = require('../services/PaymentRecordService');
      const result = await PaymentRecordService.processOrderRefund(
        orderId,
        refundReason || '管理员申请退款',
        {
          id: currentUser.id,
          name: currentUser.username || currentUser.name,
          role: currentUser.role,
          ip: req.ip || '127.0.0.1',
          platform: 'admin'
        },
        refundAmount, // 传递退款金额
        refundType    // 传递退款类型
      );

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('订单退款失败:', error);
      this.fail(res, error.message || '订单退款失败', 500);
    }
  }

  /**
   * 模拟用户取消订单（无需用户验证）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async mockCancelOrder(req, res) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 验证请求数据
      const { error, value } = OrderDto.validateCancelOrder(req.body);
      if (error) {
        return this.fail(res, '请求数据验证失败: ' + error.message, 400);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 401);
      }

      // 调用服务取消订单
      const result = await this.orderService.cancelOrder(
        orderId,
        value.cancelReason,
        {
          id: currentUser.id,
          name: currentUser.username || currentUser.name,
          role: currentUser.role,
          ip: req.ip || '127.0.0.1',
          platform: 'admin'
        }
      );

      // 返回成功响应
      this.success(res, result.data, '订单取消成功');
    } catch (error) {
      // 记录错误日志
      console.error('取消订单失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('状态不允许')) {
        return this.fail(res, error.message, 400);
      }

      return this.fail(res, '取消订单失败: ' + error.message, 500);
    }
  }

  /**
   * 模拟用户确认收货（无需用户验证）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async mockConfirmReceipt(req, res) {
    try {
      const orderId = req.params.id;
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 400);
      }

      // 调用服务确认收货
      const result = await this.orderService.confirmReceipt(
        orderId,
        {
          id: currentUser.id,
          name: currentUser.username || currentUser.name,
          role: currentUser.role,
          ip: req.ip || '127.0.0.1',
          platform: 'admin'
        }
      );

      // 返回成功响应
      this.success(res, result.data, '确认收货成功');
    } catch (error) {
      // 记录错误日志
      console.error('确认收货失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 400);
      }
      if (error.message.includes('状态不允许') || error.message.includes('未发货')) {
        return this.fail(res, error.message, 400);
      }

      return this.fail(res, '确认收货失败: ' + error.message, 500);
    }
  }


  /**
   * 获取订单角标统计数据
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getOrderBadgeStats(req, res) {
    try {
      // 从请求中获取时间范围参数
      const { timeRange } = req.query;

      // 调用服务层方法获取订单角标统计，传入时间范围参数
      const badgeStats = await this.orderService.getOrderBadgeStats({ timeRange });

      // 返回成功响应
      this.success(res, badgeStats, '获取订单角标统计成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取订单角标统计失败:', error);

      // 返回错误响应
      this.error(res, '获取订单角标统计失败', error);
    }
  }

  /**
   * 添加订单标注（管理员备注）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async addOrderRemark(req, res) {
    try {
      // 获取订单ID
      const { id } = req.params;

      // 从请求体中获取标注内容
      const { remark } = req.body;

      // 验证请求参数
      if (!id) {
        return this.error(res, '订单ID不能为空');
      }

      if (!remark) {
        return this.error(res, '备注内容不能为空');
      }

      // 构造操作者信息
      const operator = {
        id: req.user?.id || 0,
        name: req.user?.username || '未知用户',
        role: req.user?.role || 'admin',
        ip: req.ip || '0.0.0.0',
        platform: 'web'
      };

      // 调用服务层方法添加订单标注
      const result = await this.orderService.addOrderRemark(id, remark, operator);

      // 返回响应
      if (result.success) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, result.code);
      }
    } catch (error) {
      // 记录错误日志
      console.error('添加订单标注失败:', error);

      // 返回错误响应
      this.fail(res, `添加订单标注失败：${error.message}`, 500);
    }
  }

  /**
   * 获取订单跟单员列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getOrderFollowers(req, res) {
    try {
      const orderId = req.params.id;

      // 调用服务层获取跟单员列表
      const result = await this.orderService.getOrderFollowers(orderId);

      // 返回响应
      if (result.success) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, result.code);
      }
    } catch (error) {

      return this.fail(res, `获取订单跟单员列表失败: ${error.message}`);
    }
  }

  /**
   * 批量添加订单跟单员
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async addOrderFollowers(req, res) {
    try {
      const orderId = req.params.id;
      const { followerIds } = req.body;

      // 验证请求数据
      const { error } = OrderFollowerDto.validateAddFollowers(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, null, 400);
      }

      // 获取当前操作用户信息
      const operator = {
        id: req.user?.id || 'system',
        name: req.user?.username || 'system',
        role: req.user?.role || 'system',
        ip: req.ip,
        platform: req.get('User-Agent') || 'Unknown'
      };

      // 调用服务层批量添加跟单员
      const result = await this.orderService.addOrderFollowers(orderId, followerIds, operator);

      // 返回响应
      if (result.success) {
        return this.success(res, result.message, result.data);
      } else {
        return this.fail(res, result.message, result.code);
      }
    } catch (error) {

      return this.fail(res, `批量添加订单跟单员失败: ${error.message}`);
    }
  }

  /**
   * 录入订单（手动录入）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async createManualOrder(req, res) {
    try {
      // 使用手动录单的专用验证方法
      const { error, value } = OrderDto.validateManualCreate(req.body);
      if (error) {
        return this.fail(res, '请求数据验证失败: ' + error.message, 400);
      }

      // 验证必填字段
      if (!value.channelId) {
        return this.fail(res, '渠道ID不能为空', 400);
      }

      if (!value.items || value.items.length === 0) {
        return this.fail(res, '订单商品信息不能为空', 400);
      }

      if (!value.shippingInfo) {
        return this.fail(res, '收货信息不能为空', 400);
      }

      // 获取当前用户信息
      const currentUser = req.user;
      if (!currentUser || !currentUser.id) {
        return this.fail(res, '未获取到用户信息', 401);
      }

      // 验证商品SKU编码并关联到系统商品
      const invalidSkuCodes = [];

      /**
       * 步骤1: 收集所有订单项中的skuId，准备批量查询
       */
      const skuIds = [];
      const duplicateSkuIds = [];
      const skuIdCounts = {};

      for (const item of value.items) {
        if (item.skuId) {
          const skuId = String(item.skuId);
          skuIds.push(skuId);

          // 检查是否有重复的 SKU ID
          if (skuIdCounts[skuId]) {
            skuIdCounts[skuId]++;
            if (!duplicateSkuIds.includes(skuId)) {
              duplicateSkuIds.push(skuId);
            }
          } else {
            skuIdCounts[skuId] = 1;
          }
        }
      }

      // 如果有重复的 SKU ID，返回错误
      if (duplicateSkuIds.length > 0) {
        return this.fail(res, `订单中存在重复的商品 SKU ID: ${duplicateSkuIds.join(', ')}，请调整数量而不是重复添加相同商品`, 400);
      }

      if (skuIds.length === 0) {
        return this.fail(res, '订单中没有有效的商品SKU ID', 400);
      }

      /**
       * 步骤2: 批量查询所有SKU信息
       * 使用批量查询接口一次性获取所有SKU的详细信息，减少数据库查询次数
       */
      let skuInfoMap = {};
      try {
        // 批量查询SKU信息
        const skuInfoList = await GoodsSkuModel.getSkusByIds(skuIds);

        // 将查询结果转换为以skuId为键的Map，方便后续快速查找
        skuInfoMap = skuInfoList.reduce((map, sku) => {
          map[sku.skuId] = sku;
          return map;
        }, {});

        // 检查是否有SKU未找到
        skuIds.forEach(skuId => {
          if (!skuInfoMap[skuId]) {
            invalidSkuCodes.push(skuId);
          }
        });
      } catch (error) {
        console.error('批量查询SKU信息失败:', error);
        return this.fail(res, `批量查询商品信息失败: ${error.message}`, 500);
      }

      // 如果存在无效的skuId，返回错误提示
      if (invalidSkuCodes.length > 0) {
        return this.fail(res, `以下商品SKU ID在系统中不存在，请先添加商品: ${invalidSkuCodes.join(', ')}`, 400);
      }

      /**
       * 步骤3: 处理订单项，使用批量查询的结果设置商品信息
       * 只处理 skuId、unitPrice 和 quantity 三个字段
       */
      for (let i = 0; i < value.items.length; i++) {
        const item = value.items[i];

        // 如果存在skuId，使用批量查询的结果设置商品信息
        if (item.skuId) {
          const skuId = String(item.skuId);
          const skuInfo = skuInfoMap[skuId];

          // 设置商品ID信息
          value.items[i].goodsSkuId = skuId;
          value.items[i].goodsSpuId = skuInfo.spuId;

          // 设置商品名称
          value.items[i].productName = skuInfo.spuName || '';

          // 设置商品规格信息
          value.items[i].skuSpecifications = skuInfo.skuName;

          // 设置商品图片
          value.items[i].productImage = skuInfo.imageUrl || '';

          // 确保单价是有效的数字
          if (typeof value.items[i].price !== 'number') {
            value.items[i].price = Number(value.items[i].price);
          }
        }
      }

      /**
       * 步骤4: 计算每个订单项的总价和订单总金额
       * 根据前端传递的单价和数量计算，不再使用系统中的销售价格
       */
      console.log('\n开始计算订单金额...');
      let orderTotalAmount = 0;

      for (let i = 0; i < value.items.length; i++) {
        const item = value.items[i];

        // 确保单价是有效的数字
        if (item.price === undefined || item.price === null || isNaN(Number(item.price))) {
          console.log(`订单项[${i}] 单价无效，设置为0`);
          item.price = 0;
        } else if (typeof item.price !== 'number') {
          item.price = Number(item.price);
        }

        // 确保数量是有效的数字
        if (!item.quantity || isNaN(Number(item.quantity)) || Number(item.quantity) <= 0) {
          console.log(`订单项[${i}] 数量无效，设置为1`);
          item.quantity = 1;
        } else if (typeof item.quantity !== 'number') {
          item.quantity = Number(item.quantity);
        }

        // 计算订单项总价 = 单价 * 数量
        item.totalPrice = item.price * item.quantity;

        // 累加到订单总金额
        orderTotalAmount += item.totalPrice;

        console.log(`订单项[${i}]: 商品=${item.productName}, 单价=${item.price}, 数量=${item.quantity}, 总价=${item.totalPrice}`);
      }

      // 设置订单总金额，覆盖前端传递的值
      value.totalAmount = orderTotalAmount;
      console.log(`订单总金额: ${value.totalAmount}`);
      console.log('订单金额计算完成\n');

      // 为录入订单添加特殊标记
      const orderData = {
        ...value,
        orderSource: 3, // 设置订单来源为手动录入
        orderType: value.orderType || 1, // 默认订单类型
        isManualInput: true // 标记为手动录入
      };

      // 注意：货到付款订单的特殊处理在 ManualOrderService 中完成
      // 如果选择货到付款（PaymentMethodEnum.COD），系统会自动将：
      // 1. 订单状态设置为 OrderStatusEnum.PENDING_SHIPMENT（待发货）
      // 2. 支付状态设置为 PaymentStatusEnum.PAID（已支付）
      // 3. 已支付金额设置为应付金额
      // 4. 支付时间设置为当前时间
      // 这样货到付款的订单可以直接进行发货操作

      // 调用专用的手动录单服务创建订单
      const result = await this.manualOrderService.createManualOrder(orderData, currentUser);

      // 返回成功响应
      this.success(res, result.data, '订单录入成功', 200);
    } catch (error) {
      // 记录错误日志
      console.error('录入订单失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在') || error.message.includes('无效')) {
        return this.fail(res, error.message, 400);
      }

      return this.fail(res, `录入订单失败: ${error.message}`, 500);
    }
  }

  /**
   * 查询物流轨迹
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async queryLogisticsTrack(req, res) {
    try {
      const { trackingNumber, companyCode } = req.body;

      if (!trackingNumber) {
        return this.fail(res, '快递单号不能为空', 400);
      }

      // 导入物流轨迹服务
      const ExpressTrackService = require('../system/integration/services/express/ExpressTrackService');
      const prismaManager = require('../../../core/prisma');
      const prisma = prismaManager.getClient('base');
      const expressTrackService = new ExpressTrackService(prisma);

      // 根据快递单号查询物流轨迹
      const result = await expressTrackService.getTrackByExpressNo(trackingNumber);

      if (result.success && result.data) {
        // 解析轨迹数据
        let trackData = [];
        if (result.data.track_data && result.data.track_data.data) {
          trackData = result.data.track_data.data.map(item => ({
            context: item.context || item.desc || '',
            time: item.time || item.ftime || '',
            location: item.location || ''
          }));
        }

        return this.success(res, trackData, '查询物流轨迹成功');
      } else {
        return this.fail(res, result.message || '未找到物流轨迹信息', 404);
      }
    } catch (error) {
      console.error('查询物流轨迹失败:', error);
      return this.fail(res, '查询物流轨迹失败: ' + error.message, 500);
    }
  }
}

module.exports = OrderController;
