/**
 * 直销模块申请认款API
 * 用于处理直销模块申请认款相关的前端接口调用
 */
import { request } from '~/utils/request'

// API基础URL
const apiBaseUrl = '/api'

export default {
  /**
   * 创建申请认款
   * @param {Object} data - 申请认款数据
   * @param {Array} data.orderIds - 订单ID列表
   * @param {number} data.totalAmount - 申请认款总金额
   * @param {string} data.paymentDate - 付款日期
   * @param {string} data.description - 备注说明（可选）
   * @param {Array} data.selectedVoucherIds - 选择的付款凭证ID列表（可选，支持多选）
   * @param {Array} data.attachments - 附件凭证列表（可选）
   * @returns {Promise} - 返回请求结果
   */
  createApplication(data) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/payment-recognition-application`,
      method: 'post',
      data
    })
  },

  /**
   * 获取申请认款列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，默认为1
   * @param {number} params.pageSize - 每页数量，默认为20
   * @param {number} params.status - 申请状态筛选（可选）：0-待审核，1-审核通过，2-审核驳回
   * @param {string} params.applicantId - 申请人ID筛选（可选）
   * @param {string} params.applicationSn - 申请编号模糊搜索（可选）
   * @param {string} params.startDate - 开始日期（可选）
   * @param {string} params.endDate - 结束日期（可选）
   * @returns {Promise} - 返回请求结果
   */
  getApplicationList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/payment-recognition-application`,
      method: 'get',
      params
    })
  },

  /**
   * 获取申请认款详情
   * @param {string|number} id - 申请ID
   * @returns {Promise} - 返回请求结果
   */
  getApplicationDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/payment-recognition-application/${id}`,
      method: 'get'
    })
  },

  /**
   * 审核申请认款
   * @param {string|number} id - 申请ID
   * @param {Object} data - 审核数据
   * @param {string} data.action - 审核操作：approve-通过，reject-驳回
   * @param {string} data.auditRemark - 审核备注（可选）
   * @returns {Promise} - 返回请求结果
   */
  auditApplication(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/payment-recognition-application/${id}/audit`,
      method: 'post',
      data
    })
  },

  /**
   * 获取可用于认款的付款凭证列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，默认为1
   * @param {number} params.pageSize - 每页数量，默认为20
   * @param {string} params.payerName - 付款方名称筛选（可选）
   * @param {number} params.minAmount - 最小金额筛选（可选）
   * @param {number} params.maxAmount - 最大金额筛选（可选）
   * @param {string} params.startDate - 开始日期（可选）
   * @param {string} params.endDate - 结束日期（可选）
   * @returns {Promise} - 返回请求结果
   */
  getAvailableVouchers(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher/available-for-recognition`,
      method: 'get',
      params
    })
  }
}

/**
 * 申请认款状态枚举（前端使用）
 */
export const PaymentRecognitionApplicationStatus = {
  PENDING_AUDIT: 0,    // 待审核
  APPROVED: 1,         // 审核通过
  REJECTED: 2,         // 审核驳回

  /**
   * 获取状态名称
   * @param {number} status - 状态码
   * @returns {string} - 状态名称
   */
  getStatusName(status) {
    switch (parseInt(status)) {
      case this.PENDING_AUDIT:
        return '待审核';
      case this.APPROVED:
        return '审核通过';
      case this.REJECTED:
        return '审核驳回';
      default:
        return '未知状态';
    }
  },

  /**
   * 获取状态颜色
   * @param {number} status - 状态码
   * @returns {string} - 状态颜色
   */
  getStatusColor(status) {
    switch (parseInt(status)) {
      case this.PENDING_AUDIT:
        return 'orange';
      case this.APPROVED:
        return 'green';
      case this.REJECTED:
        return 'red';
      default:
        return 'gray';
    }
  },

  /**
   * 获取所有状态选项（用于下拉框）
   * @returns {Array} - 状态选项数组
   */
  getAllOptions() {
    return [
      { label: '全部', value: '' },
      { label: this.getStatusName(this.PENDING_AUDIT), value: this.PENDING_AUDIT },
      { label: this.getStatusName(this.APPROVED), value: this.APPROVED },
      { label: this.getStatusName(this.REJECTED), value: this.REJECTED },
    ];
  }
};

/**
 * 认款凭证类型枚举（前端使用）- 已废弃，保留向下兼容
 * 新版本支持同时选择付款凭证和上传附件凭证
 */
export const PaymentRecognitionVoucherType = {
  PAYMENT_VOUCHER: 0,      // 付款凭证
  EXISTING_VOUCHER: 1,     // 选择已有付款凭证
  UPLOAD_ATTACHMENT: 2,    // 上传附件凭证

  /**
   * 获取类型名称
   * @param {number} type - 类型码
   * @returns {string} - 类型名称
   */
  getTypeName(type) {
    switch (parseInt(type)) {
      case this.PAYMENT_VOUCHER:
        return '付款凭证';
      case this.EXISTING_VOUCHER:
        return '选择已有付款凭证';
      case this.UPLOAD_ATTACHMENT:
        return '上传附件凭证';
      default:
        return '未知类型';
    }
  },

  /**
   * 获取所有类型选项（用于下拉框）
   * @returns {Array} - 类型选项数组
   */
  getAllOptions() {
    return [
      { label: this.getTypeName(this.PAYMENT_VOUCHER), value: this.PAYMENT_VOUCHER },
      { label: this.getTypeName(this.EXISTING_VOUCHER), value: this.EXISTING_VOUCHER },
      { label: this.getTypeName(this.UPLOAD_ATTACHMENT), value: this.UPLOAD_ATTACHMENT },
    ];
  }
};

/**
 * 申请认款业务常量（前端使用）
 */
export const PaymentRecognitionApplicationConstants = {
  // 最大订单数量限制
  MAX_ORDER_COUNT: 50,
  
  // 最大附件数量限制
  MAX_ATTACHMENT_COUNT: 10,
  
  // 支持的文件类型
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  
  // 文件大小限制（字节）
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB

  /**
   * 验证文件类型
   * @param {string} fileType - 文件类型
   * @returns {boolean} - 是否支持
   */
  isFileTypeAllowed(fileType) {
    return this.ALLOWED_FILE_TYPES.includes(fileType);
  },

  /**
   * 验证文件大小
   * @param {number} fileSize - 文件大小（字节）
   * @returns {boolean} - 是否符合限制
   */
  isFileSizeAllowed(fileSize) {
    return fileSize <= this.MAX_FILE_SIZE;
  },

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} - 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};
