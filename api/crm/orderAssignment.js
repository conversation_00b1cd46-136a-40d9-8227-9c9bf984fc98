/**
 * CRM订单分配API
 * 用于直销模块的订单分配功能
 */
import { request } from '~/utils/request'

// API基础URL
const apiBaseUrl = '/api'

export default {
  /**
   * 获取订单分配列表（未分配的订单，用于分配）
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @returns {Promise} - 返回请求结果
   */
  getOrderList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/orders`,
      method: 'get',
      params
    })
  },

  /**
   * 获取分配给当前用户的订单列表（用于订单管理）
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @returns {Promise} - 返回请求结果
   */
  getMyAssignedOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/my-orders`,
      method: 'get',
      params
    })
  },

  /**
   * 获取待认款订单列表（专门用于发起认款时选择订单）
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @param {number} params.page - 页码，默认为1
   * @param {number} params.pageSize - 每页数量，默认为10
   * @param {string} params.order_id - 订单ID（可选）
   * @param {string} params.third_party_order_sn - 第三方订单号（可选）
   * @param {string} params.recipient_name - 收货人姓名（可选）
   * @param {string} params.startTime - 开始时间（可选）
   * @param {string} params.endTime - 结束时间（可选）
   * @param {string} params.sortField - 排序字段，默认为'assigned_at'
   * @param {string} params.sortOrder - 排序方向，默认为'desc'
   * @returns {Promise} - 返回请求结果
   */
  getPendingRecognitionOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/pending-recognition-orders`,
      method: 'get',
      params
    })
  },

  /**
   * 获取内部用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getSystemUsers(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/system-users`,
      method: 'get',
      params
    })
  },

  /**
   * 创建订单分配
   * @param {Object} data - 分配数据
   * @param {string} data.order_id - 订单ID
   * @param {string} data.order_report_id - 报备信息ID（可选）
   * @param {string} data.assigned_user_id - 被分配的内部用户ID
   * @param {number} data.rate - 费率
   * @param {number} data.assignment_amount - 分配金额
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  createAssignment(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/assign`,
      method: 'post',
      data
    })
  },

  /**
   * 获取报备信息列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getReportRecords(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/report-records`,
      method: 'get',
      params
    })
  },

  /**
   * 获取订单分配详情
   * @param {string|number} id - 分配记录ID
   * @returns {Promise} - 返回请求结果
   */
  getAssignmentDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取分配状态文本
   * @param {number} status - 分配状态
   * @returns {string} - 分配状态文本
   */
  getAssignmentStatusText(status) {
    const statusMap = {
      0: '未分配',
      1: '已分配',
      2: '已接受',
      3: '已拒绝',
      4: '已完成'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取分配状态颜色
   * @param {number} status - 分配状态
   * @returns {string} - 分配状态颜色
   */
  getAssignmentStatusColor(status) {
    const colorMap = {
      0: 'gray',      // 未分配
      1: 'blue',      // 已分配
      2: 'green',     // 已接受
      3: 'red',       // 已拒绝
      4: 'gray'       // 已完成
    }
    return colorMap[status] || 'gray'
  },

  /**
   * 格式化分配数据
   * @param {Object} assignment - 原始分配数据
   * @returns {Object} - 格式化后的分配数据
   */
  formatAssignmentData(assignment) {
    if (!assignment) return null

    return {
      ...assignment,
      assignment_status_text: this.getAssignmentStatusText(assignment.assignment_status),
      assignment_status_color: this.getAssignmentStatusColor(assignment.assignment_status),
      rate_percent: assignment.rate ? (assignment.rate * 100).toFixed(2) + '%' : '0.00%',
      assignment_amount_formatted: assignment.assignment_amount ? 
        parseFloat(assignment.assignment_amount).toFixed(2) : '0.00',
      order_amount_formatted: assignment.order_amount ? 
        parseFloat(assignment.order_amount).toFixed(2) : '0.00',
      assigned_at_formatted: assignment.assigned_at ? 
        new Date(parseInt(assignment.assigned_at)).toLocaleString('zh-CN') : '',
      accepted_at_formatted: assignment.accepted_at ? 
        new Date(parseInt(assignment.accepted_at)).toLocaleString('zh-CN') : '',
      completed_at_formatted: assignment.completed_at ? 
        new Date(parseInt(assignment.completed_at)).toLocaleString('zh-CN') : ''
    }
  },

  /**
   * 获取用户显示名称
   * @param {Object} user - 用户对象
   * @returns {string} - 用户显示名称
   */
  getUserDisplayName(user) {
    if (!user) return ''
    return user.nickname || user.username || user.display_name || ''
  },

  /**
   * 格式化用户数据
   * @param {Object} user - 原始用户数据
   * @returns {Object} - 格式化后的用户数据
   */
  formatUserData(user) {
    if (!user) return null

    return {
      ...user,
      display_name: this.getUserDisplayName(user),
      contact_info: user.phone || user.email || ''
    }
  },

  /**
   * 验证分配数据
   * @param {Object} data - 分配数据
   * @returns {Object} - 验证结果
   */
  validateAssignmentData(data) {
    const errors = []

    if (!data.order_id) {
      errors.push('订单ID不能为空')
    }

    if (!data.assigned_user_id) {
      errors.push('分配用户不能为空')
    }

    if (!data.rate || data.rate <= 0) {
      errors.push('费率必须大于0')
    }

    if (data.rate && data.rate > 1) {
      errors.push('费率不能超过100%')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}
