import { request } from '~/utils/request'
import { apiBaseUrl } from '~/api/config'

/**
 * 付款凭证类型枚举
 */
const VoucherTypeEnum = {
  BANK_TRANSFER: 0,
  CASH_PAYMENT: 1,
  OTHER: 2
}

/**
 * 付款凭证类型标签
 */
const VoucherTypeLabels = {
  [VoucherTypeEnum.BANK_TRANSFER]: '银行流水',
  [VoucherTypeEnum.CASH_PAYMENT]: '现金收款',
  [VoucherTypeEnum.OTHER]: '其他'
}

/**
 * 付款凭证状态枚举
 */
const VoucherStatusEnum = {
  UNLINKED: 0,
  LINKED: 1
}

/**
 * 付款凭证状态标签
 */
const VoucherStatusLabels = {
  [VoucherStatusEnum.UNLINKED]: '未关联',
  [VoucherStatusEnum.LINKED]: '已关联'
}

/**
 * 付款凭证管理API
 */
export default {
  /**
   * 获取付款凭证列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.voucherType - 凭证类型
   * @param {number} params.status - 状态
   * @param {string} params.payerName - 付款方名称
   * @param {string} params.voucherNumber - 凭证编号
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   * @param {number} params.minAmount - 最小金额
   * @param {number} params.maxAmount - 最大金额
   * @returns {Promise} - 返回请求结果
   */
  getList(params = {}) {
    // 处理日期参数，转换为毫秒时间戳
    const processedParams = { ...params };

    // 如果有日期范围参数，转换为毫秒时间戳
    if (params.paymentDate && Array.isArray(params.paymentDate) && params.paymentDate.length === 2) {
      const [startDate, endDate] = params.paymentDate;
      if (startDate) {
        processedParams.startDate = new Date(startDate).getTime();
      }
      if (endDate) {
        // 结束日期设置为当天的23:59:59.999
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        processedParams.endDate = endDateTime.getTime();
      }
      // 删除原始的paymentDate参数
      delete processedParams.paymentDate;
    }

    // 如果有单独的startDate和endDate参数，也转换为毫秒时间戳
    if (params.startDate && typeof params.startDate === 'string') {
      processedParams.startDate = new Date(params.startDate).getTime();
    }
    if (params.endDate && typeof params.endDate === 'string') {
      const endDateTime = new Date(params.endDate);
      endDateTime.setHours(23, 59, 59, 999);
      processedParams.endDate = endDateTime.getTime();
    }

    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher`,
      method: 'get',
      params: processedParams
    })
  },

  /**
   * 获取付款凭证详情
   * @param {string|number} id - 凭证ID
   * @returns {Promise} - 返回请求结果
   */
  getDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建付款凭证
   * @param {Object} data - 凭证数据
   * @param {number} data.voucherType - 凭证类型(0-银行流水，1-现金收款，2-其他)
   * @param {number} data.amount - 金额(>= 0.01)
   * @param {string} data.payerName - 付款方名称(<= 255字符)
   * @param {string} data.paymentDate - 付款日期(YYYY-MM-DD格式)
   * @param {string} data.remarks - 备注(可选)
   * @param {Array} data.attachments - 附件列表
   * @returns {Promise} - 返回请求结果
   */
  create(data) {
    // 数据验证
    if (typeof data.voucherType !== 'number' || ![0, 1, 2].includes(data.voucherType)) {
      throw new Error('凭证类型必须是0、1或2');
    }
    if (typeof data.amount !== 'number' || data.amount < 0.01) {
      throw new Error('金额必须大于等于0.01');
    }
    if (!data.payerName || data.payerName.length > 255) {
      throw new Error('付款方名称不能为空且不能超过255个字符');
    }
    if (!data.paymentDate) {
      throw new Error('付款日期不能为空');
    }

    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher`,
      method: 'post',
      data
    })
  },

  /**
   * 更新付款凭证
   * @param {string|number} id - 凭证ID
   * @param {Object} data - 凭证数据
   * @returns {Promise} - 返回请求结果
   */
  update(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除付款凭证
   * @param {string|number} id - 凭证ID
   * @returns {Promise} - 返回请求结果
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除付款凭证
   * @param {Array} ids - 凭证ID列表
   * @returns {Promise} - 返回请求结果
   */
  batchDelete(ids) {
    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher/batch-delete`,
      method: 'post',
      data: { ids }
    })
  },

  /**
   * 获取凭证类型选项
   * @returns {Array} - 凭证类型选项
   */
  getVoucherTypeOptions() {
    return Object.entries(VoucherTypeLabels).map(([value, label]) => ({
      value: parseInt(value),
      label
    }))
  },

  /**
   * 获取凭证状态选项
   * @returns {Array} - 凭证状态选项
   */
  getVoucherStatusOptions() {
    return Object.entries(VoucherStatusLabels).map(([value, label]) => ({
      value: parseInt(value),
      label
    }))
  },

  /**
   * 根据类型值获取类型标签
   * @param {number} type - 类型值
   * @returns {string} - 类型标签
   */
  getVoucherTypeLabel(type) {
    return VoucherTypeLabels[type] || '未知'
  },

  /**
   * 根据状态值获取状态标签
   * @param {number} status - 状态值
   * @returns {string} - 状态标签
   */
  getVoucherStatusLabel(status) {
    return VoucherStatusLabels[status] || '未知'
  },

  /**
   * 合并付款凭证
   * @param {Object} params - 合并参数
   * @param {Array} params.voucherIds - 要合并的凭证ID数组，至少2个
   * @param {string} params.mergeReason - 合并原因说明，不能为空
   * @returns {Promise} - 返回请求结果
   */
  merge(params) {
    return request({
      url: `${apiBaseUrl}/v1/finance/payment-voucher/merge`,
      method: 'POST',
      data: params
    })
  },

  /**
   * 获取待确认订单列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.customerName - 客户名称
   * @param {string} params.orderNo - 订单编号
   * @param {Array} params.dateRange - 日期范围
   * @param {number} params.minAmount - 最小金额
   * @param {number} params.maxAmount - 最大金额
   * @returns {Promise} - 返回请求结果
   */
  getPendingRecognitionOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/pending-recognition-orders`,
      method: 'GET',
      params: params
    })
  },
  paymentRecognitionApplication(params = {}){
    return request({
          url: `${apiBaseUrl}/v1/crm/order-assignment/payment-recognition-application`,
          method: 'POST',
          data: params
        })
    },
  getAvailableVouchersForRecognition(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/finance/payment-voucher/available-for-recognition`,
        method: 'POST ',
        params: params
      })
    },
  /**
   * 获取申请认款列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，默认1
   * @param {number} params.pageSize - 每页数量，默认20
   * @param {string} params.applicationSn - 申请编号（模糊搜索）
   * @param {string} params.auditorName - 审核人（模糊搜索）
   * @param {Array} params.paymentDate - 付款日期范围
   * @param {number} params.status - 审核状态（0-待审核，1-已通过，2-已驳回）
   * @returns {Promise} - 返回请求结果
   */
  getPaymentRecognitionApplication(params = {}) {
    // 处理日期参数，转换为毫秒时间戳
    const processedParams = { ...params };

    // 如果有日期范围参数，转换为毫秒时间戳
    if (params.paymentDate && Array.isArray(params.paymentDate) && params.paymentDate.length === 2) {
      const [startDate, endDate] = params.paymentDate;
      if (startDate) {
        processedParams.startDate = new Date(startDate).getTime();
      }
      if (endDate) {
        // 结束日期设置为当天的23:59:59.999
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        processedParams.endDate = endDateTime.getTime();
      }
      // 删除原始的paymentDate参数
      delete processedParams.paymentDate;
    }

    console.log('API请求参数:', processedParams);

    return request({
      url: `${apiBaseUrl}/v1/crm/order-assignment/payment-recognition-application`,
      method: 'GET',
      params: processedParams
    })
  },
}

/**
 * 导出枚举和标签
 */
export {
  VoucherTypeEnum,
  VoucherTypeLabels,
  VoucherStatusEnum,
  VoucherStatusLabels
}
