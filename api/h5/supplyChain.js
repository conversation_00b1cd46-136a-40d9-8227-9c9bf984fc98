/**
 * H5供应链相关API
 */

import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

export default {
  /**
   * 根据发货链接ID获取H5页面数据
   * @param {number} linkId - 发货链接ID
   * @returns {Promise} - 返回请求结果
   */
  getShippingPageData(linkId) {
    console.log('API调用 - 链接ID:', linkId)
    console.log('API调用 - request函数:', typeof request)
    console.log('API调用 - apiBaseUrl:', apiBaseUrl)

    const url = `${apiBaseUrl}/v1/master/csm/purchase-orders/shipping-page/${linkId}`
    console.log('API调用 - 完整URL:', url)

    return request({
      url: url,
      method: "get",
    });
  },

  /**
   * 获取商品详情（用于商品详情页面跳转）
   * @param {number} productId - 商品ID
   * @returns {Promise} - 返回请求结果
   */
  getProductDetails(productId) {
    return request({
      url: `${apiBaseUrl}/v1/master/goods/${productId}`,
      method: "get",
    });
  },

  /**
   * 确认发货（如果需要的话）
   * @param {number} linkId - 发货链接ID
   * @param {Object} data - 发货确认数据
   * @returns {Promise} - 返回请求结果
   */
  confirmShipping(linkId, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/shipping-page/${linkId}/confirm`,
      method: "post",
      data,
    });
  },
};
