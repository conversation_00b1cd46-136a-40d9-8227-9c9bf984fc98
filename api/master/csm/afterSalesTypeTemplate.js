import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 售后类型内容模板API
 */
export const afterSalesTypeTemplateApi = {
  /**
   * 获取售后类型内容模板列表
   * @param {Object} params 查询参数
   * @param {number} params.status 状态筛选 (0-禁用, 1-启用)
   * @param {number} params.pid 父级ID筛选
   * @param {boolean} params.flat 是否返回扁平化列表 (默认false，返回层级结构)
   * @returns {Promise} 响应数据
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales-type-templates`,
      method: 'get',
      params
    })
  },

  /**
   * 根据ID获取售后类型内容模板详情
   * @param {number} id 售后类型内容模板ID
   * @returns {Promise} 响应数据
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales-type-templates/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取启用状态的售后类型内容模板树形结构
   * @returns {Promise} 响应数据
   */
  getActiveTree() {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales-type-templates`,
      method: 'get',
      params: {
        status: 1, // 只获取启用状态的模板
        flat: false // 返回层级结构
      }
    })
  },

  /**
   * 获取启用状态的售后类型内容模板扁平化列表
   * @returns {Promise} 响应数据
   */
  getActiveList() {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales-type-templates`,
      method: 'get',
      params: {
        status: 1, // 只获取启用状态的模板
        flat: true // 返回扁平化列表
      }
    })
  }
}
