/**
 * 采购订单API
 */
import { request } from "@/utils/request.js";
import { apiBaseUrl } from "@/api/config";

export const purchaseOrderApi = {
  /**
   * 申请采购订单
   * @param {Object} data - 申请数据
   * @returns {Promise} - 返回请求结果
   */
  applyPurchaseOrder(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/apply`,
      method: "post",
      data,
    });
  },

  /**
   * 根据原始订单号获取采购员信息和商品信息
   * @param {string} orderNumber - 原始订单号
   * @returns {Promise} - 返回请求结果，包含采购员信息和商品信息
   */
  getPurchaserByOrderNumber(orderNumber) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/purchaser/${orderNumber}`,
      method: "get",
    });
  },

  /**
   * 获取采购订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getPurchaseOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders`,
      method: "get",
      params,
    });
  },

  /**
   * 获取采购订单详情
   * @param {number} id - 采购订单ID
   * @returns {Promise} - 返回请求结果
   */
  getPurchaseOrderDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}`,
      method: "get",
    });
  },

  /**
   * 获取采购订单完整详情（包含所有关联数据）
   * @param {number} id - 采购订单ID
   * @returns {Promise} - 返回请求结果
   */
  getPurchaseOrderFullDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/detail`,
      method: "get",
    });
  },

  /**
   * 获取采购订单详情（新版本V2）- 分离格式
   * @param {number} id - 采购订单ID
   * @returns {Promise} - 返回请求结果
   */
  getPurchaseOrderDetailV2(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/v2`,
      method: "get",
    });
  },

  /**
   * 更新采购订单状态
   * @param {number} id - 采购订单ID
   * @param {Object} data - 更新数据
   * @returns {Promise} - 返回请求结果
   */
  updatePurchaseOrderStatus(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/status`,
      method: "put",
      data,
    });
  },

  /**
   * 分配采购员
   * @param {number} id - 采购订单ID
   * @param {Object} data - 分配数据
   * @returns {Promise} - 返回请求结果
   */
  assignPurchaser(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/assign-purchaser`,
      method: "put",
      data,
    });
  },

  /**
   * 设置供应商
   * @param {number} id - 采购订单ID
   * @param {Object} data - 供应商数据
   * @returns {Promise} - 返回请求结果
   */
  setSupplier(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/set-supplier`,
      method: "put",
      data,
    });
  },

  /**
   * 添加采购备注
   * @param {number} id - 采购订单ID
   * @param {Object} data - 备注数据
   * @returns {Promise} - 返回请求结果
   */
  addPurchaseRemark(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/add-remark`,
      method: "post",
      data,
    });
  },

  /**
   * 上传采购订单附件
   * @param {number} id - 采购订单ID
   * @param {FormData} formData - 文件数据
   * @returns {Promise} - 返回请求结果
   */
  uploadAttachment(id, formData) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/upload-attachment`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /**
   * 删除采购订单附件
   * @param {number} id - 采购订单ID
   * @param {number} attachmentId - 附件ID
   * @returns {Promise} - 返回请求结果
   */
  deleteAttachment(id, attachmentId) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/attachments/${attachmentId}`,
      method: "delete",
    });
  },

  /**
   * 获取采购订单统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getPurchaseOrderStats(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/stats`,
      method: "get",
      params,
    });
  },

  /**
   * 导出采购订单
   * @param {Object} params - 导出参数
   * @returns {Promise} - 返回请求结果
   */
  exportPurchaseOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/export`,
      method: "get",
      params,
      responseType: "blob",
    });
  },

  /**
   * 批量更新采购订单状态
   * @param {Object} data - 批量更新数据
   * @returns {Promise} - 返回请求结果
   */
  batchUpdateStatus(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/batch-update-status`,
      method: "put",
      data,
    });
  },

  /**
   * 取消采购订单
   * @param {number} id - 采购订单ID
   * @param {Object} data - 取消原因
   * @returns {Promise} - 返回请求结果
   */
  cancelPurchaseOrder(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/cancel`,
      method: "put",
      data,
    });
  },

  /**
   * 完成采购订单
   * @param {number} id - 采购订单ID
   * @param {Object} data - 完成数据
   * @returns {Promise} - 返回请求结果
   */
  completePurchaseOrder(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/complete`,
      method: "put",
      data,
    });
  },

  /**
   * 获取采购订单日志
   * @param {number} id - 采购订单ID
   * @returns {Promise} - 返回请求结果
   */
  getPurchaseOrderLogs(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/logs`,
      method: "get",
    });
  },

  /**
   * 更换采购员
   * @param {number} id - 采购订单ID
   * @param {Object} data - 更换数据
   * @returns {Promise} - 返回请求结果
   */
  changePurchaser(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/change-purchaser`,
      method: "put",
      data,
    });
  },

  /**
   * 更新采购备注
   * @param {number} id - 采购订单ID
   * @param {Object} data - 备注数据
   * @returns {Promise} - 返回请求结果
   */
  updatePurchaseRemark(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/update-remark`,
      method: "put",
      data,
    });
  },

  /**
   * 更新采购进度
   * @param {number} id - 采购订单ID
   * @param {Object} data - 进度数据
   * @returns {Promise} - 返回请求结果
   */
  updatePurchaseProgress(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/update-progress`,
      method: "put",
      data,
    });
  },

  /**
   * 设置订单成本总价
   * @param {number} id - 采购订单ID
   * @param {Object} data - 成本数据
   * @returns {Promise} - 返回请求结果
   */
  setCostTotal(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/set-cost-total`,
      method: "put",
      data,
    });
  },

  /**
   * 设置商品项成本总价
   * @param {number} productId - 商品项ID
   * @param {Object} data - 成本数据
   * @returns {Promise} - 返回请求结果
   */
  setProductCostTotal(productId, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/product/${productId}/set-cost-total`,
      method: "put",
      data,
    });
  },

  /**
   * 设置商品项亏损原因
   * @param {number} productId - 商品项ID
   * @param {Object} data - 亏损原因数据
   * @returns {Promise} - 返回请求结果
   */
  setProductLossReason(productId, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/product/${productId}/set-loss-reason`,
      method: "put",
      data,
    });
  },

  /**
   * 设置实际供应商
   * @param {number} id - 采购订单ID
   * @param {Object} data - 供应商数据
   * @returns {Promise} - 返回请求结果
   */
  setActualSupplier(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/set-actual-supplier`,
      method: "put",
      data,
    });
  },

  /**
   * 更新税收分类
   * @param {number} id - 采购订单ID
   * @param {Object} data - 税收分类数据
   * @returns {Promise} - 返回请求结果
   */
  updateTaxClassification(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/update-tax-classification`,
      method: "put",
      data,
    });
  },

  /**
   * 保存亏损原因
   * @param {number} id - 采购订单ID
   * @param {Object} data - 亏损原因数据
   * @returns {Promise} - 返回请求结果
   */
  saveLossReason(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/save-loss-reason`,
      method: "put",
      data,
    });
  },

  /**
   * 设置货期时间
   * @param {number} id - 采购订单ID
   * @param {Object} data - 货期时间数据
   * @param {number} data.expected_delivery_time - 预期交货时间（13位时间戳）
   * @param {number} [data.itemId] - 商品项ID（可选，如果指定则只更新该商品项）
   * @returns {Promise} - 返回请求结果
   */
  setExpectedDeliveryTime(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/set-expected-delivery-time`,
      method: "put",
      data,
    });
  },

  /**
   * 生成发货链接
   * @param {number} id - 采购订单ID
   * @param {Object} data - 发货链接数据
   * @param {string} data.recipientName - 收货人姓名
   * @param {string} data.recipientPhone - 收货人电话
   * @param {string} data.recipientAddress - 收货地址
   * @param {number} data.shippingType - 发货类型
   * @returns {Promise} - 返回请求结果
   */
  generateShippingLink(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/generate-shipping-link`,
      method: "post",
      data,
    });
  },

  /**
   * 获取发货链接信息
   * @param {number} id - 采购订单ID
   * @returns {Promise} - 返回请求结果
   */
  getShippingLink(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/purchase-orders/${id}/shipping-link`,
      method: "get",
    });
  },
};
