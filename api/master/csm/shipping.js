import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 发货模块API
 */
export const shippingApi = {
  /**
   * 根据订单号获取配送方式列表
   * @param {string} orderNo 订单号
   * @param {Object} params 查询参数
   * @param {boolean} params.forceMatch 是否强制匹配渠道（可选）
   * @returns {Promise} 响应数据
   */
  getDeliveryMethodsByOrderNo(orderNo, params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/shipping/delivery-methods/${orderNo}`,
      method: 'get',
      params
    })
  },

  /**
   * 创建发货信息
   * @param {Object} data 发货信息数据
   * @returns {Promise} 响应数据
   */
  createShippingInfo(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/shipping/shipping-info`,
      method: 'post',
      data
    })
  }
}

export default shippingApi
