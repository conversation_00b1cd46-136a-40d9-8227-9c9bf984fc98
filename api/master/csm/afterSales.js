/**
 * 售后申请API
 */
import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

export const afterSalesApi = {
  /**
   * 创建售后申请
   * @param {Object} data - 售后申请数据
   * @returns {Promise} - 返回请求结果
   */
  createAfterSalesApplication(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales`,
      method: 'post',
      data
    })
  },

  /**
   * 获取售后申请列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getAfterSalesApplications(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales`,
      method: 'get',
      params
    })
  },

  /**
   * 获取售后申请详情
   * @param {string|number} id - 售后申请ID
   * @returns {Promise} - 返回请求结果
   */
  getAfterSalesApplicationDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取回复组件所需数据
   * @param {string|number} id - 售后申请ID
   * @returns {Promise} - 返回请求结果
   */
  getReplyComponentData(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/reply-component-data`,
      method: 'get'
    })
  },

  /**
   * 获取回复历史记录
   * @param {string|number} id - 售后申请ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getReplyHistory(id, params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/reply-history`,
      method: 'get',
      params
    })
  },

  /**
   * 获取订单回复历史记录（用于采购订单场景）
   * @param {string|number} orderId - 订单ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getOrderReplyHistory(orderId, params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/order/reply-history`,
      method: 'get',
      params: {
        ...params,
        order_id: orderId
      }
    })
  },

  /**
   * 更新售后申请状态
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - 更新数据
   * @returns {Promise} - 返回请求结果
   */
  updateAfterSalesStatus(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/status`,
      method: 'put',
      data
    })
  },

  /**
   * 添加售后回复
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - 回复数据
   * @returns {Promise} - 返回请求结果
   */
  addAfterSalesReply(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/reply`,
      method: 'post',
      data
    })
  },

  /**
   * 更换售后员
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - 售后员数据
   * @returns {Promise} - 返回请求结果
   */
  changeAfterSalesStaff(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/change-staff`,
      method: 'put',
      data
    })
  },

  /**
   * 添加售后备注
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - 备注数据
   * @returns {Promise} - 返回请求结果
   */
  addAfterSalesRemark(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/remark`,
      method: 'post',
      data
    })
  },

  /**
   * 获取售后回复历史
   * @param {string|number} id - 售后申请ID
   * @returns {Promise} - 返回请求结果
   */
  getAfterSalesReplyHistory(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/reply-history`,
      method: 'get'
    })
  },

  /**
   * 复制售后问题
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - 复制数据
   * @returns {Promise} - 返回请求结果
   */
  copyAfterSalesIssue(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/copy`,
      method: 'post',
      data
    })
  },

  /**
   * 单独更新售后状态
   * @param {string|number} id - 售后申请ID
   * @param {number} afterSalesStatus - 售后状态
   * @returns {Promise} - 返回请求结果
   */
  updateAfterSalesStatusOnly(id, afterSalesStatus) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/status-only`,
      method: 'put',
      data: { afterSalesStatus }
    })
  },

  /**
   * 单独更新售后进度
   * @param {string|number} id - 售后申请ID
   * @param {number} afterSalesProgress - 售后进度
   * @returns {Promise} - 返回请求结果
   */
  updateAfterSalesProgressOnly(id, afterSalesProgress) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/progress`,
      method: 'put',
      data: { afterSalesProgress }
    })
  },

  /**
   * 更新退货地址
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - 退货地址数据
   * @returns {Promise} - 返回请求结果
   */
  updateReturnAddress(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/return-address`,
      method: 'put',
      data
    })
  },

  /**
   * 更新退货单号
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - 退货单号数据
   * @returns {Promise} - 返回请求结果
   */
  updateReturnNumber(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/return-number`,
      method: 'put',
      data
    })
  },

  /**
   * 更新ERP系统状态
   * @param {string|number} id - 售后申请ID
   * @param {Object} data - ERP状态数据
   * @returns {Promise} - 返回请求结果
   */
  updateErpStatus(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/erp-status`,
      method: 'put',
      data
    })
  },

  /**
   * 获取售后操作日志
   * @param {string|number} id - 售后申请ID
   * @returns {Promise} - 返回请求结果
   */
  getAfterSalesLogs(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/${id}/logs`,
      method: 'get'
    })
  },

  /**
   * 导出售后申请列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  exportAfterSalesApplications(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/export`,
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 批量更新售后申请状态
   * @param {Object} data - 批量更新数据
   * @returns {Promise} - 返回请求结果
   */
  batchUpdateAfterSalesStatus(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/batch-update-status`,
      method: 'put',
      data
    })
  },

  /**
   * 获取售后统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getAfterSalesStatistics(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/csm/after-sales/statistics`,
      method: 'get',
      params
    })
  },

  /**
   * 获取销售部门列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getSalesDepartments(params = { page: 1, pageSize: 100 }) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/dept`,
      method: 'get',
      params
    })
  }
}

export default afterSalesApi
