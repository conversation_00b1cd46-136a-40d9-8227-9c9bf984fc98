# 货期时间组件修改测试

## 修改内容总结

### 1. 组件替换
- ✅ 将原来的 `<a-input>` 货期时间输入框替换为 `<a-date-picker>` 组件
- ✅ 添加了 `show-time` 属性支持时间选择
- ✅ 设置了样式 `style="width: 220px; margin: 0 24px 24px 0;"`
- ✅ 使用 `value-format="timestamp"` 获取13位时间戳

### 2. API集成
- ✅ 导入了 `purchaseOrderApi` 从 `~/api/master/csm/purchaseOrder`
- ✅ 修改了 `handleDeliveryTimeChange` 方法，调用新的货期时间设置API
- ✅ 添加了错误处理和成功提示

### 3. 事件处理
- ✅ 使用 `@change="(value) => handleDeliveryTimeChange(record, value)"` 处理时间变更
- ✅ 当时间变更时自动调用 `purchaseOrderApi.setExpectedDeliveryTime` API

## 代码变更详情

### 模板部分
```vue
<!-- 原来的输入框 -->
<a-input
  :model-value="record.deliveryTime"
  size="small"
  class="delivery-time-input"
  placeholder="请填写货期时间"
  @blur="(e) => handleDeliveryTimeChange(record, e.target.value)"
  @press-enter="(e) => handleDeliveryTimeChange(record, e.target.value)"
/>

<!-- 新的日期选择器 -->
<a-date-picker
  :model-value="record.deliveryTime"
  show-time
  style="width: 220px; margin: 0 24px 24px 0"
  format="YYYY-MM-DD HH:mm"
  value-format="timestamp"
  @change="(value) => handleDeliveryTimeChange(record, value)"
/>
```

### 脚本部分
```javascript
// 新增导入
import { purchaseOrderApi } from "~/api/master/csm/purchaseOrder";

// 修改后的处理方法
const handleDeliveryTimeChange = async (record, time) => {
  if (!time) {
    return;
  }

  try {
    // 调用货期时间设置API
    await purchaseOrderApi.setExpectedDeliveryTime(record.id, {
      expected_delivery_time: time.toString()
    });

    // 更新本地数据
    record.deliveryTime = time;
    
    // 发送事件通知
    emit("delivery-time-updated", {
      orderData: record,
      deliveryTime: time,
    });

    Message.success("货期时间更新成功");
  } catch (error) {
    console.error("设置货期时间失败:", error);
    Message.error(error.message || "设置货期时间失败");
  }
};
```

## 功能特性

### 1. 用户体验改进
- 📅 提供直观的日期时间选择器界面
- ⏰ 支持精确到分钟的时间选择
- 🎨 统一的UI样式和尺寸
- 💬 清晰的成功/失败提示信息

### 2. 数据处理
- 🔢 自动转换为13位时间戳格式
- 💾 实时调用API保存到服务器
- 🔄 同步更新本地数据状态
- 📡 发送事件通知给父组件

### 3. 错误处理
- ❌ 捕获API调用异常
- 📝 记录详细错误日志
- 🚨 显示用户友好的错误提示
- 🛡️ 防止空值提交

## 测试场景

### 正常流程测试
1. 选择采购进度为"货期"
2. 点击日期选择器
3. 选择具体的日期和时间
4. 确认选择
5. 验证API调用成功
6. 验证成功提示显示
7. 验证数据更新

### 异常情况测试
1. 网络异常时的错误处理
2. 服务器返回错误时的提示
3. 无效数据的验证
4. 空值处理

### 兼容性测试
1. 不同浏览器的日期选择器显示
2. 移动端适配
3. 时区处理
4. 数据格式兼容性

## 注意事项

1. **时间戳格式**: 确保传递给API的是13位毫秒级时间戳
2. **用户权限**: 需要确保用户有修改货期时间的权限
3. **数据同步**: 修改后需要刷新相关数据显示
4. **性能考虑**: 避免频繁的API调用，可考虑防抖处理
5. **时区问题**: 注意服务器和客户端的时区一致性

## 后续优化建议

1. **防抖处理**: 添加防抖机制避免频繁API调用
2. **批量操作**: 支持批量设置多个订单的货期时间
3. **历史记录**: 显示货期时间的修改历史
4. **权限控制**: 根据用户角色控制是否可以修改
5. **数据验证**: 添加更严格的日期范围验证
