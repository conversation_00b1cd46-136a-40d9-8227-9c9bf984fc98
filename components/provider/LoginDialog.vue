<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => visible = val"
    @cancel="close"
    :footer="false"
    :mask-closable="false"
    :width="isMobile ? '90%' : '30%'"
    title="登录"
  >
    <div class="login-content">
      <a-tabs
        :activeKey="loginType"
        @update:activeKey="(val) => loginType = val"
        @change="handleLoginTypeChange"
      >
        <a-tab-pane key="account" title="账号密码登录"></a-tab-pane>
        <a-tab-pane key="phone" title="手机号登录"></a-tab-pane>
      </a-tabs>

      <a-form
        ref="formRef"
        :model="loginType === 'account' ? form : phoneForm"
        :rules="loginType === 'account' ? accountRules : phoneRules"
        @submit="handleSubmit"
      >
        <!-- 账号密码登录表单 -->
        <template v-if="loginType === 'account'">
          <a-form-item field="username" :hide-label="true">
            <a-input
              v-model="form.username"
              class="w-full"
              size="large"
              placeholder="请输入用户名"
              allow-clear
            >
              <template #prefix>
                <icon-user />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item field="password" :hide-label="true">
            <a-input-password
              v-model="form.password"
              class="w-full"
              size="large"
              placeholder="请输入密码"
              allow-clear
            >
              <template #prefix>
                <icon-lock />
              </template>
            </a-input-password>
          </a-form-item>
          <a-form-item field="captcha" :hide-label="true">
            <div class="flex" style="align-items:center;width:100%">
              <a-input
                v-model="form.captcha"
                size="large"
                placeholder="请输入验证码"
                class="flex-1"
                :style="isMobile ? 'width: 60%' : 'width: 80%'"
              >
                <template #prefix>
                  <icon-safe />
                </template>
              </a-input>
              <div class="ml-2 sm:ml-3 cursor-pointer" @click="getCaptcha">
                <img :src="captchaImg" alt="验证码" class="captchaImg" />
              </div>
            </div>
          </a-form-item>
        </template>

        <!-- 手机号登录表单 -->
        <template v-else>
          <a-form-item field="phone" :hide-label="true">
            <a-input
              v-model="phoneForm.phone"
              class="w-full"
              size="large"
              allow-clear
              placeholder="请输入手机号"
              maxlength="11"
            >
              <template #prefix>
                <icon-phone />
              </template>
            </a-input>
          </a-form-item>
          <!-- 短信验证码 -->
          <a-form-item field="captcha" :hide-label="true">
            <div class="flex" style="align-items:center;width:100%">
              <a-input
                v-model="phoneForm.captcha"
                size="large"
                placeholder="请输入短信验证码"
                class="flex-1"
                :style="isMobile ? 'width: 55%' : 'width: 80%'"
              >
                <template #prefix>
                  <icon-safe />
                </template>
              </a-input>
              <a-button
             
                class="ml-2 sm:ml-3"
                :disabled="countdown > 0"
                @click="sendSmsCode"
                :style="isMobile ? 'font-size: 12px; padding: 0 8px;' : ''"
              >
                {{ countdown > 0 ? `${countdown}s` : "获取验证码" }}
              </a-button>
            </div>
          </a-form-item>
        </template>

        <a-form-item :hide-label="true" class="mt-3 sm:mt-5">
          <a-button type="primary" html-type="submit" long :loading="loading" class="bg-gradient-to-r from-red-500 to-orange-500 border-none font-semibold" size="large" style="width:100%">
            登录服务商端
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted } from "vue";
import { useUserStore, useTagStore } from "~/store/index.js";
import commonApi from "~/api/common.js";
import masterAuthApi from "~/api/master/auth.js";
import providerAuthApi from "~/api/provider/auth.js";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "vue-router";
import { navigateTo } from "#app";
import providerMenuMock from "~/mock/provider.js";

const router = useRouter();

// 弹窗控制
const visible = ref(false);
const formRef = ref(null);
const loading = ref(false);
const isMobile = ref(false);

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// 表单验证规则
const phoneRules = {
  phone: [
    { required: true, message: "请输入手机号" },
    { match: /^1[3-9]\d{9}$/, message: "请输入正确的手机号" }
  ],
  captcha: [{ required: true, message: "请输入短信验证码" }]
};

const accountRules = {
  username: [
    { required: true, message: '请输入用户名' }
  ],
  password: [
    { required: true, message: '请输入密码' }
  ],
  captcha: [
    { required: true, message: '请输入验证码' }
  ]
};

// 账号密码登录表单
const form = reactive({
  username: "",
  password: "",
// username: "provider001",
// password: "provider001",
  code: "",
  captcha: "",
  captchaId: ""
});

// 手机号登录表单
const phoneForm = reactive({
  phone: "",
  captcha: ""
});

// 验证码相关
const captchaImg = ref("");

// 获取验证码
const getCaptcha = async () => {
  try {
    const res = await commonApi.getCaptcha();
    if (res.code === 200) {
      captchaImg.value =
        "data:image/svg+xml;utf8," + encodeURIComponent(res.data.image);
      form.captchaId = res.data.id;
    } else {
      console.error("验证码数据结构不符合预期:", res.data);
    }
  } catch (error) {
    console.error("获取验证码出错:", error);
  }
};

const userStore = useUserStore();

// 登录方式
const loginType = ref("account");

// 发送手机验证码
const smsSending = ref(false);
const countdown = ref(0);
const timer = ref(null);

const sendSmsCode = async () => {
  if (smsSending.value) return;

  // 验证手机号
  if (!phoneForm.phone || !/^1[3-9]\d{9}$/.test(phoneForm.phone)) {
    Message.error("请输入正确的手机号");
    return;
  }

  smsSending.value = true;

  try {
    // 调用发送短信验证码的API
    const params = {
      phoneNumber: phoneForm.phone,
      templateCode: "SMS_276465873",
      type: "login"
    };

    const res = await providerAuthApi.getLoginCaptcha(params);

    if (res.code === 200) {
      countdown.value = 60;
      Message.success("验证码已发送");
      
      // 开发环境下显示验证码
      if (process.env.NODE_ENV === 'development' && res.data && res.data.captcha) {
        Message.info(`验证码：${res.data.captcha}`);
      }

      // 倒计时
      timer.value = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer.value);
          smsSending.value = false;
        }
      }, 1000);
    } else {
      Message.error(res.message || "发送短信验证码失败");
      smsSending.value = false;
    }
  } catch (error) {
    console.error("发送短信验证码失败:", error);
    Message.error(error.message || "发送短信验证码失败");
    smsSending.value = false;
  }
};

// 切换登录方式时重置状态
const handleLoginTypeChange = key => {
  loginType.value = key;
  getCaptcha();
};

// 提交登录
const handleSubmit = async ({ values, errors }) => {
  if (loading.value) {
    return;
  }

  loading.value = true;
  
  try {
    
    if (loginType.value === 'account') {
      // 账号密码登录
      const loginResult = await providerAuthApi.login({
        username: form.username,
        password: form.password,
        captcha: form.captcha,
        captchaId: form.captchaId
      });

      if (!loginResult || loginResult.code !== 200 || !loginResult.data || !loginResult.data.token) {
        throw new Error(loginResult?.message || '登录失败');
      }

      // 使用真实token和菜单数据
      const realToken = loginResult.data.token;
      const menuTree = providerMenuMock.routers;
      const userInfo = {
        id: loginResult.data.id,
        username: loginResult.data.username,
        phone: loginResult.data.phone,
        status: loginResult.data.status,
        remark: loginResult.data.remark
      };

   

      // 存储数据
      const tool = await import('@/utils/tool');
      tool.default.local.set('token_provider', realToken);
      tool.default.local.set('menu_provider', menuTree);
      tool.default.local.set('user_provider', userInfo);
      
      // 设置过期时间，默认一天
      const expiresIn = loginResult.data.expires_in || 86400;
      const expiresTime = Date.now() + expiresIn * 1000;
      tool.default.local.set('expires_provider', expiresTime);

      // 清除标签
      useTagStore().clearTags();

      Message.success('服务商登录成功');
      close();
    } else {
      // 手机号登录
      if (!values.phone || !/^1[3-9]\d{9}$/.test(values.phone)) {
        Message.error('请输入正确的手机号');
        loading.value = false;
        return;
      }
      
      if (!values.captcha) {
        Message.error('请输入短信验证码');
        loading.value = false;
        return;
      }

      const loginResult = await providerAuthApi.phoneLogin({
        phone: values.phone,
        captcha: values.captcha
      });

      if (!loginResult || loginResult.code !== 200 || !loginResult.data || !loginResult.data.token) {
        throw new Error(loginResult?.message || '登录失败');
      }

      // 使用真实token和菜单数据
      const realToken = loginResult.data.token;
      const menuTree = providerMenuMock.routers;
      const userInfo = {
        id: loginResult.data.id,
        username: loginResult.data.username,
        phone: loginResult.data.phone,
        status: loginResult.data.status,
        remark: loginResult.data.remark
      };
      
    
    
      // 存储数据
      const tool = await import('@/utils/tool');
      tool.default.local.set('token_provider', realToken);
      console.log('存储菜单数据:', menuTree);
      tool.default.local.set('menu_provider', menuTree);
    
      
      tool.default.local.set('user_provider', userInfo);
      
      // 设置过期时间，默认一天
      const expiresIn = loginResult.data.expires_in || 86400;
      const expiresTime = Date.now() + expiresIn * 1000;
      tool.default.local.set('expires_provider', expiresTime);

      // 清空标签
      const tagStore = useTagStore();
      tagStore.clearTags();

      // 显示成功消息
      Message.success('登录成功');
      close();
    }
    // debugger
    // 获取存储在本地的用户信息
    const tool = await import('@/utils/tool');
    const userInfo = tool.default.local.get('user_provider');

    

    let targetPath = '/provider/workbench';
  
    // 根据状态判断跳转路径
    switch(userInfo.status) {
      case 0: // 未审核
        targetPath = '/provider/information';
        break;
      case 1: // 正常状态
        targetPath = '/provider/workbench';
        break;
      case 2: // 审核中
        targetPath = '/provider/review';
        break;
      case 3: // 驳回
        // 将驳回原因作为参数传递
        const rejectReason = encodeURIComponent(userInfo.remark || '');
        targetPath = `/provider/failed?remark=${rejectReason}`;
        break;
      default:
        targetPath = '/provider/workbench';
    }
    
    try {
      await router.push(targetPath);
    } catch (error) {
      // 如果路由跳转失败，使用 navigateTo
      await navigateTo(targetPath);
    }
  } catch (error) {
    console.error('登录失败:', error);
    // 显示后端返回的错误信息
    Message.error(error.message || '登录失败');
  } finally {
    loading.value = false;
  }
};

// 打开弹窗
const open = () => {
  visible.value = true;
  getCaptcha();
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  // 重置表单
  form.username = "";
  form.password = "";
  form.captcha = "";
  phoneForm.phone = "";
  phoneForm.captcha = "";
  loginType.value = "account";
};

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});

// 暴露方法给父组件
defineExpose({
  open,
  close
});
</script>

<style scoped lang="less">
.captchaImg {
  width: 120px;
  height: 35px;
}

.login-content {
  padding: 15px;

  @media (min-width: 768px) {
    padding: 20px;
  }

  :deep(.arco-tabs-nav) {
    margin-bottom: 15px;

    @media (min-width: 768px) {
      margin-bottom: 20px;
    }
  }

  :deep(.arco-form-item) {
    margin-bottom: 15px;

    @media (min-width: 768px) {
      margin-bottom: 20px;
    }
  }

  :deep(.arco-input-wrapper) {
    background-color: var(--color-fill-2);
  }

  :deep(.arco-btn-primary) {
    margin: 0 auto;
    display: block;
  }
}
</style>