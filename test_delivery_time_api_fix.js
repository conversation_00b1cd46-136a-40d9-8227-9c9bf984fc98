/**
 * 测试货期时间API修复
 * 验证日志记录字段类型问题的修复
 */

// 模拟修复后的日志记录数据结构
const fixedLogData = {
  purchase_order_id: 77,
  action_type: "update_delivery_time",
  action_description: "更新订单预期交货时间",
  old_status: null, // 修复：设置为null而不是日期字符串
  new_status: null, // 修复：设置为null而不是日期字符串
  status_description: "订单预期交货时间更新为: 2025-07-09T10:42:30.615Z",
  operator_id: "194258403865006080",
  operator_name: "系统管理员1",
  operator_role: "196978599641878528",
  details: {
    expectedDeliveryTime: "1752057750615",
    deliveryDate: "2025-07-09T10:42:30.615Z",
    oldDeliveryTime: null
  },
  remark: "更新订单预期交货时间为: 2025/7/9"
};

// 模拟商品项日志记录数据结构
const itemLogData = {
  purchase_order_id: 77,
  action_type: "update_item_delivery_time",
  action_description: "更新商品预期交货时间",
  old_status: null, // 正确：设置为null
  new_status: null, // 正确：设置为null
  status_description: "商品项预期交货时间更新为: 2025-07-09T10:42:30.615Z",
  operator_id: "194258403865006080",
  operator_name: "系统管理员1",
  operator_role: "196978599641878528",
  details: {
    itemId: 123,
    expectedDeliveryTime: "1752057750615",
    deliveryDate: "2025-07-09T10:42:30.615Z"
  },
  remark: "更新商品预期交货时间为: 2025/7/9"
};

/**
 * 验证日志数据结构
 * @param {Object} logData - 日志数据
 * @returns {Object} - 验证结果
 */
function validateLogData(logData) {
  const errors = [];
  const warnings = [];

  // 检查必需字段
  if (!logData.purchase_order_id) {
    errors.push("缺少 purchase_order_id 字段");
  }

  if (!logData.action_type) {
    errors.push("缺少 action_type 字段");
  }

  // 检查状态字段类型
  if (logData.old_status !== null && typeof logData.old_status !== 'number') {
    errors.push(`old_status 字段类型错误: 期望 null 或 number, 实际 ${typeof logData.old_status}`);
  }

  if (logData.new_status !== null && typeof logData.new_status !== 'number') {
    errors.push(`new_status 字段类型错误: 期望 null 或 number, 实际 ${typeof logData.new_status}`);
  }

  // 检查操作人ID类型
  if (logData.operator_id && typeof logData.operator_id !== 'string' && typeof logData.operator_id !== 'bigint') {
    warnings.push(`operator_id 字段类型: ${typeof logData.operator_id}, 建议使用 string 或 bigint`);
  }

  // 检查详细信息
  if (!logData.details) {
    warnings.push("缺少 details 字段，建议添加详细信息");
  } else {
    if (!logData.details.expectedDeliveryTime) {
      warnings.push("details 中缺少 expectedDeliveryTime 字段");
    }
    if (!logData.details.deliveryDate) {
      warnings.push("details 中缺少 deliveryDate 字段");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 运行验证测试
 */
function runValidationTests() {
  console.log("🔍 开始验证货期时间API日志数据结构...\n");

  // 测试订单级别的日志数据
  console.log("📋 测试1: 订单级别货期时间更新日志");
  const orderLogResult = validateLogData(fixedLogData);
  
  if (orderLogResult.isValid) {
    console.log("✅ 订单日志数据结构验证通过");
  } else {
    console.log("❌ 订单日志数据结构验证失败:");
    orderLogResult.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (orderLogResult.warnings.length > 0) {
    console.log("⚠️  警告:");
    orderLogResult.warnings.forEach(warning => console.log(`   - ${warning}`));
  }

  console.log("─".repeat(50));

  // 测试商品项级别的日志数据
  console.log("📋 测试2: 商品项级别货期时间更新日志");
  const itemLogResult = validateLogData(itemLogData);
  
  if (itemLogResult.isValid) {
    console.log("✅ 商品项日志数据结构验证通过");
  } else {
    console.log("❌ 商品项日志数据结构验证失败:");
    itemLogResult.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (itemLogResult.warnings.length > 0) {
    console.log("⚠️  警告:");
    itemLogResult.warnings.forEach(warning => console.log(`   - ${warning}`));
  }

  console.log("─".repeat(50));

  // 测试错误的数据结构（用于对比）
  console.log("📋 测试3: 错误的日志数据结构（修复前）");
  const wrongLogData = {
    ...fixedLogData,
    old_status: "2025-07-09T10:42:30.615Z", // 错误：应该是null或number
    new_status: "2025-07-09T10:42:30.615Z"  // 错误：应该是null或number
  };
  
  const wrongLogResult = validateLogData(wrongLogData);
  
  if (wrongLogResult.isValid) {
    console.log("✅ 错误数据结构验证通过（不应该发生）");
  } else {
    console.log("❌ 错误数据结构验证失败（预期结果）:");
    wrongLogResult.errors.forEach(error => console.log(`   - ${error}`));
  }

  console.log("\n🎉 验证测试完成!");
}

/**
 * 生成正确的API请求示例
 */
function generateApiExamples() {
  console.log("\n📝 正确的API调用示例:\n");

  console.log("1. 设置整个订单的货期时间:");
  console.log(`
PUT /v1/master/csm/purchase-orders/77/set-expected-delivery-time
Content-Type: application/json

{
  "expected_delivery_time": "1752057750615"
}
`);

  console.log("2. 设置单个商品项的货期时间:");
  console.log(`
PUT /v1/master/csm/purchase-orders/77/set-expected-delivery-time
Content-Type: application/json

{
  "expected_delivery_time": "1752057750615",
  "itemId": 123
}
`);

  console.log("3. 前端调用示例:");
  console.log(`
// 使用日期选择器获取的时间戳
const timestamp = "1752057750615"; // 13位时间戳

// 调用API
await purchaseOrderApi.setExpectedDeliveryTime(77, {
  expected_delivery_time: timestamp
});
`);
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runValidationTests();
  generateApiExamples();
}

module.exports = {
  validateLogData,
  runValidationTests,
  generateApiExamples,
  fixedLogData,
  itemLogData
};
