<template>
  <div class="p-5 bg-white rounded">
    <!-- 快递100物流接口配置 -->
    <div>
      <a-card :body-style="{ padding: '16px' }">
        <div style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;">快递100物流接口配置</div>
        <div class="mb-3 text-sm text-gray-500">请填写快递100物流接口的相关配置信息</div>
        <a-form :model="formModel" layout="vertical">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 是否启用 -->
            <a-form-item field="enabled" label="是否启用">
              <a-switch v-model="formModel.enabled" />
            </a-form-item>

            <!-- 授权key -->
            <a-form-item field="key" label="授权Key">
              <a-input v-model="formModel.key" placeholder="请输入快递100授权Key" />
            </a-form-item>

            <!-- customer -->
            <a-form-item field="customer" label="Customer">
              <a-input v-model="formModel.customer" placeholder="请输入Customer" />
            </a-form-item>

            <!-- secret -->
            <a-form-item field="secret" label="Secret">
              <a-input-password v-model="formModel.secret" placeholder="请输入Secret" />
            </a-form-item>

            <!-- userid -->
            <a-form-item field="userid" label="User ID">
              <a-input v-model="formModel.userid" placeholder="请输入User ID" />
            </a-form-item>

            <!-- 智能判断 -->
            <a-form-item field="autoCom" label="智能判断">
              <a-input v-model="formModel.autoCom" placeholder="请输入智能判断参数" />
            </a-form-item>

            <!-- 回调接口 -->
            <a-form-item field="callbackUrl" label="回调接口" class="md:col-span-2">
              <a-input v-model="formModel.callbackUrl" placeholder="请输入回调接口地址" />
              <template #extra>
                <div class="text-sm text-gray-500 mt-1">
                  <!-- 线上访问地址：https://v4api.ioa.8080bl.com/ -->
                </div>
              </template>
            </a-form-item>
          </div>

          <!-- 测试查询物流信息 -->
          <div class="mt-6">
            <div class="font-medium mb-3" style="font-size: 16px; color: black;">测试查询物流信息</div>
            <a-card style="margin-top: 10px;">
              <div class="grid grid-cols-1 gap-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <a-form-item field="testExpressCode" label="快递公司编码">
                    <a-select v-model="testExpressCode" placeholder="请选择快递公司" allow-search>
                      <a-option
                        v-for="company in expressCompanies"
                        :key="company.companyCode"
                        :value="company.companyCode"
                        :label="company.companyName"
                      >
                        {{ company.companyName }} ({{ company.companyCode }})
                      </a-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item field="testExpressNo" label="快递单号">
                    <a-input v-model="testExpressNo" placeholder="请输入快递单号" />
                  </a-form-item>
                </div>
                <a-form-item field="testPhone" label="手机号后四位（可选）">
                  <a-input v-model="testPhone" placeholder="请输入手机号后四位" maxlength="4" />
                </a-form-item>
                <div class="flex justify-end">
                  <a-button type="primary" @click="testLogisticsQuery" :loading="testLoading">
                    测试查询物流信息
                  </a-button>
                </div>
              </div>
            </a-card>
          </div>

          <!-- 测试结果展示 -->
          <div v-if="testResult" class="mt-4">
            <div class="font-medium mb-3" style="font-size: 16px; color: black;">测试结果</div>
            <a-card>
              <pre class="text-sm bg-gray-50 p-3 rounded overflow-auto max-h-96">{{ JSON.stringify(testResult, null, 2) }}</pre>
            </a-card>
          </div>
        </a-form>

        <div class="flex justify-end mt-4">
          <a-button type="primary" @click="submitForm" :loading="saveLoading">
            <template #icon><icon-save /></template>
            保存设置
          </a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import systemApi from '@/api/master/system';

definePageMeta({
  name: 'system-config-logisticsDynamicConfiguration',
  path: '/master/system/config/logisticsDynamicConfiguration'
});

const configApi = systemApi.configuration;

// 表单数据
const formModel = ref({
  enabled: false,
  key: '',
  customer: '',
  secret: '',
  userid: '',
  autoCom: '',
  callbackUrl: ''
});

// 测试相关数据
const testExpressCode = ref('');
const testExpressNo = ref('');
const testPhone = ref('');
const testResult = ref(null);
const testLoading = ref(false);
const saveLoading = ref(false);

// 快递公司列表
const expressCompanies = ref([]);

// 获取快递公司列表
const fetchExpressCompanies = async () => {
  try {
    const res = await systemApi.getExpressCompanies({ getAllData: 'true' });
    if (res.code === 200 && res.data) {
      expressCompanies.value = res.data.map(company => ({
        companyCode: company.companyCode,
        companyName: company.companyName
      }));
    } else {
      // 如果API调用失败，使用一些常见的快递公司作为备选
      expressCompanies.value = [
        { companyCode: 'yuantong', companyName: '圆通速递' },
        { companyCode: 'shentong', companyName: '申通快递' },
        { companyCode: 'zhongtong', companyName: '中通快递' },
        { companyCode: 'yunda', companyName: '韵达速递' },
        { companyCode: 'shunfeng', companyName: '顺丰速运' },
        { companyCode: 'ems', companyName: '中国邮政速递物流' },
        { companyCode: 'jingdong', companyName: '京东物流' },
        { companyCode: 'debangwuliu', companyName: '德邦快递' }
      ];
    }
  } catch (error) {
    console.error('获取快递公司列表失败:', error);
    // 使用备选数据
    expressCompanies.value = [
      { companyCode: 'yuantong', companyName: '圆通速递' },
      { companyCode: 'shentong', companyName: '申通快递' },
      { companyCode: 'zhongtong', companyName: '中通快递' },
      { companyCode: 'yunda', companyName: '韵达速递' },
      { companyCode: 'shunfeng', companyName: '顺丰速运' },
      { companyCode: 'ems', companyName: '中国邮政速递物流' },
      { companyCode: 'jingdong', companyName: '京东物流' },
      { companyCode: 'debangwuliu', companyName: '德邦快递' }
    ];
  }
};

// 获取配置
const fetchSettings = async () => {
  try {
    const res = await configApi.objectTypes('Express100');
    if (res.code === 200 && res.data) {
      // 将配置数据映射到表单
      const config = res.data.express100 || {};
      formModel.value = {
        enabled: config.enabled === '1' || config.enabled === true,
        key: config.key || '',
        customer: config.customer || '',
        secret: config.secret || '',
        userid: config.userid || '',
        autoCom: config.autoCom || '',
        callbackUrl: config.callbackUrl || ''
      };
    }
  } catch (error) {
    console.error('获取配置失败:', error);
    Message.error('获取配置失败');
  }
};

// 测试查询物流信息
const testLogisticsQuery = async () => {
  if (!testExpressCode.value || !testExpressNo.value) {
    Message.warning('请选择快递公司并输入快递单号');
    return;
  }

  testLoading.value = true;
  testResult.value = null;

  try {
    // 构建测试请求数据
    const testData = {
      expressCode: testExpressCode.value,
      expressNo: testExpressNo.value,
      phone: testPhone.value,
      configValue: {
        key: formModel.value.key,
        customer: formModel.value.customer,
        secret: formModel.value.secret,
        userid: formModel.value.userid,
        autoCom: formModel.value.autoCom,
        callbackUrl: formModel.value.callbackUrl
      }
    };

    // 调用测试API
    const res = await systemApi.testLogisticsQuery(testData);

    if (res.code === 200) {
      testResult.value = res.data;
      Message.success('测试查询成功');
    } else {
      testResult.value = { error: res.message };
      Message.error(res.message || '测试查询失败');
    }
  } catch (error) {
    console.error('测试查询失败:', error);
    testResult.value = { error: error.message };
    Message.error('测试查询失败: ' + error.message);
  } finally {
    testLoading.value = false;
  }
};

// 提交表单
const submitForm = async () => {
  try {
    saveLoading.value = true;
    Message.loading('正在保存设置，请稍候...');

    // 组装配置数据
    const configData = {
      enabled: formModel.value.enabled ? '1' : '0',
      key: formModel.value.key,
      customer: formModel.value.customer,
      secret: formModel.value.secret,
      userid: formModel.value.userid,
      autoCom: formModel.value.autoCom,
      callbackUrl: formModel.value.callbackUrl
    };

    // 组装最终要提交的数据
    const data = {
      configValue: configData,
      isDefault: true,
      name: '快递100物流接口配置',
      remark: '快递100物流接口配置信息'
    };

    // 调用API接口保存配置
    const res = await configApi.update('Express100', 'express100', data);

    if (res.code === 200) {
      Message.success(res.message || '保存成功');
      fetchSettings();
    } else {
      Message.error(res.message || '保存失败');
    }
  } catch (error) {
    console.error('保存快递100配置失败:', error);
    Message.error('保存失败，请稍后重试');
  } finally {
    saveLoading.value = false;
  }
};

onMounted(() => {
  fetchSettings();
  fetchExpressCompanies();
});
</script>

<style lang="less" scoped>
// 配置卡片样式
.config-card {
  width: 100%;
}

.test-result {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
}
</style>