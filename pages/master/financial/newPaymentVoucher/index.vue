<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud
      :options="crud"
      :columns="columns"
      ref="crudRef"
      :selectedKeys="selectedKeys"
      @update:selectedKeys="(val) => { console.log('接收到 update:selectedKeys:', val); selectedKeys = val }"
      @selection-change="handleSelectionChange"
    >
      <template #tableBeforeButtons>
       <a-button type="primary" size="small" @click="handleImportVoucher">
          导入凭证
        </a-button>
         <a-button  size="small" @click="handleMergeVoucher" :disabled="!selectedKeys || selectedKeys.length === 0">
          合并凭证({{ selectedKeys ? selectedKeys.length : 0 }})
        </a-button>
        <!-- 导出数据按钮暂时隐藏 -->
        <!-- <a-button size="small" @click="handleExportData">
          导出数据
        </a-button> -->
      </template>
      <!-- 金额 -->
      <template #amount="{ record }">
        ¥{{ Number(record.amount).toLocaleString() }}
      </template>

      <!-- 付款日期 -->
      <template #paymentDate="{ record }">
        {{ formatDate(record.paymentDate) }}
      </template>

      <!-- 状态 -->
      <template #statusLabel="{ record }">
        <a-tag :color="getStatusColor(record.statusLabel)">
          {{record.statusLabel }}
        </a-tag>
      </template>

      <!-- 创建时间 -->
      <template #createdAt="{ record }">
        {{ formatDateTime(record.createdAt) }}
      </template>
      

      <!-- 操作列 -->
      <template #operationAfterExtend="{ record }">
        <a-button type="text" size="small" @click="handleViewDetail(record)">
          <template #icon><icon-eye /></template>
          查看
        </a-button>
      </template>
    </ma-crud>

    <!-- 详情 -->
    <AuditDetailDrawer ref="auditDetailDrawerRef" @success="handleAuditSuccess" />

    <!-- 导入凭证弹窗 -->
    <ImportVoucherModal ref="importVoucherModalRef" @success="handleImportSuccess" />

    <!-- 合并凭证弹窗 -->
    <MergeVoucherModal ref="mergeVoucherModalRef" @success="handleMergeSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";
import AuditDetailDrawer from "./components/AuditDetailDrawer.vue";
import ImportVoucherModal from "./components/ImportVoucherModal.vue";
import MergeVoucherModal from "./components/MergeVoucherModal.vue";
import paymentVoucherApi, { VoucherTypeLabels, VoucherStatusLabels } from "~/api/finance/paymentVoucher";

// 定义页面路由元信息
definePageMeta({
  name: "financial-newPaymentVoucher",
  path: "/master/financial/newPaymentVoucher",
});

// 响应式数据
const crudRef = ref();
const auditDetailDrawerRef = ref();
const importVoucherModalRef = ref();
const mergeVoucherModalRef = ref();
const selectedKeys = ref([]);

// CRUD 配置
const crud = reactive({
  api: paymentVoucherApi.getList,
  showIndex: false,
  pageLayout: "fixed",
  operationColumn: true,
  operationColumnWidth: 120,
  rowSelection: {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false, // 关键：允许跨页面记住选择
  },
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
});

// 表格列定义
const columns = reactive([
  {
    title: '凭证编号',
    dataIndex: 'voucherNumber',
    width: 150,
    search: true,
    slotName: 'voucherNumber'
  },
  {
    title: '凭证类型',
    dataIndex: 'voucherTypeLabel',
    width: 120,
    search: true,
    formType: 'select',
    dict: {
      data: paymentVoucherApi.getVoucherTypeOptions()
    }
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 120,
    slotName: 'amount',
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '付款日期',
    dataIndex: 'paymentDate',
    width: 120,
    slotName: 'paymentDate',
    search: true,
    formType: 'range',
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '付款方',
    dataIndex: 'payerName',
    width: 150,
    search: true,
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '状态',
    dataIndex: 'statusLabel',
    width: 100,
    slotName: 'statusLabel',
    search: true,
    formType: 'select',
    dict: {
      data: paymentVoucherApi.getVoucherStatusOptions()
    },
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    width: 200,
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    slotName: 'createdAt',
    addDisplay: false,
    editDisplay: false
  }
]);

// 方法
// 格式化日期
const formatDate = (paymentDate) => {
  if (!paymentDate) return '-';
  return new Date(paymentDate).toLocaleDateString('zh-CN');
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '-';
  return new Date(timestamp).toLocaleString('zh-CN');
};



// 获取状态颜色
const getStatusColor = (statusLabel) => {
  const colorMap = {
    '未关联': '#faad14',
    '已关联': '#52c41a'
  };
  return colorMap[statusLabel] || '#d9d9d9';
};

// 查看详情
const handleViewDetail = (record) => {
  auditDetailDrawerRef.value?.open(record);
};

// 处理审核成功
const handleAuditSuccess = (record) => {
  Message.success('审核操作成功');

  // 刷新表格数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 导入凭证
const handleImportVoucher = () => {
  importVoucherModalRef.value?.open();
};

// 处理导入成功
const handleImportSuccess = () => {
  Message.success('凭证导入成功');

  // 刷新表格数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 处理选择变化 - 用户勾选数据时触发，支持跨页面记住选择
const handleSelectionChange = (keys) => {
  console.log('选择变化，当前选中的keys:', keys);
  console.log('选中数量:', keys ? keys.length : 0);

  // selectedKeys 会通过 @update:selectedKeys 事件自动更新
  // 不需要手动设置
};


// 合并凭证 - 点击合并按钮时触发
const handleMergeVoucher = async () => {
  if (selectedKeys.value.length === 0) {
    Message.warning('请先选择要合并的凭证');
    return;
  }
  if (selectedKeys.value.length < 2) {
    Message.warning('至少选择2个凭证才能合并');
    return;
  }

  console.log('准备合并，选中的keys:', selectedKeys.value);

  // 分多次请求获取所有选中的数据
  try {
    let selectedRowsData = [];
    let foundIds = new Set(); // 记录已找到的ID
    let currentPage = 1;
    const pageSize = 100;

    console.log('开始分页查找选中的数据，目标IDs:', selectedKeys.value);

    // 循环请求直到找到所有数据或没有更多页面
    while (foundIds.size < selectedKeys.value.length) {
      console.log(`请求第 ${currentPage} 页，已找到 ${foundIds.size}/${selectedKeys.value.length} 个数据`);

      const response = await paymentVoucherApi.getList({
        ids: selectedKeys.value,
        page: currentPage,
        pageSize: pageSize
      });

      if (response.code !== 200 || !response.data || !response.data.items) {
        console.error('API 请求失败或数据格式错误');
        break;
      }

      const { items, pageInfo } = response.data;
      console.log(`第 ${currentPage} 页返回 ${items.length} 条数据`);
      console.log('当前页数据 IDs:', items.map(item => item.id));

      // 筛选出选中的数据
      const currentPageSelectedData = items
        .filter(row => {
          const isIncluded = selectedKeys.value.includes(row.id) && !foundIds.has(row.id);
          if (isIncluded) {
            foundIds.add(row.id);
            console.log(`找到匹配数据: ${row.id} - ${row.voucherNumber}`);
          }
          return isIncluded;
        })
        .map(row => ({
          id: row.id,
          voucherNumber: row.voucherNumber,
          payerName: row.payerName,
          voucherTypeLabel: row.voucherTypeLabel,
          amount: row.amount,
          statusLabel: row.statusLabel,
          paymentDate: row.paymentDate,
          createdBy: row.createdBy ,
          remark: row.remarks,
          relatedOrders: [],
          bankInfo: ''
        }));

      selectedRowsData.push(...currentPageSelectedData);

      // 检查是否还有更多页面
      if (currentPage >= pageInfo.totalPage) {
        console.log('已到达最后一页');
        break;
      }

      currentPage++;

      // 安全检查：避免无限循环
      if (currentPage > 50) {
        console.warn('已请求超过50页，停止查找');
        break;
      }
    }

    console.log('最终找到的选中数据:', selectedRowsData);
    console.log(`总共找到 ${selectedRowsData.length}/${selectedKeys.value.length} 个匹配的数据`);

    if (selectedRowsData.length > 0) {
      mergeVoucherModalRef.value?.open(selectedRowsData);
    } else {
      Message.error(`无法获取选中的凭证数据。选中了 ${selectedKeys.value.length} 个，但只找到了 ${selectedRowsData.length} 个匹配的数据。`);
    }

    // 如果没有找到所有数据，给出提示
    if (selectedRowsData.length < selectedKeys.value.length) {
      const missingIds = selectedKeys.value.filter(id => !foundIds.has(id));
      console.warn('未找到的数据 IDs:', missingIds);
      Message.warning(`部分数据未找到，已找到 ${selectedRowsData.length}/${selectedKeys.value.length} 个数据`);
    }

  } catch (error) {
    console.error('获取选中数据失败:', error);
    Message.error('获取选中数据失败');
  }
};

// 处理合并成功
const handleMergeSuccess = () => {
  Message.success('付款凭证合并成功');

  // 清空选择
  selectedKeys.value = [];

  // 刷新表格数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 导出数据 - 暂时隐藏
// const handleExportData = () => {
//   Message.info('导出数据功能开发中...');
// };

// 监听表格数据变化，在数据加载完成后恢复选择状态
watch(() => {
  if (crudRef.value) {
    const tableData = crudRef.value.getTableData();
    return tableData ? tableData.length : 0;
  }
  return 0;
}, (newLength) => {
  if (newLength > 0 && selectedKeys.value.length > 0) {
    console.log('表格数据已加载，准备恢复选择状态...');
    // 延迟执行，确保表格渲染完成
    nextTick(() => {
      setTimeout(() => {
        if (crudRef.value) {
          // 获取当前页面数据
          const tableData = crudRef.value.getTableData();
          if (tableData && Array.isArray(tableData)) {
            // 找出当前页面中需要选中的数据ID
            const currentPageIds = tableData.map(row => row.id);
            const toSelectIds = selectedKeys.value.filter(id => currentPageIds.includes(id));

            if (toSelectIds.length > 0) {
              console.log('恢复当前页面选中状态:', toSelectIds);
              crudRef.value.setSelecteds(toSelectIds);
            }
          }
        }
      }, 200);
    });
  }
}, { immediate: false });

// ma-crud 组件会通过 @selection-change 事件来处理选择变化
// 设置了 onlyCurrent: false 后，表格会自动维护跨页面的选择状态
</script>

<style scoped>
.amount-text {
  font-weight: 600;
  color: #1890ff;
}
.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.subtitle {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 16px;
}

.table-section {
  margin-bottom: 16px;
}

.table-subtitle {
  color: #8c8c8c;
  font-size: 12px;
  margin-bottom: 16px;
}

.amount-text {
  font-weight: 600;
  color: #1890ff;
}

.audit-detail {
  padding: 0;
}

.info-section {
  margin-bottom: 24px;
}

.info-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.voucher-info {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  color: #666;
}

.audit-actions {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
}

:deep(.arco-descriptions-item-label) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.arco-card-header) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.arco-table-th) {
  background-color: #f7f8fa;
}
</style>