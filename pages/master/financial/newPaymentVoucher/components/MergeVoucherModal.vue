<template>
  <a-modal
    :visible="visible"
    title="合并付款凭证"
    :width="850"
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @close="handleCancel"
  >
    <div class="merge-voucher-content">
      <div class="selected-vouchers-section">
        <div class="tips">将选中的多个凭证合并为一个完整的付款凭证，通常用于同一笔付款的不同凭证类型</div>
        <h3>选中的凭证 ({{ selectedVouchers.length }})</h3>
        <div class="voucher-list">
          <div 
            v-for="voucher in selectedVouchers" 
            :key="voucher.id"
            class="voucher-item"
            :class="{ 'selected': voucher.id === selectedVoucherId }"
            @click="selectVoucher(voucher.id)"
          >
            <div class="voucher-header">
              <span class="voucher-no">{{ voucher.voucherNumber }}</span>
              <a-tag :color="getStatusColor(voucher.statusLabel)">{{ voucher.statusLabel }}</a-tag>
            </div>
            <div class="voucher-info">
              <span>{{ voucher.payerName }} - {{ voucher.voucherTypeLabel }}</span>
            </div>
            <div class="voucher-amount">
              <span class="amount">¥{{ voucher.amount.toLocaleString() }}</span>
              <span class="date">{{ formatDate(voucher.paymentDate) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="merge-result-section">
        <h3>合并验证</h3>
        <div class="validation-results">
          <!-- 金额一致性检查 -->
          <!-- <div class="validation-item" :class="{ 'error': !amountConsistent, 'success': amountConsistent }">
            <a-icon :name="amountConsistent ? 'check-circle' : 'close-circle'" />
            <span class="validation-text">
              金额一致性: {{ amountConsistent ? '通过' : '不一致' }}
            </span>
            <div class="validation-detail">
              金额: ¥{{ amounts.join(', ¥') }}
            </div>
          </div> -->

          <!-- 付款方一致性检查 -->
          <div class="validation-item" :class="{ 'error': !payerConsistent, 'success': payerConsistent }">
            <a-icon :name="payerConsistent ? 'check-circle' : 'close-circle'" />
            <span class="validation-text">
              付款方一致性: {{ payerConsistent ? '通过' : '不一致' }}
            </span>
            <div class="validation-detail">
              付款方: {{ payerName.join(', ') }}
            </div>
          </div>

          <!-- 日期一致性检查 -->
          <div class="validation-item" :class="{ 'error': !dateConsistent, 'success': dateConsistent }">
            <a-icon :name="dateConsistent ? 'check-circle' : 'close-circle'" />
            <span class="validation-text">
              日期一致性: {{ dateConsistent ? '通过' : '不一致' }}
            </span>
            <div class="validation-detail">
              付款日期: {{ paymentDate.map(d => formatDate(d)).join(', ') }}
            </div>
          </div>
        </div>
      </div>

      <div class="merge-form-section">
        <h3>合并说明</h3>
        <a-form :model="mergeForm" layout="vertical">
          <a-form-item label="合并说明" required>
            <a-textarea 
              v-model="mergeForm.mergeReason" 
              placeholder="请说明合并原因，例如：同一笔付款的不同凭证..."
              :rows="4"
              :max-length="500"
              show-word-limit
            />
          </a-form-item>
        </a-form>
      </div>

      <div class="merge-tips-section">
        <h4>注意事项</h4>
        <ul>
          <li>合并后将保留凭证编号为"已合并"状态，不可再次使用</li>
          <li>系统将自动识别并生成新的合并凭证编号</li>
          <li>重复记录将被自动过滤</li>
          <li>合并操作不可撤销，请谨慎操作</li>
        </ul>
      </div>
    </div>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button 
          type="primary" 
          :loading="loading" 
          @click="handleMerge"
          :disabled="!mergeForm.mergeReason"
        >
          确认合并
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import paymentVoucherApi from '~/api/finance/paymentVoucher';

// 定义事件
const emit = defineEmits(['success']);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const selectedVouchers = ref([]);
const selectedVoucherId = ref(null);

// 合并表单
const mergeForm = reactive({
  mergeReason: ''
});

// 计算属性
const amounts = computed(() => {
  return [...new Set(selectedVouchers.value.map(v => v.amount))];
});

const payerName = computed(() => {
  return [...new Set(selectedVouchers.value.map(v => v.payerName))];
});

const paymentDate = computed(() => {
  return [...new Set(selectedVouchers.value.map(v => v.paymentDate))];
});

const amountConsistent = computed(() => {
  return amounts.value.length === 1;
});

const payerConsistent = computed(() => {
  return payerName.value.length === 1;
});

const dateConsistent = computed(() => {
  return paymentDate.value.length === 1;
});

// 方法
// 格式化日期
const formatDate = (date) => {
  if (!date) return '-';
  // 处理时间戳格式
  const dateObj = typeof date === 'number' ? new Date(date) : new Date(date);
  return dateObj.toLocaleDateString('zh-CN');
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '待审核': '#faad14',
    '已录入': '#52c41a',
    '已审核': '#52c41a',
    '已驳回': '#ff4d4f'
  };
  return colorMap[status] || '#d9d9d9';
};

// 选择凭证
const selectVoucher = (voucherId) => {
  selectedVoucherId.value = voucherId;
};

// 打开弹窗
const open = (vouchers) => {
  console.log('MergeVoucherModal.open called with:', vouchers);
  console.log('vouchers type:', typeof vouchers);
  console.log('vouchers length:', vouchers ? vouchers.length : 'undefined');

  selectedVouchers.value = vouchers || [];
  selectedVoucherId.value = vouchers && vouchers.length > 0 ? vouchers[0].id : null;
  visible.value = true;
  resetForm();

  console.log('selectedVouchers after assignment:', selectedVouchers.value);
  console.log('selectedVouchers length:', selectedVouchers.value.length);
};

// 重置表单
const resetForm = () => {
  Object.assign(mergeForm, {
    mergeReason: ''
  });
};

// 关闭弹窗
const handleCancel = () => {
  visible.value = false;
  selectedVouchers.value = [];
  selectedVoucherId.value = null;
  resetForm();
};

// 合并凭证
const handleMerge = async () => {
  if (!mergeForm.mergeReason) {
    Message.error('请填写合并说明');
    return;
  }

  if (!selectedVouchers.value || selectedVouchers.value.length < 2) {
    Message.error('至少需要选择2个凭证才能合并');
    return;
  }

  loading.value = true;

  try {
    // 准备请求参数
    const params = {
      voucherIds: selectedVouchers.value.map(item => item.id),
      mergeReason: mergeForm.mergeReason
    };

    console.log('合并凭证请求参数:', params);

    // 调用合并接口
    const response = await paymentVoucherApi.merge(params);

    if (response.code === 200) {
      emit('success');
      handleCancel();
    } else {
      Message.error(response.message || '合并失败，请重试');
    }
  } catch (error) {
    console.error('合并凭证失败:', error);
    Message.error(error.message || '合并失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 暴露方法
defineExpose({
  open
});
</script>

<style scoped>
.tips{
  font-size: 12px;
  color: #000;
}
.merge-voucher-content {
  max-height: 600px;
  overflow-y: auto;
}

.selected-vouchers-section,
.merge-result-section,
.merge-form-section,
.merge-tips-section {
  margin-bottom: 24px;
}

.selected-vouchers-section h3,
.merge-result-section h3,
.merge-form-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.voucher-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.voucher-item {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.voucher-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.voucher-item.selected {
  border-color: #1890ff;
  background: #f0f8ff;
}

.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.voucher-no {
  font-weight: 600;
  font-size: 16px;
  color: #262626;
}

.voucher-info {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.voucher-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount {
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
}

.date {
  color: #999;
  font-size: 12px;
}

.validation-results {
  background: #f6f8fa;
  border-radius: 8px;
  padding: 16px;
}

.validation-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 6px;
}

.validation-item:last-child {
  margin-bottom: 0;
}

.validation-item.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
}

.validation-item.success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.validation-item.warning {
  background: #fffbe6;
  border: 1px solid #ffe58f;
}

.validation-item .arco-icon {
  margin-right: 8px;
  margin-top: 2px;
}

.validation-item.error .arco-icon {
  color: #ff4d4f;
}

.validation-item.success .arco-icon {
  color: #52c41a;
}

.validation-item.warning .arco-icon {
  color: #faad14;
}

.validation-text {
  font-weight: 600;
  margin-bottom: 4px;
}

.validation-detail {
  font-size: 12px;
  color: #666;
  margin-left: 24px;
}

.merge-tips-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.merge-tips-section ul {
  margin: 0;
  padding-left: 20px;
  background: #f6f8fa;
  border-radius: 8px;
  padding: 16px 16px 16px 36px;
}

.merge-tips-section li {
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.merge-tips-section li:last-child {
  margin-bottom: 0;
}

:deep(.arco-form-item-label) {
  font-weight: 600;
}
</style>
