<template>
  <a-modal
    v-model:visible="visible"
    title="凭证详情"
    :width="800"
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @close="handleCancel"
  >
    <div v-if="currentRecord" class="voucher-detail">
    

      <!-- 凭证详细信息 -->
      <div class="info-section">
        <h3 class="section-title">
          <icon-file-text />
          凭证信息
        </h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="凭证编号">{{ currentRecord.voucherNo }}</a-descriptions-item>
          <a-descriptions-item label="付款方">{{ currentRecord.payer }}</a-descriptions-item>
          <a-descriptions-item label="凭证类型">{{ currentRecord.voucherType }}</a-descriptions-item>
          <a-descriptions-item label="付款银行账号">{{ currentRecord.bankInfo || '-' }}</a-descriptions-item>
          <a-descriptions-item label="收款金额">{{ currentRecord.bankInfo || '-' }}</a-descriptions-item>
          <a-descriptions-item label="付款方银行">{{ currentRecord.bankInfo || '-' }}</a-descriptions-item>
          <a-descriptions-item label="付款日期">{{ formatDateTime(currentRecord.createdAt) }}</a-descriptions-item>
          <a-descriptions-item label="备注" >
           {{ currentRecord.remark || '无备注' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <!-- 附件信息 -->
      <div class="info-section" v-if="currentRecord.attachments && currentRecord.attachments.length > 0">
        <h3 class="section-title">
          <icon-attachment />
          附件信息
        </h3>
        <div class="attachment-list">
          <div
            v-for="(attachment, index) in currentRecord.attachments"
            :key="index"
            class="attachment-item"
          >
            <icon-file />
            <span>附件{{ index + 1 }}</span>
            <a-button type="text" size="small">
              <icon-download />
              下载
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed } from "vue";
import { Message } from "@arco-design/web-vue";


// 定义事件
const emit = defineEmits(['success', 'cancel']);

// 抽屉显示状态
const visible = ref(false);
const currentRecord = ref(null);



// 方法
// 格式化日期
const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('zh-CN');
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '-';
  return new Date(timestamp).toLocaleString('zh-CN');
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '待审核': '#faad14',
    '已录入': '#52c41a',
    '已审核': '#52c41a',
    '已驳回': '#ff4d4f'
  };
  return colorMap[status] || '#d9d9d9';
};

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已驳回'
  };
  return textMap[status] || '未知';
};

// 打开抽屉
const open = (record) => {
  currentRecord.value = record;
  visible.value = true;
};

// 关闭抽屉
const handleCancel = () => {
  visible.value = false;
  currentRecord.value = null;
  emit('cancel');
};



// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.voucher-detail {
  padding: 0;
}

/* 凭证头部 */
.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.voucher-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.voucher-meta {
  display: flex;
  gap: 16px;
  opacity: 0.9;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
}

.amount-display {
  text-align: right;
}

.amount-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 28px;
  font-weight: 700;
  color: #fff;
}

/* 信息区块 */
.info-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.section-title .arco-icon {
  color: #1890ff;
}

/* 备注内容 */
.remark-content {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  color: #666;
  font-style: italic;
}

/* 关联订单 */
.related-orders {
  min-height: 40px;
}

.order-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.order-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  font-size: 14px;
}

/* 附件列表 */
.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.attachment-item .arco-icon {
  color: #1890ff;
}

/* 审核操作 */
.audit-actions {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
  background: #fafafa;
  border-radius: 6px;
}

/* 描述列表样式 */
:deep(.arco-descriptions-item-label) {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

:deep(.arco-descriptions-item-value) {
  color: #595959;
}

/* 标签样式 */
:deep(.arco-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voucher-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    align-items: flex-start;
    width: 100%;
  }

  .amount-display {
    text-align: left;
  }

  .order-list {
    flex-direction: column;
  }
}
</style>
