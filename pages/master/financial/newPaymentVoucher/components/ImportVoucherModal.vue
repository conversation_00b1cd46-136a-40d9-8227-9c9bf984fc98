<template>
  <a-modal
    v-model:visible="visible"
    title="导入付款凭证"
    :width="900"
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @close="handleCancel"
  >
    <div class="import-voucher-content">
      <div class="form-section">
        <a-form :model="voucherForm" layout="vertical">
          <!-- 批量导入和单个上传切换 -->
          <div class="upload-tabs">
            <a-radio-group v-model="uploadType" type="button" size="large">
              <a-radio value="single">单个上传</a-radio>
              <a-radio value="batch">批量导入</a-radio>
            </a-radio-group>
          </div>

          <!-- 单个上传表单 -->
          <div v-if="uploadType === 'single'" class="single-upload-form">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="凭证类型" required>
                  <a-select 
                    v-model="voucherForm.voucherType" 
                    placeholder="请选择凭证类型"
                    :options="voucherTypeOptions"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="金额" required>
                  <a-input-number
                    v-model="voucherForm.amount"
                    placeholder="请输入金额"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                  >
                    <template #prefix>¥</template>
                  </a-input-number>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="付款方名称" required>
                  <a-input
                    v-model="voucherForm.payerName"
                    placeholder="请输入付款方名称"
              
                    show-word-limit
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="付款日期" required>
                  <a-date-picker
                    v-model="voucherForm.paymentDate"
                    placeholder="请选择付款日期"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="上传凭证文件" required>
              <ma-upload
                v-model="voucherForm.attachments"
                type="file"
                :multiple="true"
                :draggable="true"
                :accept="'.pdf,.png,.jpg,.jpeg,.doc,.docx'"
                :size="10 * 1024 * 1024"
                style="margin-top: 12px;"
                :tip="'支持上传 pdf/png/jpg/jpeg/doc/docx 等格式文件，单个文件不超过 10MB'"
                return-type="url"
                title="点击上传附件凭证"
              >
                <template #default>
                  <div class="upload-area">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                      <div>点击上传文件 或将文件拖拽到此处</div>
                      <div class="upload-tips">
                        支持扩展名：.jpg、.jpeg、.png、.pdf、.doc、.docx，单个文件最大 10MB
                      </div>
                    </div>
                  </div>
                </template>
              </ma-upload>
            </a-form-item>

            <a-form-item label="备注">
              <a-textarea
                v-model="voucherForm.remarks"
                placeholder="请输入备注信息（可选）"
                :rows="3"
                :max-length="200"
                show-word-limit
              />
            </a-form-item>
          </div>

          <!-- 批量导入 -->
          <div v-else class="batch-upload-form">
            <div class="batch-upload-area">
              <ma-upload
                v-model="batchForm.files"
                type="file"
                :multiple="false"
                :draggable="true"
                :accept="'.xlsx,.xls,.csv'"
                :size="10 * 1024 * 1024"
                style="margin-top: 12px;"
                :tip="'支持上传 Excel (.xlsx, .xls) 和 CSV 格式文件'"
                return-type="url"
                title="点击选择文件 或拖拽文件到此处"
              >
                <template #default>
                  <div class="upload-area">
                    <div class="upload-icon">📊</div>
                    <div class="upload-text">
                      <div>点击选择文件 或拖拽文件到此处</div>
                      <div class="upload-tips">
                        支持 Excel (.xlsx, .xls) 和 CSV 格式
                      </div>
                    </div>
                  </div>
                </template>
              </ma-upload>
            </div>

            <div class="import-instructions">
              <h4>导入说明</h4>
              <ul>
                <li>文件应包含：日期、金额、付款方、银行信息、凭证类型等字段</li>
                <li>系统将自动识别并生成凭证编号</li>
                <li>重复记录将被自动过滤</li>
              </ul>
            </div>
          </div>
        </a-form>
      </div>
    </div>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          {{ uploadType === 'single' ? '确认导入' : '批量导入' }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Message } from '@arco-design/web-vue';
import paymentVoucherApi from "~/api/finance/paymentVoucher";

// 定义事件
const emit = defineEmits(['success']);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const uploadType = ref('single'); // single: 单个上传, batch: 批量导入
const batchType = ref('template');

// 单个上传表单
const voucherForm = reactive({
  voucherType: '', // 使用数字类型
  amount: null,
  payerName: '',
  paymentDate: '',
  attachments: [],
  remarks: ''
});

// 批量导入表单
const batchForm = reactive({
  files: []
});

// 凭证类型选项 - 使用API提供的选项
const voucherTypeOptions = paymentVoucherApi.getVoucherTypeOptions();

// 方法
// 打开弹窗
const open = () => {
  visible.value = true;
  resetForm();
};

// 重置表单
const resetForm = () => {
  uploadType.value = 'single';
  batchType.value = 'template';
  
  // 重置单个上传表单
  Object.assign(voucherForm, {
    voucherType: '',
    amount: null,
    payerName: '',
    paymentDate: '',
    attachments: [],
    remarks: ''
  });
  
  // 重置批量导入表单
  Object.assign(batchForm, {
    files: []
  });
};

// 关闭弹窗
const handleCancel = () => {
  visible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (uploadType.value === 'single') {
    await handleSingleUpload();
  } else {
    await handleBatchUpload();
  }
};

// 处理单个上传
const handleSingleUpload = async () => {
  // 表单验证
  if (voucherForm.voucherType === null || voucherForm.voucherType === undefined) {
    Message.error('请选择凭证类型');
    return;
  }
  if (!voucherForm.amount || voucherForm.amount < 0) {
    Message.error('请输入有效金额');
    return;
  }
  if (!voucherForm.payerName || voucherForm.payerName.trim() === '') {
    Message.error('请输入付款方名称');
    return;
  }
  if (!voucherForm.paymentDate) {
    Message.error('请选择付款日期');
    return;
  }
  if (!voucherForm.attachments || voucherForm.attachments.length === 0) {
    Message.error('请上传凭证文件');
    return;
  }

  loading.value = true;

  try {
    // 准备提交数据，严格按照接口文档格式
    const submitData = {
      voucherType: parseInt(voucherForm.voucherType), // 确保是整数类型
      amount: Number(voucherForm.amount), // 确保是数字类型
      payerName: voucherForm.payerName.trim(), // 去除首尾空格
      paymentDate: formatDateToString(voucherForm.paymentDate), // 格式化为 YYYY-MM-DD 格式
      remarks: voucherForm.remarks || '', // 可选字段
      attachments: voucherForm.attachments || [] // 附件列表
    };

    console.log('提交数据:', submitData);

    // 调用API创建付款凭证
    const response = await paymentVoucherApi.create(submitData);

    if (response.code === 200 || response.success) {
      Message.success('付款凭证创建成功');
      emit('success');
      handleCancel();
    } else {
      Message.error(response.message || '创建失败，请重试');
    }
  } catch (error) {
    console.error('创建付款凭证失败:', error);
    Message.error(error.response?.data?.message || error.message || '创建失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 格式化日期为字符串 (YYYY-MM-DD)
const formatDateToString = (date) => {
  if (!date) return '';

  // 如果是字符串，直接返回
  if (typeof date === 'string') {
    return date.split('T')[0]; // 取日期部分
  }

  // 如果是Date对象，格式化为YYYY-MM-DD
  if (date instanceof Date) {
    return date.toISOString().split('T')[0];
  }

  // 其他情况，尝试转换为Date对象
  try {
    return new Date(date).toISOString().split('T')[0];
  } catch (e) {
    console.error('日期格式化失败:', e);
    return '';
  }
};

// 处理批量上传
const handleBatchUpload = async () => {
  if (!batchForm.files || batchForm.files.length === 0) {
    Message.error('请选择要导入的文件');
    return;
  }

  loading.value = true;
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    Message.success('批量导入成功');
    emit('success');
    handleCancel();
  } catch (error) {
    Message.error('批量导入失败，请检查文件格式');
  } finally {
    loading.value = false;
  }
};

// 暴露方法
defineExpose({
  open
});
</script>

<style scoped>
.import-voucher-content {
  max-height: 600px;
  overflow-y: auto;
}

.upload-tabs {
  margin-bottom: 24px;
  text-align: center;
}

.batch-upload-tabs {
  margin-bottom: 16px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-text > div:first-child {
  font-size: 16px;
  color: #262626;
  margin-bottom: 8px;
}

.upload-tips {
  font-size: 12px;
  color: #8c8c8c;
}

.import-instructions {
  margin-top: 24px;
  padding: 16px;
  background: #f6f8fa;
  border-radius: 8px;
}

.import-instructions h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.import-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.import-instructions li {
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

:deep(.arco-form-item-label) {
  font-weight: 600;
}

:deep(.arco-radio-button) {
  border-radius: 6px !important;
}

:deep(.arco-radio-group-type-button .arco-radio-button:first-child) {
  border-radius: 6px 0 0 6px !important;
}

:deep(.arco-radio-group-type-button .arco-radio-button:last-child) {
  border-radius: 0 6px 6px 0 !important;
}
</style>
