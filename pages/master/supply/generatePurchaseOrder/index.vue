<template>
  <div>
    <div class="order-list-container">
      <!-- 顶部标签页 -->
      <div class="order-tabs">
        <div
          class="tab-item"
          :class="{ active: timeRange === 'recent6m' }"
          @click="switchTimeRange('recent6m')"
        >
          近6个月订单
        </div>
        <div
          class="tab-item"
          :class="{ active: timeRange === 'before6m' }"
          @click="switchTimeRange('before6m')"
        >
          6个月前订单
        </div>
      </div>
      <div class="search-area">
        <!-- 顶部统计卡片 -->
        <div class="stats-cards-container">
          <div
            class="stats-scroll-btn stats-scroll-left"
            @click="scrollStatsCards('left')"
          >
            <icon-left />
          </div>
          <div class="stats-cards" ref="statsCardsRef">
            <div
              v-for="card in statsCards"
              :key="card.id"
              class="stats-card"
              :class="{ active: activeStatsCard === card.id }"
              @click="setActiveStatsCard(card.id)"
            >
              <div class="stats-corner-mark" v-if="activeStatsCard === card.id">
                <icon-check class="check-icon" />
              </div>
              <div class="stats-title">{{ card.title }}</div>
              <div class="stats-value">{{ card.value }}</div>
            </div>
          </div>
          <div
            class="stats-scroll-btn stats-scroll-right"
            @click="scrollStatsCards('right')"
          >
            <icon-right />
          </div>
        </div>
        <!-- 搜索表单 -->
        <SearchForm
          :formData="searchForm"
          :formItems="formItems"
          :showAdvancedSearch="showAdvancedSearch"
          @update:formData="updateSearchForm"
          @search="handleSearch"
          @reset="resetSearch"
        >
        </SearchForm>
      </div>

      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :loading="tableLoading"
        :span-method="spanMethod"
        row-key="rowId"
        class="order-table"
        :hoverable="false"
      >
        <template #productInfo-cell="{ record }">
          <div v-if="record.type === 'header'" class="order-header-cell">
            <div class="order-header-left">
              <span>系统订单编号： {{ record.id }}</span>
              <span v-if="record.thirdPartyOrderSn"
                >三方订单编号： {{ record.thirdPartyOrderSn }}</span
              >
              <span>采购订单编号： {{ record.purchaseOrderNumber }}</span>

              <span v-if="record.channelName || record.channelIconUrl">
                渠道：{{ record.channelName }}
                <i class="iconfont" :class="record.channelIconUrl"></i>
              </span>
              <span v-if="record.platformName">
                平台名称：{{ record.platformName }}
              </span>

              <span v-if="record.storeName">
                店铺名称：{{ record.storeName }}
              </span>

              <span v-if="record.remark" class="order-remark"
                >备注：{{ record.remark }}</span
              >

              <span class="order-time-inline"
                >下单时间： {{ record.createdAt }}</span
              >
            </div>
          </div>
          <div v-else class="product-info-cell-content">
            <a-image
              width="80"
              height="60"
              :src="record.imageUrl"
              class="product-image"
            />
            <div class="product-details">
              <span class="product-name">{{ record.productName }}</span>
              <span style="color: #999">
                {{ record.color }}
              </span>
              <div class="product-meta">
                <div>系统SPU：{{ record.goodsSpuId }}</div>
                <div>系统SKU：{{ record.goodsSkuId }}</div>
                <div v-if="record.thirdPartySpuId">
                  三方SPU：{{ record.thirdPartySpuId }}
                </div>
                <div v-if="record.thirdPartySkuId">
                  三方SKU：{{ record.thirdPartySkuId }}
                </div>
              </div>
            </div>
          </div>
        </template>

        <template #priceQuantity-cell="{ record }">
          <div v-if="record.type === 'details'" class="price-cell-content">
            <div class="price">¥{{ record.price }}</div>
            <div class="quantity">x{{ record.quantity }}</div>
          </div>
        </template>

        <template #receiver-cell="{ record }">
          <div
            v-if="record.type === 'details'"
            class="payment-cell-content"
            style="text-align: center"
          >
            <div class="address" :title="record.recipientName">
              {{ record.recipientName }}
            </div>
            <div class="address" :title="record.recipientPhone">
              {{ record.recipientPhone }}
            </div>
            <div class="address" :title="record.address">
              {{ record.address }}
            </div>
          </div>
        </template>

        <template #payment-cell="{ record }">
          <div class="price highlight" v-if="record.type === 'details'">
            ¥{{ record.totalAmount }}
          </div>
        </template>

        <template #purchaser-cell="{ record }">
          <div v-if="record.type === 'details'" class="purchaser-cell-content">
            <div v-if="record.purchaserInfo" class="purchaser-info">
              <div class="purchaser-name">
                {{ record.purchaserInfo.purchaserName || "-" }}
              </div>
              <div class="purchaser-details">
                <div v-if="record.purchaserInfo.follower" class="follower">
                  跟单员: {{ record.purchaserInfo.follower }}
                </div>
              </div>
            </div>
            <div
              v-else-if="record.purchaseOrderNumber"
              class="loading-purchaser"
            >
              加载中...
            </div>
            <div v-else class="no-purchaser">-</div>
          </div>
        </template>

        <template #status-cell="{ record }">
          <div v-if="record.type === 'details'" class="status-cell-content">
            <div class="status-text">
              {{
                record.orderStatusText === "退款中" &&
                record.paymentStatusText === "已退款"
                  ? "已退款"
                  : record.orderStatusText
              }}
            </div>
          </div>
        </template>

        <template #delivery-method-cell="{ record }">
          <div
            v-if="record.type === 'details'"
            class="status-tag"
            style="
              display: block;
              text-align: center;
              color: #1d2129;
              font-weight: 500;
            "
          >
            <!-- 如果有包裹信息，显示包裹列表 -->
            <div v-if="record.packages && record.packages.length > 0">
              <div
                v-for="(pkg, index) in record.packages"
                :key="pkg.id"
                style="margin-bottom: 8px"
              >
                <div style="font-weight: 600">
                  <!-- 只有多个包裹时才显示包裹编号 -->
                  <template v-if="record.packages.length > 1">
                    包裹 {{ index + 1 }}：{{
                      pkg.shippingMethodText || "未设置"
                    }}
                  </template>
                  <template v-else>
                    {{ pkg.shippingMethodText || "未设置" }}
                  </template>
                </div>
                <div
                  v-if="pkg.shippingCompanyName && pkg.trackingNumber"
                  style="font-size: 12px; color: #86909c; margin-top: 2px"
                >
                  {{ pkg.shippingCompanyName }}-{{ pkg.trackingNumber }}
                </div>
                <div
                  v-else-if="pkg.shippingMethodText === '商家自送'"
                  style="font-size: 12px; color: #86909c; margin-top: 2px"
                >
                  商家自送
                </div>
              </div>
            </div>
            <!-- 如果没有包裹信息，显示原有逻辑 -->
            <div v-else>
              {{ record.shippingMethodText || "暂无配送信息" }}
            </div>
          </div>
        </template>

        <!-- 操作列 -->
        <template #operations-cell="{ record }">
          <div class="operations-cell-content">
            <div class="operation-buttons">
              <a-button
                v-if="
                  (record.orderStatus === 1 && !record.purchaseOrderNumber) ||
                  record.paymentStatus === 7
                "
                type="primary"
                size="small"
                @click="handlePurchaseApplication(record)"
              >
                申请采购
              </a-button>
              <a-button
                v-if="
                  record.purchaseOrderNumber !== null &&
                  record.purchaseOrderNumber !== ''
                "
                type="primary"
                status="danger"
                size="small"
                :loading="
                  afterSalesSubmitting &&
                  currentOrderData?.id === record.originalId
                "
                :disabled="afterSalesSubmitting"
                @click="handleAfterSalesGeneration(record)"
              >
                申请售后
              </a-button>
              <a-button
                v-if="
                  record.purchaseOrderNumber !== null &&
                  record.purchaseOrderNumber !== ''
                "
                type="primary"
                size="small"
                @click="handleViewReplyHistory(record)"
              >
                查看回复
              </a-button>
            </div>
          </div>
        </template>

        <template #purchaser-progress-cell="{ record }">
          <div v-if="record.type === 'details'" class="status-cell-content">
            <div class="status-text">
              {{ record.purchaseProgress }}
            </div>
            <div
              v-if="record.purchaseProgress === '货期' && record.deliveryTime"
              v-time="record.deliveryTime"
            ></div>
          </div>
        </template>
      </a-table>
      <!-- 分页组件 -->
      <div class="pagination-container fixed-pagination">
        <a-pagination
          :total="totalItems"
          show-total
          show-jumper
          size="small"
          :page-size="pageSize"
          show-page-size
          :current="currentPage"
          @change="handlePageChange"
          @pageSizeChange="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 申请采购弹窗 -->
    <PurchaseApplicationModal
      v-model="purchaseApplicationVisible"
      :order-data="currentOrderData"
      @submit="handlePurchaseApplicationSubmit"
    />

    <!-- 申请售后弹窗 -->
    <AfterSalesApplicationModal
      v-model="afterSalesApplicationVisible"
      :order-data="currentOrderData"
      @submit="handleAfterSalesApplicationSubmit"
      @update:modelValue="handleAfterSalesModalClose"
    />

    <!-- 查看回复历史弹窗 -->
    <ReplyHistoryModal
      v-model="replyHistoryVisible"
      :after-sales-data="currentReplyData"
      type="purchaseOrder"
    />
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { IconRight, IconLeft, IconCheck } from "@arco-design/web-vue/es/icon";
import { Message, Modal } from "@arco-design/web-vue";
import SearchForm from "./components/SearchForm.vue";
import PurchaseApplicationModal from "./components/PurchaseApplicationModal.vue";
import AfterSalesApplicationModal from "./components/AfterSalesApplicationModal.vue";
import ReplyHistoryModal from "../afterSales/components/ReplyHistoryModal/index.vue";
import orderApi from "@/api/master/order";
import { purchaseOrderApi } from "@/api/master/csm";
import { PaymentMethodEnum } from "@/constants/PaymentMethodEnum";

// 定义页面路由元信息
definePageMeta({
  name: "generatePurchaseOrder",
  path: "/master/supply/generatePurchaseOrder",
});

// 获取路由实例
const router = useRouter();

onMounted(() => {
  getChannelOptions();
  getOrders();
});

// 表格加载状态
const tableLoading = ref(false);

// 获取渠道选项
const getChannelOptions = async () => {
  try {
    const res = await orderApi.getChannelList({
      page: 1,
      pageSize: 100,
    });

    if (res && res.data && res.data.items && Array.isArray(res.data.items)) {
      // 找到渠道字段并更新其options
      const channelField = formItems.find((item) => item.field === "channelId");
      if (channelField) {
        channelField.options = [
          { label: "全部", value: "全部" },
          ...res.data.items.map((item) => ({
            label: item.name,
            value: item.id,
          })),
        ];
      }
    }
  } catch (error) {
    console.error("获取渠道列表失败:", error);
  }
};

const getOrders = () => {
  // 显示加载状态
  tableLoading.value = true;

  // 构建请求参数
  const params = {
    timeRange: timeRange.value,
    orderNumber: searchForm.value.orderNumber,
    productName: searchForm.value.productName,
    buyerPhone: searchForm.value.buyerPhone,
    address: searchForm.value.address,
    channelId:
      searchForm.value.channelId === "全部" ? null : searchForm.value.channelId,
    // 当统计卡片不是"all"时，优先使用统计卡片ID映射到的订单状态值；否则使用搜索表单中的订单状态
    orderStatus:
      activeStatsCard.value !== "all"
        ? statsCardToOrderStatus[activeStatsCard.value]
        : searchForm.value.orderStatus === "全部"
        ? null
        : searchForm.value.orderStatus,
    paymentMethod:
      searchForm.value.paymentMethod === ""
        ? null
        : searchForm.value.paymentMethod,
    shippingStatus:
      searchForm.value.shippingStatus === "全部"
        ? null
        : searchForm.value.shippingStatus,
    // 将日期转换为时间戳格式
    startTime: searchForm.value.startTime
      ? new Date(searchForm.value.startTime).getTime()
      : null,
    endTime: searchForm.value.endTime
      ? new Date(searchForm.value.endTime).getTime()
      : null,
    // 添加分页参数
    page: currentPage.value,
    pageSize: pageSize.value,
  };
  orderApi
    .getOrders(params)
    .then((res) => {
      if (res.code === 200 && res.data && res.data.items) {
        // 转换订单数据，后端已经包含了采购员信息
        originalOrders.value = res.data.items.map((order) =>
          transformOrderData(order)
        );

        // 更新分页信息
        if (res.data.pageInfo) {
          totalItems.value = res.data.pageInfo.total || 0;
          totalPages.value = res.data.pageInfo.totalPage || 1;
          currentPage.value = res.data.pageInfo.currentPage || 1;
        }
        // 获取订单数据后再获取统计数据
        getOrderStats();
      } else {
        console.error("获取订单数据失败", res);
      }
      tableLoading.value = false;
    })
    .catch((err) => {
      console.error("获取订单数据失败", err);
      tableLoading.value = false;
    });
};

// 将API返回的订单数据转换为表格需要的格式
const transformOrderData = (order) => {
  // 构建商品数据
  const products =
    order.items && order.items.length > 0
      ? order.items.map((item) => ({
          productId: item.id,
          productName: item.productName,
          imageUrl: item.productImage || "/placeholder.png",
          price: item.unitPrice,
          quantity: item.quantity,
          goodsSpuId: item.goodsSpuId || "",
          goodsSkuId: item.goodsSkuId || "",
          thirdPartySpuId: item.thirdPartySpuId || "",
          thirdPartySkuId: item.thirdPartySkuId || "",
          color: item.skuSpecifications || "",
        }))
      : [];

  // 获取收货地址信息
  const shipping = order.shipping || {};
  const address = shipping.id
    ? shipping.streetAddress || "未设置收货地址"
    : "未设置收货地址";
  const recipientName = shipping.recipientName || "";
  const recipientPhone = shipping.recipientPhone || "";

  // 配送状态映射
  const getShippingStatusText = (status) => {
    const statusMap = {
      0: "未发货",
      1: "已发货",
      2: "部分发货",
      3: "已收货",
      4: "退货中",
      5: "已退货",
    };
    return statusMap[status] || "未知状态";
  };

  return {
    id: order.id,
    purchaseProgress: order.purchaseProgress,
    deliveryTime: order.deliveryTime,
    thirdPartyOrderSn: order.thirdPartyOrderSn || "",
    channelName: order.channelName || "",
    platformName: order.platformName || "",
    purchaseOrderNumber: order.purchaseOrderNumber || "",
    purchaserInfo: order.purchaserInfo || null, // 添加采购员信息
    storeName: order.storeName || "",
    channelIconUrl: order.channelIconUrl || "",
    totalAmount: order.totalAmount,
    address: address,
    recipientName: recipientName,
    recipientPhone: recipientPhone,
    userId: order.userId || "",
    remark: order.remark || "",
    orderStatus: order.orderStatus,
    orderStatusText: order.orderStatusText || "未知状态",
    paymentStatusText: order.paymentStatusText || "未知状态",
    shippingStatus: order.shippingStatus,
    shippingStatusText:
      order.shippingStatusText || getShippingStatusText(order.shippingStatus),
    createdAt: formatTime(order.createdAt),
    shipping: order.shipping || {}, // 添加shipping字段
    packages: order.packages || [], // 添加packages字段
    products:
      products.length > 0
        ? products
        : [
            {
              productId: "unknown",
              productName: "无商品信息",
              imageUrl: "/placeholder.png",
              price: "0.00",
              quantity: 0,
              color: "",
            },
          ],
  };
};

// 格式化时间戳
const formatTime = (timestamp) => {
  if (!timestamp) return "-";
  const date = new Date(Number(timestamp));
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(/\//g, "-");
};

// 弹窗状态
const purchaseApplicationVisible = ref(false);
const afterSalesApplicationVisible = ref(false);
const replyHistoryVisible = ref(false);
const currentOrderData = ref(null);
const currentReplyData = ref({});

// 申请售后按钮加载状态
const afterSalesSubmitting = ref(false);

// 申请采购处理函数
const handlePurchaseApplication = (record) => {
  // 获取完整的订单数据
  const orderData = originalOrders.value.find(
    (order) => order.id === record.originalId
  );
  currentOrderData.value = orderData;
  purchaseApplicationVisible.value = true;
};

// 申请采购提交处理
const handlePurchaseApplicationSubmit = async (data) => {
  try {
    // 调用API提交申请采购数据
    const response = await purchaseOrderApi.applyPurchaseOrder(data);

    if (response.code === 200) {
      Message.success(response.message || "申请采购成功");
      // 关闭弹窗
      purchaseApplicationVisible.value = false;
      // 刷新订单列表
      getOrders();
    } else {
      Message.error(response.message || "申请采购失败");
    }
  } catch (error) {
    console.error("申请采购失败:", error);
    Message.error(error?.message || "申请采购失败，请稍后重试");
  }
};

/**
 * 申请售后处理函数
 * 功能说明：
 * 1. 验证订单状态（必须是已关闭状态：orderStatus === 5）
 * 2. 验证是否有采购订单号
 * 3. 打开售后申请弹窗
 * 4. 设置按钮加载状态防止重复点击
 */
const handleAfterSalesGeneration = (record) => {
  try {
    // 获取完整的订单数据
    const orderData = originalOrders.value.find(
      (order) => order.id === record.originalId
    );

    if (!orderData) {
      Message.error("未找到订单数据，请刷新页面后重试");
      return;
    }

    // 检查是否有采购订单号
    if (!orderData.purchaseOrderNumber) {
      Message.error("该订单没有关联的采购订单，无法申请售后");
      return;
    }

    currentOrderData.value = orderData;
    afterSalesSubmitting.value = true; // 设置加载状态
    afterSalesApplicationVisible.value = true;
  } catch (error) {
    console.error("打开售后申请弹窗失败:", error);
    Message.error("打开售后申请弹窗失败，请稍后重试");
  }
};

/**
 * 申请售后提交处理函数
 * 功能说明：
 * 1. 处理售后申请弹窗的提交结果
 * 2. 成功时关闭弹窗、刷新订单列表、显示成功消息
 * 3. 可选择跳转到售后页面查看详情
 * 4. 失败时记录错误日志
 * 5. 重置加载状态
 */
const handleAfterSalesApplicationSubmit = async (result) => {
  try {
    if (result.success) {
      // 申请成功，刷新订单列表

      // 关闭弹窗
      afterSalesApplicationVisible.value = false;

      // 重置加载状态
      afterSalesSubmitting.value = false;

      // 刷新订单列表以显示最新状态
      getOrders();

      // 显示详细的成功消息
      const afterSalesNumber = result.data?.afterSalesNumber;
      const successMessage = afterSalesNumber
        ? `售后申请提交成功！售后编号：${afterSalesNumber}`
        : "售后申请提交成功！";

      Message.success({
        content: successMessage,
        duration: 5000, // 显示5秒
      });

      // 可以在这里添加其他成功后的操作
      // 比如跳转到售后页面、发送通知等

      // 可选：询问用户是否要跳转到售后页面查看详情
      if (afterSalesNumber) {
        setTimeout(() => {
          // 使用Modal确认是否跳转
          Modal.confirm({
            title: "申请成功",
            content: `售后申请已提交成功（编号：${afterSalesNumber}），是否要跳转到售后页面查看详情？`,
            okText: "立即查看",
            cancelText: "稍后查看",
            onOk: () => {
              // 跳转到售后页面
              router.push("/master/supply/afterSales");
            },
          });
        }, 1000); // 延迟1秒显示，让用户先看到成功消息
      }
    } else {
      // 申请失败，错误信息已经在弹窗组件中显示了
      console.error("售后申请失败:", result.error);

      // 重置加载状态
      afterSalesSubmitting.value = false;

      // 可以在这里添加额外的错误处理逻辑
      // 比如记录错误日志、发送错误报告等
      if (result.error && result.error.response) {
        // 记录详细的错误信息用于调试
        console.error("售后申请API错误详情:", {
          status: result.error.response.status,
          data: result.error.response.data,
          url: result.error.config?.url,
        });
      }
    }
  } catch (error) {
    console.error("处理售后申请结果失败:", error);

    // 重置加载状态
    afterSalesSubmitting.value = false;

    Message.error("处理售后申请结果失败，请稍后重试");
  }
};

// 申请售后弹窗关闭处理
const handleAfterSalesModalClose = (visible) => {
  if (!visible) {
    // 弹窗关闭时重置状态
    afterSalesSubmitting.value = false;
    currentOrderData.value = null;
  }
};

/**
 * 查看回复历史按钮处理函数
 * 功能说明：
 * 1. 获取完整的订单数据
 * 2. 构造售后数据格式（兼容ReplyHistoryModal组件）
 * 3. 打开回复历史弹窗
 */
const handleViewReplyHistory = (record) => {
  try {
    // 获取完整的订单数据
    const orderData = originalOrders.value.find(
      (order) => order.id === record.originalId
    );

    if (!orderData) {
      Message.error("未找到订单数据，请刷新页面后重试");
      return;
    }

    // 检查是否有采购订单号
    if (!orderData.purchaseOrderNumber) {
      Message.error("该订单没有关联的采购订单，无法查看回复历史");
      return;
    }

    // 构造售后数据格式，兼容ReplyHistoryModal组件
    // ReplyHistoryModal期望的是afterSalesData格式，我们需要将订单数据转换为售后数据格式
    // 使用表格数据中的originalId字段作为接口参数，通过id字段传递给统一组件
    const afterSalesData = {
      id: record.originalId, // 使用表格数据中的originalId作为id，这样统一组件就会用这个值调用接口
      orderNumber: orderData.id, // 系统订单编号
      purchaseOrderNumber: orderData.purchaseOrderNumber, // 采购订单编号
      thirdPartyOrderSn: orderData.thirdPartyOrderSn, // 三方订单编号
      channelName: orderData.channelName,
      platformName: orderData.platformName,
      storeName: orderData.storeName,
      totalAmount: orderData.totalAmount,
      address: orderData.address,
      recipientName: orderData.recipientName,
      recipientPhone: orderData.recipientPhone,
      createdAt: orderData.createdAt,
      products: orderData.products || [],
    };

    currentReplyData.value = afterSalesData;
    replyHistoryVisible.value = true;
  } catch (error) {
    console.error("打开回复历史弹窗失败:", error);
    Message.error("打开回复历史弹窗失败，请稍后重试");
  }
};

const showAdvancedSearch = ref(false);
// 搜索表单项定义
const formItems = reactive([
  {
    field: "channelId",
    label: "订单来源",
    type: "select",
    options: [{ label: "全部", value: "全部" }],
    defaultValue: "全部",
    span: 1,
  },
  {
    field: "orderNumber",
    label: "订单编号",
    type: "input",
    placeholder: "请输入",
    span: 1,
  },
  {
    field: "productName",
    label: "商品名称",
    type: "input",
    placeholder: "请输入",
    span: 1,
  },
  {
    field: "buyerPhone",
    label: "买家手机",
    type: "input",
    placeholder: "请输入",
    span: 1,
  },
  {
    field: "address",
    label: "收货地址",
    type: "input",
    placeholder: "请输入",
    span: 1,
  },
  {
    field: "orderStatus",
    label: "订单状态",
    type: "select",
    placeholder: "全部",
    options: [
      { label: "全部", value: "全部" },
      { label: "待付款", value: "0" },
      { label: "待发货", value: "1" },
      { label: "已发货", value: "2" },
      { label: "交易成功", value: "3" },
      { label: "已关闭", value: "4" },
      { label: "退款中", value: "5" },
    ],
    defaultValue: "全部",
    span: 1,
  },

  {
    field: "paymentMethod",
    label: "支付方式",
    type: "select",
    placeholder: "请选择",
    options: PaymentMethodEnum.getAllOptions(),
    defaultValue: "",
    span: 1,
  },

  {
    field: "shippingStatus",
    label: "配送状态",
    type: "select",
    placeholder: "全部",
    options: [
      { label: "全部", value: "全部" },
      { label: "未发货", value: "0" },
      { label: "已发货", value: "1" },
      { label: "部分发货", value: "2" },
      { label: "已收货", value: "3" },
      { label: "退货中", value: "4" },
      { label: "已退货", value: "5" },
    ],
    defaultValue: "全部",
    span: 1,
  },
  {
    field: "createdAt",
    label: "下单时间",
    isTimeRange: true,
    timePresetField: "timeRange",
    startDateField: "startTime",
    endDateField: "endTime",
    span: 2,
  },
]);

// 搜索表单字段
const searchForm = ref({
  orderNumber: "",
  productName: "",
  buyerPhone: "",
  address: "",
  orderStatus: "全部",
  paymentMethod: "",
  shippingStatus: "全部",
  timeRange: "recent6m",
  startTime: null,
  endTime: null,
});

// 更新搜索表单数据的方法
const updateSearchForm = (newVal) => {
  searchForm.value = { ...newVal };
};

const handleSearch = (formData) => {
  // 更新搜索表单数据
  searchForm.value = { ...searchForm.value, ...formData };
  currentPage.value = 1;
  getOrders();
};

const resetSearch = () => {
  // 重置页码
  currentPage.value = 1;
  // 确保所有搜索参数都被清空
  Object.keys(searchForm.value).forEach((key) => {
    if (key === "timeRange") {
      searchForm.value[key] = "recent6m";
    } else if (key === "startTime" || key === "endTime") {
      searchForm.value[key] = null;
    } else if (key.includes("status") || key === "channelId") {
      searchForm.value[key] = "全部";
    } else if (key === "paymentMethod") {
      searchForm.value[key] = "";
    } else {
      searchForm.value[key] = "";
    }
  });
  getOrders();
};

// 存储API获取的订单数据
const originalOrders = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const totalPages = ref(1);

const tableData = computed(() => {
  const data = [];
  originalOrders.value.forEach((order) => {
    const productCount = order.products.length;
    data.push({
      // 表头行数据
      rowId: `${order.id}-header`, // 表头唯一ID
      type: "header",
      originalId: order.id,
      id: order.id, // 添加id字段，用于显示系统订单编号
      purchaseProgress: order.purchaseProgress,
      deliveryTime: order.deliveryTime,
      followers: order.followers,
      channelId: order.channelId,
      channelIconUrl: order.channelIconUrl,
      // 使用id作为系统订单编号，不再需要单独的orderSn字段
      thirdPartyOrderSn: order.thirdPartyOrderSn,
      purchaseOrderNumber: order.purchaseOrderNumber,
      channelName: order.channelName || "", // 添加渠道名称字段
      platformName: order.platformName || "", // 添加平台名称字段
      storeName: order.storeName || "", // 添加店铺名称字段
      orderSourceText: order.orderSourceText,
      createdAt: order.createdAt,
      remark: order.remark, // 添加备注字段
      productCount: productCount, // 用于合并单元格计算
    });
    // 详情行数据 (每个商品一行)
    order.products.forEach((product, productIndex) => {
      data.push({
        // 订单级信息 (用于合并单元格或在详情行显示)
        originalId: order.id,
        // 使用id作为系统订单编号
        followers: order.followers,
        thirdPartyOrderSn: order.thirdPartyOrderSn,
        purchaserInfo: order.purchaserInfo, // 添加采购员信息
        channelName: order.channelName || "", // 添加渠道名称字段
        platformName: order.platformName || "", // 添加平台名称字段
        storeName: order.storeName || "", // 添加店铺名称字段
        orderSourceText: order.orderSourceText,
        paymentMethodText: order.paymentMethodText,
        shippingMethodText: order.shipping?.shippingMethodText || "", // 配送方式字段
        packages: order.packages || [], // 添加包裹信息字段
        totalAmount: order.totalAmount,
        paymentSn: order.paymentSn,
        address: order.address,
        recipientName: order.recipientName,
        recipientPhone: order.recipientPhone,
        userId: order.userId,
        orderStatus: order.orderStatus,
        orderStatusText: order.orderStatusText,
        paymentStatusText: order.paymentStatusText,
        shippingStatusText: order.shippingStatusText, // 添加配送状态字段
        invoiceStatus: order.invoiceStatus, // 添加开票状态字段
        invoiceStatusText: order.invoiceStatusText, // 添加开票状态文本
        deliveryTime: order.deliveryTime,
        trackingNumber: order.trackingNumber,
        isShippable: order.isShippable, // 订单级别的可发货状态
        hasAddressEdit: order.hasAddressEdit,
        hasLogisticsCheck: order.hasLogisticsCheck,
        // 商品级信息
        ...product,
        // 表格行属性
        rowId: `${order.id}-${product.productId}-details`, // 详情行唯一ID (订单ID + 商品ID)
        type: "details",
        productIndex: productIndex, // 商品在订单中的索引 (0-based)
        productCount: productCount, // 订单总商品数
        purchaseProgress: order.purchaseProgress, // 添加采购进度字段
        deliveryTime: order.deliveryTime, // 添加采购进度字段
      });
    });
  });

  return data;
});

const columns = reactive([
  {
    title: "商品信息",
    slotName: "productInfo-cell",
    width: 380,
  },
  {
    title: "单价/数量",
    slotName: "priceQuantity-cell",
    width: 120,
    align: "center",
  },
  { title: "收件信息", slotName: "receiver-cell", width: 150, align: "center" },
  { title: "实付金额 ", slotName: "payment-cell", width: 130, align: "center" },
  {
    title: "采购员信息",
    slotName: "purchaser-cell",
    width: 120,
    align: "center",
  },
  { title: "采购进度", slotName: "purchaser-progress-cell", width: 130 },
  { title: "订单状态", slotName: "status-cell", width: 150 },
  {
    title: "配送方式",
    slotName: "delivery-method-cell",
    width: 120,
    align: "center",
  },
  {
    title: "操作",
    slotName: "operations-cell",
    width: 150,
    align: "center",
    fixed: "right",
  },
]);

// 用于合并单元格的 Span 方法
const spanMethod = ({ record, column, rowIndex }) => {
  if (record.type === "header") {
    // 表头行: 第一列合并所有列
    if (column.slotName === "productInfo-cell") {
      return { rowspan: 1, colspan: columns.length };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  if (record.type === "details") {
    // 详情行: 对特定列进行垂直合并
    const isFirstProduct = record.productIndex === 0;
    const rowspan = isFirstProduct ? record.productCount : 0;
    // 需要垂直合并的列 (订单级别信息)
    const colsToSpan = [
      "payment-cell",
      "receiver-cell",
      "purchaser-cell",
      "status-cell",
      "delivery-method-cell",
      "operations-cell",
      "purchaser-progress-cell",
    ];

    if (colsToSpan.includes(column.slotName)) {
      return { rowspan: rowspan, colspan: 1 };
    }
  }
  // 其他单元格不合并
  return { rowspan: 1, colspan: 1 };
};

// --- 分页处理程序 ---
const handlePageChange = (page) => {
  currentPage.value = page;
  getOrders();
};

const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  getOrders();
};

// 统计卡片ID到订单状态值的映射
const statsCardToOrderStatus = {
  all: "全部",
  unpaid: "0",
  toBeShipped: "1",
  shipped: "2",
  success: "3",
  closed: "4",
  refunding: "5",
};

// 统计卡片数据
const statsCards = reactive([
  { id: "all", title: "全部订单", value: "0" },
  { id: "unpaid", title: "待付款", value: "0" },
  { id: "toBeShipped", title: "待发货", value: "0" },
  { id: "shipped", title: "已发货", value: "0" },
  { id: "closed", title: "已关闭", value: "0" },
  { id: "success", title: "交易成功", value: "0" },
  { id: "refunding", title: "退款中", value: "0" },
]);

// 获取订单统计数据
const getOrderStats = () => {
  orderApi
    .getOrderBadgeStats({ timeRange: timeRange.value })
    .then((res) => {
      if (res.code === 200 && res.data) {
        // 更新统计卡片数据
        statsCards.forEach((card) => {
          switch (card.id) {
            case "all":
              card.value = (res.data.all || 0).toString();
              break;
            case "unpaid":
              card.value = (res.data.unpaid || 0).toString();
              break;
            case "toBeShipped":
              card.value = (res.data.toBeShipped || 0).toString();
              break;
            case "shipped":
              card.value = (res.data.shipped || 0).toString();
              break;
            case "closed":
              card.value = (res.data.closed || 0).toString();
              break;
            case "success":
              card.value = (res.data.success || 0).toString();
              break;
            case "refunding":
              card.value = (res.data.refunding || 0).toString();
              break;
          }
        });
      } else {
        console.error("获取订单统计数据失败", res);
      }
    })
    .catch((err) => {
      console.error("获取订单统计数据失败", err);
    });
};

// 当前选中的统计卡片
const activeStatsCard = ref("all");
// 当前时间范围
const timeRange = ref("recent6m"); // 默认近6个月

// 设置当前选中的统计卡片
const setActiveStatsCard = (id) => {
  activeStatsCard.value = id;
  currentPage.value = 1;
  if (searchForm.value.orderStatus !== "全部") {
    searchForm.value.orderStatus = "全部";
  }
  getOrders();
};

// 切换时间范围
const switchTimeRange = (range) => {
  timeRange.value = range;
  activeStatsCard.value = "all";
  getOrders();
};

// 统计卡片滚动相关
const statsCardsRef = ref(null);

// 滚动统计卡片
const scrollStatsCards = (direction) => {
  if (!statsCardsRef.value) return;
  const scrollAmount = 200; // 每次滚动的像素数
  const currentScroll = statsCardsRef.value.scrollLeft;
  if (direction === "left") {
    statsCardsRef.value.scrollTo({
      left: Math.max(0, currentScroll - scrollAmount),
      behavior: "smooth",
    });
  } else {
    statsCardsRef.value.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: "smooth",
    });
  }
};
</script>

<style scoped lang="less">
@import "./index.css";
</style>
