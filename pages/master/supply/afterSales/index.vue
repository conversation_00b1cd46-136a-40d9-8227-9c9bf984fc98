<template>
  <div class="ma-content-block p-4">
    <!-- 使用ma-crud表格组件 -->
    <SimpleTable
      :key="tableKey"
      :data="tableData"
      :pagination="pagination"
      :loading="loading"
      @detail="handleDetail"
      @reply="handleReply"
      @remark="handleAfterSalesRemark"
      @history="handleReplyHistory"
      @change-staff="handleChangeStaff"
      @copy-issue="handleCopyIssue"
      @request="handleRequest"
      @status-change="handleStatusChange"
      @view-product-detail="handleViewProductDetail"
    />

    <!-- 详情抽屉 -->
    <OrderDetailDrawer
      v-model="detailVisible"
      :order-id="currentOrderId"
      @edit="handleEditOrder"
      @action="handleOrderAction"
    />

    <!-- 回复弹窗 -->
    <ReplyModal
      v-model="replyVisible"
      :after-sales-data="currentRecord"
      @submit="handleReplySubmit"
    />

    <!-- 售后备注弹窗 -->
    <AfterSalesRemarkModal
      v-model="remarkVisible"
      :after-sales-data="currentRecord"
      @submit="handleRemarkSubmit"
    />

    <!-- 回复历史弹窗 -->
    <ReplyHistoryModal
      v-model="historyVisible"
      :after-sales-data="currentRecord"
    />

    <!-- 更换售后员弹窗 -->
    <ChangeStaffModal
      v-model="staffVisible"
      :after-sales-data="currentRecord"
      @submit="handleStaffSubmit"
    />

    <!-- 复制问题弹窗 -->
    <CopyIssueModal
      v-model="copyVisible"
      :after-sales-data="currentRecord"
      @copy="handleCopySubmit"
    />

    <!-- 商品详情抽屉 -->
    <ProductDetailDrawer
      v-model:visible="productDetailVisible"
      :product-id="currentProductId"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { afterSalesApi } from '@/api/master/csm/afterSales.js'

// 导入组件
import SimpleTable from './components/AfterSalesTable/simple.vue'
import OrderDetailDrawer from './components/OrderDetailDrawer/index.vue'
import ReplyModal from './components/ReplyModal/index.vue'
import AfterSalesRemarkModal from './components/AfterSalesRemarkModal/index.vue'
import ReplyHistoryModal from './components/ReplyHistoryModal/index.vue'
import ChangeStaffModal from './components/ChangeStaffModal/index.vue'
import CopyIssueModal from './components/CopyIssueModal/index.vue'
import ProductDetailDrawer from '~/components/base/ProductDetailDrawer/index.vue'

// 定义页面路由元信息
definePageMeta({
  name: "afterSales",
  path: "/master/supply/afterSales",
});

// 路由实例
const router = useRouter()

// 页面状态
const loading = ref(false)
const tableKey = ref(0) // 用于强制刷新表格组件

// 弹窗控制
const detailVisible = ref(false)
const replyVisible = ref(false)
const remarkVisible = ref(false)
const historyVisible = ref(false)
const staffVisible = ref(false)
const copyVisible = ref(false)
const productDetailVisible = ref(false)

// 当前操作的记录
const currentRecord = ref({})
const currentOrderId = ref(null)
const currentProductId = ref(null)

// 表格数据
const tableData = ref([])
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10
})

// 请求处理
const handleRequest = async (params) => {
  console.log('请求参数:', params)
  loading.value = true

  try {
    // 调用真实的售后申请API
    const response = await afterSalesApi.getAfterSalesApplications(params)

    if (response.code === 200) {
      console.log('获取售后申请列表成功:', response.data)

      // 更新表格数据
      tableData.value = response.data.items || []

      // 更新分页信息
      if (response.data.pageInfo) {
        
        pagination.value = {
          total: response.data.pageInfo.total,
          current: response.data.pageInfo.currentPage,
          pageSize: response.data.pageInfo.pageSize
        }
      }

      console.log('表格数据已更新:', tableData, '条记录')
    } else {
      Message.error(response.message || '获取售后申请列表失败')
      // 清空数据
      tableData.value = []
      pagination.value.total = 0
    }
  } catch (error) {
    console.error('获取售后申请列表失败:', error)
    Message.error('获取售后申请列表失败，请稍后重试')
    // 清空数据
    tableData.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 操作事件处理函数
const handleDetail = (record) => {
  currentRecord.value = record
  currentOrderId.value = record.id
  detailVisible.value = true
}

const handleReply = (record) => {
  console.log('主页面 - 处理回复事件:', record)
  currentRecord.value = record
  replyVisible.value = true
  console.log('主页面 - replyVisible设置为:', replyVisible.value)
}

const handleAfterSalesRemark = (record) => {
  console.log('主页面 - 处理备注事件:', record)
  currentRecord.value = record
  remarkVisible.value = true
  console.log('主页面 - remarkVisible设置为:', remarkVisible.value)
}

const handleReplyHistory = (record) => {
  console.log('主页面 - 处理历史事件:', record)
  currentRecord.value = record
  historyVisible.value = true
  console.log('主页面 - historyVisible设置为:', historyVisible.value)
}

const handleChangeStaff = (record) => {
  console.log('主页面 - 处理更换售后员事件:', record)
  currentRecord.value = record
  staffVisible.value = true
  console.log('主页面 - staffVisible设置为:', staffVisible.value)
}

const handleCopyIssue = (record) => {
  console.log('主页面 - 处理复制问题事件:', record)
  currentRecord.value = record
  copyVisible.value = true
  console.log('主页面 - copyVisible设置为:', copyVisible.value)
}

// 表单提交处理
const handleReplySubmit = (data) => {
  console.log('提交回复:', data)
  Message.success('回复提交成功')
}

const handleRemarkSubmit = async (data) => {
  console.log('更新售后备注:', data)

  if (!data.afterSalesId) {
    Message.error('售后申请ID不能为空')
    return
  }

  if (!data.remark || !data.remark.trim()) {
    Message.error('备注内容不能为空')
    return
  }

  try {
    // 调用添加售后备注API
    const response = await afterSalesApi.addAfterSalesRemark(data.afterSalesId, {
      remark: data.remark.trim(),
      attachments: data.attachments || []
    })

    if (response.code === 200) {
      Message.success('售后备注添加成功')

      // 强制刷新表格数据
      console.log('开始刷新表格数据...')
      console.log('刷新前数据长度:', tableData.value.length)

      // 方法1：先清空数据，强制触发响应式更新
      tableData.value = []

      // 方法2：强制刷新组件
      tableKey.value++
      console.log('强制刷新组件，新key:', tableKey.value)

      // 方法3：重新请求数据
      await handleRequest({
        page: pagination.value.current,
        pageSize: pagination.value.pageSize
      })

      console.log('刷新后数据长度:', tableData.value.length)
      console.log('售后备注添加成功，表格数据已刷新')
    } else {
      Message.error(response.message || '售后备注添加失败')
    }
  } catch (error) {
    console.error('添加售后备注失败:', error)

    // 根据错误类型显示不同的错误消息
    if (error.response) {
      const { status, data } = error.response
      if (status === 404) {
        Message.error('售后申请不存在')
      } else if (status === 400) {
        Message.error(data.message || '请求参数错误')
      } else if (status === 500) {
        Message.error('服务器内部错误，请稍后重试')
      } else {
        Message.error(data.message || '售后备注添加失败')
      }
    } else if (error.message) {
      Message.error(error.message)
    } else {
      Message.error('售后备注添加失败，请稍后重试')
    }
  }
}

const handleStaffSubmit = async (data) => {
  console.log('更换售后员:', data)

  try {
    // 调用更换售后员API
    const response = await afterSalesApi.changeAfterSalesStaff(data.afterSalesId, {
      newStaffId: data.newStaffId,
      newStaffName: data.newStaffName,
      changeReason: data.changeReason
    })

    if (response.code === 200) {
      Message.success('售后员更换成功')

      // 强制刷新表格数据
      console.log('开始刷新表格数据...')
      console.log('刷新前数据长度:', tableData.value.length)

      // 方法1：先清空数据，强制触发响应式更新
      tableData.value = []

      // 方法2：强制刷新组件
      tableKey.value++
      console.log('强制刷新组件，新key:', tableKey.value)

      // 方法3：重新请求数据
      await handleRequest({
        page: pagination.value.current,
        pageSize: pagination.value.pageSize
      })

      console.log('刷新后数据长度:', tableData.value.length)
      console.log('售后员更换成功，表格数据已刷新')
    } else {
      Message.error(response.message || '售后员更换失败')
    }
  } catch (error) {
    console.error('更换售后员失败:', error)
    Message.error('售后员更换失败，请稍后重试')
  }
}

const handleCopySubmit = (data) => {
  console.log('复制问题到剪贴板:', data)
  Message.success('售后问题已复制到剪贴板')
}

// 处理订单编辑
const handleEditOrder = (orderData) => {
  console.log('编辑订单:', orderData)
  Message.info('编辑功能开发中...')
}

// 处理订单操作
const handleOrderAction = (action, orderData) => {
  const actionMap = {
    remark: '售后备注',
    history: '回复历史',
    staff: '更换售后员',
    copy: '复制问题'
  }

  console.log(`执行操作: ${actionMap[action]}`, orderData)

  // 根据操作类型打开对应的弹窗
  switch (action) {
    case 'remark':
      handleAfterSalesRemark(orderData)
      break
    case 'history':
      handleReplyHistory(orderData)
      break
    case 'staff':
      handleChangeStaff(orderData)
      break
    case 'copy':
      handleCopyIssue(orderData)
      break
    default:
      Message.info(`${actionMap[action]}功能开发中...`)
  }
}

// 处理售后状态变更
const handleStatusChange = (data) => {
  console.log('售后状态已变更:', data)

  // 可以在这里添加额外的逻辑，比如：
  // 1. 刷新列表数据
  // 2. 记录操作日志
  // 3. 发送通知等

  // 如果需要刷新数据，可以调用：
  // handleRequest({})
}

// 处理查看商品详情
const handleViewProductDetail = (productId) => {
  console.log('查看商品详情:', productId)
  currentProductId.value = productId
  productDetailVisible.value = true
}

// 页面初始化
onMounted(() => {
  console.log('售后页面初始化，开始加载数据...')
  // 初始化加载数据
  handleRequest({
    page: 1,
    pageSize: 10
  })
})

</script>

<style scoped>
.ma-content-block {
  background-color: #ffffff;
  border-radius: var(--border-radius-medium);
  height: calc(100vh - 120px); /* 为固定表格布局提供合适的高度 */
  display: flex;
  flex-direction: column;
}

/* 确保SimpleTable组件能够占用剩余空间 */
:deep(.simple-table) {
  flex: 1;
  min-height: 0; /* 允许flex子项缩小 */
}

.test-content {
  padding: 20px;
}

.data-item {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.data-item p {
  margin: 8px 0;
}

.data-item button {
  margin-right: 8px;
}
</style>