<template>
  <div class="simple-table">
    <!-- ma-crud配置 - 添加多个插槽 -->
    <ma-crud :key="crudKey" ref="crudRef" :options="crudOptions" :columns="columns">
      <!-- 订单详情插槽 -->
      <template #orderInfo="{ record }">
        <div class="order-info-card">
          <div class="info-row">
            <span class="label">订单编号：</span>
            <span>{{ record.orderNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">售后编号：</span>
            <span>{{ record.afterSalesNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单类型：</span>
            <span>{{ record.orderType }}</span>
          </div>
          <div class="info-row">
            <span class="label">售后时间：</span>
            <span>{{ formatDateTime(record.createdAt) }}</span>
          </div>
          <div class="info-row">
            <span class="label">回复时间：</span>
            <span>{{ formatDateTime(record.replyTime) }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单来源：</span>
            <span>{{ record.orderSource }}</span>
          </div>
          <div class="info-row">
            <span class="label">申请人：</span>
            <span>{{ record.applicantName }}</span>
          </div>
          <div class="info-row">
            <span class="label">跟单员：</span>
            <!-- {{ record }} -->
            <span>{{ record.followerName }}</span>
          </div>
          <div class="info-row">
            <span class="label">销售员：</span>
            <span>{{ record.salesStaffName }}</span>
          </div>
          <div class="info-row">
            <span class="label">采购员：</span>
            <span>{{ record.purchaserName }}</span>
          </div>
          <div class="info-row">
            <span class="label">售后员：</span>
            <span>{{ record.afterSalesStaffName }}</span>
          </div>
          <div class="info-row">
            <span class="label">回复者：</span>
            <span>{{ record.replierName }}</span>
          </div>
          <div class="info-row">
            <span class="label">销售部门：</span>
            <!-- {{ record }} -->
            <span>{{ record.salesDepartment }}</span>
          </div>
        </div>
      </template>

      <!-- 商品详情插槽 - 支持多商品和图片 -->
      <template #productInfo="{ record }">
        <div class="product-info-card">
          <!-- 如果是多个商品 -->
          <div v-if="record.products && record.products.length > 0" class="multiple-products">
            <div v-for="(product, index) in record.products" :key="index" class="product-item">
              <div class="product-header">
                <img :src="product.productImage || '/not-image.png'" :alt="product.productName"
                  class="product-image clickable-image" @error="handleImageError"
                  @click="handleProductImageClick(product.goodsSpuId || product.productCode)" />
                <div class="product-basic-info">
                  <div class="product-name">{{ product.productName }}</div>
                  <div class="product-code">商品编码：{{ product.goodsSpuId }}</div>
                  <div class="product-sku">SKU：{{ product.skuCode }}</div>
                  <div class="product-spec">规格：{{ product.specification }}</div>
                </div>
              </div>
              <div class="product-details">
                <div class="detail-row">
                  <span class="detail-label">数量：</span>
                  <span>{{ product.quantity }}</span>
                </div>
                <!-- <div class="detail-row">
                  <span class="detail-label">单价：</span>
                  <span class="price">¥{{ product.unitPrice }}</span>
                </div> -->
                <div class="detail-row">
                  <span class="detail-label">销售价：</span>
                  <span class="price">¥{{ product.unitPrice }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">成本价：</span>
                  <span class="price">¥{{ product.costPrice }}</span>
                </div>
                <!-- <div class="detail-row">
                  <span class="detail-label">退货状态：</span>
                  <a-tag :color="product.isReturn ? 'red' : 'green'">
                    {{ product.isReturn ? '是' : '否' }}
                  </a-tag>
                </div> -->
                <!-- <div v-if="product.isReturn" class="detail-row">
                  <span class="detail-label">退货数量：</span>
                  <span>{{ product.returnQuantity }}</span>
                </div> -->
              </div>
              <!-- 供应商信息 -->
              <div class="supplier-info">
                <div v-if="product.suggestedSupplierName" class="detail-row">
                  <span class="detail-label">建议供应商：</span>
                  <span>{{ product.suggestedSupplierName }}</span>
                </div>
                <div v-if="product.actualSupplierName" class="detail-row">
                  <span class="detail-label">实际供应商：</span>
                  <span>{{ product.actualSupplierName }}</span>
                </div>
              </div>
              <!-- 物流信息 -->
              <div v-if="product.shippingNumber || product.shippingInfo" class="shipping-info">
                <div v-if="product.shippingNumber" class="detail-row">
                  <span class="detail-label">物流单号：</span>
                  <span>{{ product.shippingNumber }}</span>
                </div>
                <div v-if="product.shippingInfo" class="detail-row">
                  <span class="detail-label">物流信息：</span>
                  <span>{{ product.shippingInfo }}</span>
                </div>
              </div>
              <div v-if="index < record.products.length - 1" class="product-divider"></div>
            </div>
            <div class="delivery-address">
              <span class="address-label">实际收货地址：</span>
              <span class="address-content" :title="record.deliveryAddress">{{ record.deliveryAddress }}</span>
            </div>
          </div>

          <!-- 如果没有商品数据，显示提示 -->
          <div v-else class="no-products">
            <div class="no-data-tip">暂无商品信息</div>
            <div class="delivery-address">
              <span class="address-label">实际收货地址：</span>
              <span>{{ record.deliveryAddress }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">实际供应商：</span>
              <span>{{ record.actualSupplier }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">是否自主品牌：</span>
              <a-tag :color="record.isOwnBrand === '是' ? 'green' : 'blue'">
                {{ record.isOwnBrand }}
              </a-tag>
            </div>
          </div>
        </div>
      </template>

      <!-- 售后信息插槽 - 包含实际供应商和是否为自主品牌 -->
      <template #afterSalesInfo="{ record }">
        <div class="after-sales-info-card">
          <div class="info-item">
            <span class="info-label">售后类型：</span>
            <span>{{ record.afterSalesType }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">售后内容：</span>
            <span>{{ record.afterSalesContent }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">售后状态：</span>
            <a-select :model-value="record.afterSalesStatus" @change="(value) => handleStatusChange(record, value)"
              :style="{ width: '120px' }" size="small">
              <a-option v-for="option in getStatusOptions('afterSalesStatus')" :key="option.value"
                :value="option.value">
                {{ option.label }}
              </a-option>
            </a-select>
          </div>
          <div class="info-item">
            <span class="info-label">售后进度：</span>
            <a-select :model-value="record.afterSalesProgress" @change="(value) => handleProgressChange(record, value)"
              :style="{ width: '140px' }" size="small">
              <a-option v-for="option in getStatusOptions('afterSalesProgress')" :key="option.value"
                :value="option.value">
                {{ option.label }}
              </a-option>
            </a-select>
          </div>
        </div>
      </template>



      <!-- 是否退货插槽 -->
      <template #returnInfo="{ record }">
        <div class="return-info-card">
          <div class="return-item">
            <span class="return-label">业务-是否退货：</span>

            <a-tag :color="record.products[0].isReturn ? 'green' : 'gray'">
              {{ record.products[0].isReturn ? '是' : '否' }}
            </a-tag>

          </div>
          <div class="return-item">
            <span class="return-label">客服-是否退货：</span>
            <!-- {{ record }} -->
            <a-tag :color="record.isReturn ? 'green' : 'gray'">
              {{ record.isReturn ? '是' : '否' }}
            </a-tag>
          </div>
          <div class="return-item">
            <span class="return-label">退货地址：</span>
            <a-textarea :auto-size="{ minRows: 2, maxRows: 4 }" :model-value="record.returnAddress || ''"
              @input="(value) => handleReturnAddressInput(record, value)"
              @blur="(e) => handleReturnAddressChange(record, e.target.value)" :style="{ width: '200px' }" size="small"
              placeholder="请输入退货地址" />
          </div>
          <div class="return-item">
            <span class="return-label">退货单号：</span>
            <a-input :model-value="record.returnNumber || ''" @input="(value) => handleReturnNumberInput(record, value)"
              @blur="(e) => handleReturnNumberChange(record, e.target.value)" :style="{ width: '200px' }" size="small"
              placeholder="请输入退货单号" />
          </div>
        </div>
      </template>

      <!-- 客户维护插槽 -->
      <template #customerMaintenance="{ record }">
        <div class="customer-maintenance-card">
          <div class="maintenance-item">
            <span class="maintenance-label">客户诉求：</span>
            <span>{{ record.customerDemand }}</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">附件：</span>
            <span v-if="record.attachments && record.attachments.length > 0">
              <a-button type="text" size="mini" @click="handleViewAttachments(record, 'attachments')"
                class="attachment-btn">
                <template #icon><icon-file /></template>
                {{ record.attachments.length }}个文件
              </a-button>
            </span>
            <span v-else>-</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">回复内容：</span>
            <span>{{ record.replyContent || '-' }}</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">回复附件：</span>
            <span v-if="record.reply_file && record.reply_file.length > 0">
              <a-button type="text" size="mini" @click="handleViewAttachments(record, 'reply_file')"
                class="attachment-btn">
                <template #icon><icon-file /></template>
                {{ record.reply_file.length }}个文件
              </a-button>
            </span>
            <span v-else>-</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">责任归属：</span>
            <span>{{ record.responsibilityText }}</span>
          </div>
        </div>
      </template>

      <!-- ERP操作系统插槽 -->
      <template #erpSystem="{ record }">
        <a-select :model-value="record.erpStatus" @change="(value) => handleErpSystemChange(record, value)"
          :style="{ width: '140px' }" size="small">
          <a-option v-for="option in getStatusOptions('erpStatus')" :key="option.value" :value="option.value">
            {{ option.label }}
          </a-option>
        </a-select>
      </template>
      <template #remark="{ record }">
        <div class="remark-item" v-if="record.remark">
          <span class="remark-content" style="white-space: pre-wrap">{{
            record.remark
            }}</span>
        </div>

        <!-- 查看备注附件按钮 -->
        <a-button size="small" v-if="hasRemarkAttachments(record)" type="primary"
          @click="handleViewRemarkAttachments(record)">
          <template #icon><icon-file /></template>
          查看附件
        </a-button>
      </template>


      <!-- 操作列插槽 -->
      <template #operation="{ record }">
        <a-space direction="vertical" size="small">
          <a-button type="text" size="small" status="normal" @click="handleDetail(record)">
            <template #icon><icon-eye /></template>
            详情
          </a-button>
          <a-button type="text" size="small" status="success" @click="handleReply(record)">
            <template #icon><icon-message /></template>
            回复
          </a-button>
          <a-button type="text" size="small" status="warning" @click="handleRemark(record)">
            <template #icon><icon-edit /></template>
            售后备注
          </a-button>
          <a-button type="text" size="small" status="normal" @click="handleHistory(record)">
            <template #icon><icon-history /></template>
            回复历史
          </a-button>
          <a-button type="text" size="small" status="danger" @click="handleChangeStaff(record)">
            <template #icon><icon-user /></template>
            更换售后员
          </a-button>
          <a-button type="text" size="small" status="normal" @click="handleCopyIssue(record)">
            <template #icon><icon-copy /></template>
            复制问题
          </a-button>
        </a-space>
      </template>
    </ma-crud>

    <!-- 附件查看弹窗 -->
    <a-modal v-model:visible="attachmentModalVisible" title="查看附件" :width="600" :footer="false">
      <div class="attachment-list">
        <div v-for="(attachment, index) in currentAttachments" :key="index" class="attachment-item">
          <div class="attachment-info">
            <icon-file class="file-icon" />
            <span class="file-name">{{ getFileName(attachment) }}</span>
            <span class="file-size">{{ getFileSize(attachment) }}</span>
          </div>
          <div class="attachment-actions">
            <a-button type="text" size="small" @click="handleViewAttachment(attachment)">
              <template #icon><icon-eye /></template>
              查看
            </a-button>
            <a-button type="text" size="small" @click="handleDownloadAttachment(attachment)">
              <template #icon><icon-download /></template>
              下载
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 备注附件查看弹窗 -->
    <a-modal v-model:visible="remarkAttachmentModalVisible" title="查看备注附件" :width="600" :footer="false">
      <div class="attachment-list">
        <div v-for="(attachment, index) in currentRemarkAttachments" :key="index" class="attachment-item">
          <div class="attachment-info">
            <icon-file class="file-icon" />
            <span class="file-name">{{ getRemarkFileName(attachment) }}</span>
            <span class="file-size">{{ getRemarkFileSize(attachment) }}</span>
          </div>
          <div class="attachment-actions">
            <a-button type="text" size="small" @click="handleViewRemarkAttachment(attachment)">
              <template #icon><icon-eye /></template>
              查看
            </a-button>
            <a-button type="text" size="small" @click="handleDownloadRemarkAttachment(attachment)">
              <template #icon><icon-download /></template>
              下载
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { afterSalesApi } from '@/api/master/csm/afterSales.js'
import { afterSalesTypeTemplateApi } from '@/api/master/csm/afterSalesTypeTemplate.js'

// 状态映射配置
const statusMappings = {
  // 售后状态映射
  afterSalesStatus: {
    0: '待处理',
    1: '处理中',
    2: '已完成'
  },
  // 售后进度映射
  afterSalesProgress: {
    0: '跟进补发单号',
    1: '跟进退货',
    2: '跟进退款',
    3: '跟进换货',
    4: '跟进维修结果',
    5: '待客户回复',
    6: '待供应商回复',
    7: '补发维修中',
    8: '理赔中',
    9: '跟进安装结果',
    10: '跟进物流派送'
  },
  // ERP操作系统映射
  erpStatus: {
    0: '完成erp系统操作',
    1: '已操作采购退料',
    2: '已下推费用单',
    3: '其他'
  }
}

// 获取状态文字显示
const getStatusText = (type, value) => {
  return statusMappings[type]?.[value] || value
}

// 获取状态选项列表
const getStatusOptions = (type) => {
  const mapping = statusMappings[type]
  if (!mapping) return []

  return Object.entries(mapping).map(([value, label]) => ({
    value: parseInt(value),
    label
  }))
}

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  pagination: {
    type: Object,
    default: () => ({
      total: 0,
      current: 1,
      pageSize: 10
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'detail',
  'reply',
  'remark',
  'history',
  'change-staff',
  'copy-issue',
  'request',
  'status-change',
  'view-product-detail'
])

// 销售部门数据
const salesDepartments = ref([
  { label: "全部", value: "" }
])

// 售后类型数据
const afterSalesTypeOptions = ref([])
const afterSalesTypeLoading = ref(false)

// 树形选择器过滤函数
const filterTreeNode = (searchValue, nodeData) => {
  if (!searchValue) return true
  return nodeData.title.toLowerCase().includes(searchValue.toLowerCase())
}

// 获取销售部门数据
const fetchSalesDepartments = async () => {
  try {
    console.log('开始获取销售部门数据...')
    const response = await afterSalesApi.getSalesDepartments({
      page: 1,
      pageSize: 100
    })

    if (response.code === 200 && response.data && response.data.items) {
      console.log('销售部门数据获取成功:', response.data)

      // 转换数据格式
      const departmentOptions = response.data.items.map(dept => ({
        label: dept.name || dept.deptName || dept.title,
        value: dept.name || dept.deptName || dept.title
      }))

      // 更新销售部门选项（保留"全部"选项）
      salesDepartments.value = [
        { label: "全部", value: "" },
        ...departmentOptions
      ]

      console.log('销售部门选项已更新:', salesDepartments.value)

      // 更新列配置中的销售部门字典数据
      nextTick(() => {
        updateSalesDepartmentDict()
      })
    } else {
      console.warn('销售部门数据格式异常:', response)
      Message.warning('获取销售部门数据失败')
    }
  } catch (error) {
    console.error('获取销售部门数据失败:', error)
    Message.error('获取销售部门数据失败，请稍后重试')
  }
}

// 更新销售部门列的字典数据
const updateSalesDepartmentDict = () => {
  console.log('=== 更新销售部门字典数据 ===')
  console.log('销售部门列表:', salesDepartments.value)

  if (salesDepartments.value && Array.isArray(salesDepartments.value) && salesDepartments.value.length > 1) {
    // 查找销售部门列
    const salesDeptColumn = columns.find(col => col.dataIndex === 'salesDepartment')
    console.log('找到的销售部门列:', salesDeptColumn)

    if (salesDeptColumn) {
      // 直接替换整个dict对象
      salesDeptColumn.dict = {
        data: [...salesDepartments.value]
      }

      console.log('销售部门下拉框选项已更新:', salesDepartments.value)

      // 强制刷新ma-crud组件
      crudKey.value++
      console.log('强制刷新ma-crud组件，新key:', crudKey.value)
    } else {
      console.warn('未找到销售部门列配置')
    }
  } else {
    console.warn('销售部门列表为空或不是数组，跳过更新销售部门字典')
  }
}

// 获取售后类型数据
const fetchAfterSalesTypes = async () => {
  try {
    console.log('开始获取售后类型数据...')
    afterSalesTypeLoading.value = true

    // 调用售后类型内容模板API获取树形结构数据
    const response = await afterSalesTypeTemplateApi.getActiveTree()

    if (response.code === 200 && response.data) {
      console.log('售后类型数据获取成功:', response.data)

      // 转换数据格式为树形选择器需要的格式
      const convertToTreeSelectFormat = (items) => {
        return items.map(item => {
          const treeNode = {
            key: item.name, // 使用name作为key
            title: item.name,
            value: item.name,
            label: item.name, // 添加label字段
            id: item.id
          }

          // 处理子节点
          if (item.children && item.children.length > 0) {
            treeNode.children = convertToTreeSelectFormat(item.children)
          }

          return treeNode
        })
      }

      // 添加"全部"选项
      afterSalesTypeOptions.value = [
        { key: '', title: '全部', value: '', label: '全部' },
        ...convertToTreeSelectFormat(response.data)
      ]

      console.log('售后类型选项已更新:', afterSalesTypeOptions.value)
    } else {
      console.warn('售后类型数据格式异常:', response)
      Message.warning('获取售后类型数据失败')
    }
  } catch (error) {
    console.error('获取售后类型数据失败:', error)
    Message.error('获取售后类型数据失败，请稍后重试')
  } finally {
    afterSalesTypeLoading.value = false
  }
}

// 日期时间格式化函数
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const crudRef = ref()
const crudKey = ref(0) // 用于强制刷新 ma-crud 组件

// 附件查看相关状态
const attachmentModalVisible = ref(false)
const currentAttachments = ref([])
const currentAttachmentType = ref('')

// 备注附件查看相关状态
const remarkAttachmentModalVisible = ref(false)
const currentRemarkAttachments = ref([])



// API函数 - 直接使用父组件传递的数据和分页信息
const simpleApi = async (params) => {
  console.log('=== 售后服务API请求 ===')
  console.log('ma-crud请求参数:', params)
  console.log('父组件数据长度:', props.data.length)
  console.log('父组件分页信息:', props.pagination)

  try {
    // 构建请求参数
    const requestParams = {
      // 分页参数
      page: params.page || 1,
      pageSize: params.pageSize || 10
    }

    // 处理搜索参数 - 使用 beforeSearch 处理后的参数
    // 注意：beforeSearch 已经将字段映射为后端需要的格式

    // 订单相关（不需要映射的字段）
    if (params.orderNumber) {
      requestParams.orderNumber = params.orderNumber
    }
    if (params.afterSalesNumber) {
      requestParams.afterSalesNumber = params.afterSalesNumber
    }
    if (params.afterSalesStatus !== undefined && params.afterSalesStatus !== '') {
      requestParams.afterSalesStatus = params.afterSalesStatus
    }
    if (params.orderSource) {
      requestParams.orderSource = params.orderSource
    }
    if (params.follower) {
      requestParams.follower = params.follower
    }
    if (params.salesperson) {
      requestParams.salesperson = params.salesperson
    }
    if (params.replier) {
      requestParams.replier = params.replier
    }
    if (params.isOwnBrand) {
      requestParams.isOwnBrand = params.isOwnBrand
    }

    // 使用 beforeSearch 映射后的字段
    if (params.purchaser_username) {
      requestParams.purchaser_username = params.purchaser_username
    }
    if (params.is_return) {
      requestParams.is_return = params.is_return
    }
    if (params.product_name) {
      requestParams.product_name = params.product_name
    }
    if (params.product_code) {
      requestParams.product_code = params.product_code
    }
    if (params.supplier_name) {
      requestParams.supplier_name = params.supplier_name
    }
    if (params.after_sales_type) {
      requestParams.after_sales_type = params.after_sales_type
    }
    if (params.after_sales_staff_username) {
      requestParams.after_sales_staff_username = params.after_sales_staff_username
    }
    if (params.proposer_username) {
      requestParams.proposer_username = params.proposer_username
    }
    if (params.delivery_address) {
      requestParams.delivery_address = params.delivery_address
    }
    if (params.shipping_number) {
      requestParams.shipping_number = params.shipping_number
    }
    if (params.created_at_start) {
      requestParams.created_at_start = params.created_at_start
    }
    if (params.created_at_end) {
      requestParams.created_at_end = params.created_at_end
    }
    if (params.reply_start_time) {
      requestParams.reply_start_time = params.reply_start_time
    }
    if (params.reply_end_time) {
      requestParams.reply_end_time = params.reply_end_time
    }
    if (params.dept_name) {
      requestParams.dept_name = params.dept_name
    }

    // 处理回复时间范围参数（如果还需要的话）
    if (params.replyTimeRange && params.replyTimeRange.length === 2) {
      const [startDate, endDate] = params.replyTimeRange
      const start = new Date(startDate)
      const end = new Date(endDate)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      requestParams.replyStartTime = Math.floor(start.getTime() / 1000)
      requestParams.replyEndTime = Math.floor(end.getTime() / 1000)
    }

   

    console.log('最终请求参数:', requestParams)

    // 记录当前的数据状态，用于检测数据是否已更新
    const currentDataLength = props.data.length
    const currentTotal = props.pagination.total

    console.log('请求前数据状态:', {
      当前数据长度: currentDataLength,
      当前总数: currentTotal,
      当前页: props.pagination.current
    })

    // 触发外部请求事件，让父组件重新请求数据
    emit('request', requestParams)

    // 等待父组件数据更新完成
    let waitCount = 0
    const maxWaitCount = 50 // 最多等待5秒

    while (waitCount < maxWaitCount) {
      await new Promise(resolve => setTimeout(resolve, 100))
      waitCount++

      // 检查数据是否已更新（数据长度或总数发生变化，或者是第一页且数据已加载）
      const newDataLength = props.data.length
      const newTotal = props.pagination.total
      const newCurrent = props.pagination.current

      console.log(`等待数据更新 ${waitCount}/50:`, {
        新数据长度: newDataLength,
        新总数: newTotal,
        新当前页: newCurrent
      })

      // 如果是搜索操作（有搜索条件）且数据已更新，或者是分页操作
      const hasSearchParams = Object.keys(requestParams).some(key =>
        key !== 'page' && key !== 'pageSize' && requestParams[key] !== undefined && requestParams[key] !== ''
      )

      if (hasSearchParams) {
        // 搜索操作：等待总数变化或数据变化
        if (newTotal !== currentTotal || newDataLength !== currentDataLength) {
          console.log('检测到搜索结果数据已更新')
          break
        }
      } else {
        // 分页操作：等待当前页数据加载完成
        if (newDataLength > 0 && newCurrent === requestParams.page) {
          console.log('检测到分页数据已更新')
          break
        }
      }
    }

    if (waitCount >= maxWaitCount) {
      console.warn('等待数据更新超时，使用当前数据')
    }

    // 使用更新后的父组件数据和分页信息
    const result = {
      success: true,
      code: 200,
      message: '获取售后服务列表成功',
      data: {
        items: props.data || [],
        pageInfo: {
          total: props.pagination.total || 0,
          currentPage: props.pagination.current || 1,
          pageSize: props.pagination.pageSize || 10,
          totalPage: Math.ceil((props.pagination.total || 0) / (props.pagination.pageSize || 10))
        }
      }
    }

    console.log('=== 分页调试信息 ===')
    console.log('使用父组件数据量:', props.data.length)
    console.log('父组件分页总数:', props.pagination.total)
    console.log('父组件当前页:', props.pagination.current)
    console.log('父组件每页大小:', props.pagination.pageSize)
    console.log('计算总页数:', Math.ceil((props.pagination.total || 0) / (props.pagination.pageSize || 10)))
    console.log('返回结果:', result)
    console.log('==================')

    return result

  } catch (error) {
    console.error('售后服务API请求失败:', error)
    return {
      success: false,
      code: 500,
      message: '获取售后服务列表失败',
      data: {
        items: [],
        pageInfo: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          totalPage: 1
        }
      }
    }
  }
}

// ma-crud配置 - 参考服务商列表的配置
const crudOptions = reactive({
  // API配置
  api: simpleApi,

  // 基本配置
  add: false,
  edit: false,
  delete: false,
  showIndex: false,
  operationColumn: false, // 禁用自动添加的操作列，因为已在columns中手动定义了

  // 搜索配置
  search: true,
  searchShow: true,
  searchLabelWidth: "130px",
  searchColNumber: 3,

  // 分页配置 - 参考服务商列表
  page: true,
  pageSize: 10,
  pageSizes: [10, 20, 30, 50, 100],
  showTotal: true,
  showJumper: true,
  showPageSize: true,

  // 确保分页正确工作的关键配置
  requestPage: true, // 启用分页请求
  beforeRequest: (params) => {
    console.log('ma-crud beforeRequest:', params)
    return params
  },
  beforeSearch: (params) => {
    console.log('=== beforeSearch 参数处理 ===')
    console.log('原始参数:', params)

    // 采购员：purchaser -> purchaser_username
    if (params && params.purchaser) {
      params.purchaser_username = params.purchaser.trim()
      delete params.purchaser
    } else {
      delete params.purchaser_username
    }

    // 客服是否退货：customerServiceReturn -> is_return (是=t, 否=f)
    if (params && params.customerServiceReturn) {
      params.is_return = params.customerServiceReturn === '是' ? 't' : 'f'
      delete params.customerServiceReturn
    } else {
      delete params.is_return
    }

    // 商品名称：productName -> product_name
    if (params && params.productName) {
      params.product_name = params.productName.trim()
      delete params.productName
    } else {
      delete params.product_name
    }

    // 商品编码：productCode -> product_code
    if (params && params.productCode) {
      params.product_code = params.productCode.trim()
      delete params.productCode
    } else {
      delete params.product_code
    }

    // 供应商名称：actualSupplier -> supplier_name
    if (params && params.actualSupplier) {
      params.supplier_name = params.actualSupplier.trim()
      delete params.actualSupplier
    } else {
      delete params.supplier_name
    }

    // 售后类型：afterSalesType -> after_sales_type
    if (params && params.afterSalesType) {
      params.after_sales_type = params.afterSalesType.trim()
      delete params.afterSalesType
    } else {
      delete params.after_sales_type
    }

    // 售后员：afterSalesStaff -> after_sales_staff_username
    if (params && params.afterSalesStaff) {
      params.after_sales_staff_username = params.afterSalesStaff.trim()
      delete params.afterSalesStaff
    } else {
      delete params.after_sales_staff_username
    }

    // 提出人：proposer -> proposer_username
    if (params && params.proposer) {
      params.proposer_username = params.proposer.trim()
      delete params.proposer
    } else {
      delete params.proposer_username
    }

    // 销售部门：salesDepartment -> dept_name
    if (params && params.salesDepartment) {
      params.dept_name = params.salesDepartment.trim()
      delete params.salesDepartment
    } else {
      delete params.dept_name
    }

    // 实际收货地址：actualDeliveryAddress -> delivery_address
    if (params && params.actualDeliveryAddress) {
      params.delivery_address = params.actualDeliveryAddress.trim()
      delete params.actualDeliveryAddress
    } else {
      delete params.delivery_address
    }

    // 寄出单号：shippingNumber -> shipping_number
    if (params && params.shippingNumber) {
      params.shipping_number = params.shippingNumber.trim()
      delete params.shippingNumber
    } else {
      delete params.shipping_number
    }

    // 售后时间范围：afterSalesTimeRange -> created_at_start, created_at_end (转时间戳)
    if (params && params.afterSalesTimeRange && params.afterSalesTimeRange.length === 2) {
      const [startDate, endDate] = params.afterSalesTimeRange
      const start = new Date(startDate)
      const end = new Date(endDate)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      params.created_at_start = Math.floor(start.getTime() / 1000)
      params.created_at_end = Math.floor(end.getTime() / 1000)
      delete params.afterSalesTimeRange

    } else {
      delete params.created_at_start
      delete params.created_at_end
    }
    // 回复时间范围：afterSalesTimeRange -> created_at_start, created_at_end (转时间戳)
    if (params && params.replyTime && params.replyTime.length === 2) {
      const [startDate, endDate] = params.replyTime
      const start = new Date(startDate)
      const end = new Date(endDate)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      params.reply_start_time = Math.floor(start.getTime() / 1000)
      params.reply_end_time = Math.floor(end.getTime() / 1000)
      delete params.replyTime


    } else {
      delete params.reply_start_time
      delete params.reply_end_time
    }


    console.log('处理后参数:', params)
    console.log('========================')
    return params
  },

  // 导出配置
  export: {
    show: true,
  },

  // 启用tabs功能
  tabs: {
    type: 'line',
    trigger: 'click',
    dataIndex: 'afterSalesStatus',
    data: [
      { label: '全部', value: '' },
      { label: '待处理', value: 0 },
      { label: '处理中', value: 1 },
      { label: '已完成', value: 2 }
    ],
    defaultKey: '',
    searchKey: 'afterSalesStatus'
  }
})

// 列配置 - 包含搜索功能
const columns = [
  // 搜索字段
  {
    title: "订单编号",
    dataIndex: "orderNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入订单编号"
  },
  {
    title: "跟单员",
    dataIndex: "follower",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入跟单员"
  },
  {
    title: "销售部门",
    dataIndex: "salesDepartment",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [] // 将通过 updateSalesDepartmentDict 函数动态更新
    },
    searchPlaceholder: "请选择销售部门"
  },
  {
    title: "售后类型",
    dataIndex: "afterSalesType",
    formType: "tree-select",
    search: true,
    hide: true,
    dict: {
      data: afterSalesTypeOptions
    },
    searchPlaceholder: "请选择售后类型",
    // 树形选择器配置
    treeSelectProps: {
      allowSearch: true,
      filterTreeNode: filterTreeNode,
      loading: afterSalesTypeLoading
    }
  },
  {
    title: "客服是否退货",
    dataIndex: "customerServiceReturn",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "是", value: "是" },
        { label: "否", value: "否" }
      ]
    },
    searchPlaceholder: "请选择客服是否退货"
  },
  {
    title: "售后员",
    dataIndex: "afterSalesStaff",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入售后员"
  },
  {
    title: "提出人",
    dataIndex: "proposer",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入提出人"
  },
  {
    title: "售后状态",
    dataIndex: "afterSalesStatus",
    formType: "select",
    search: false, // 禁用搜索，因为已经在 tabs 中使用
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待处理", value: 0 },
        { label: "处理中", value: 1 },
        { label: "已完成", value: 2 }
      ]
    },
    searchPlaceholder: "请选择售后状态"
  },
  {
    title: "采购员",
    dataIndex: "purchaser",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入采购员"
  },
  {
    title: "供应商名称",
    dataIndex: "actualSupplier",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入供应商名称"
  },
  {
    title: "商品名称",
    dataIndex: "productName",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品名称"
  },
  {
    title: "商品编码",
    dataIndex: "productCode",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品编码"
  },
  {
    title: "是否自主品牌",
    dataIndex: "isOwnBrand",
    formType: "select",
    search: false,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "是", value: "是" },
        { label: "否", value: "否" }
      ]
    },
    searchPlaceholder: "请选择是否自主品牌"
  },

  {
    title: "实际收货地址",
    dataIndex: "actualDeliveryAddress",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入实际收货地址"
  },
  {
    title: "寄出单号",
    dataIndex: "shippingNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入寄出单号"
  },
  {
    title: "售后编号",
    dataIndex: "afterSalesNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入售后编号"
  },
  {
    title: "售后时间",
    dataIndex: "afterSalesTimeRange",
    formType: "range",
    search: true,
    hide: true,
    searchPlaceholder: "请选择售后时间范围"
  },
  {
    title: "回复时间",
    dataIndex: "replyTime",
    formType: "range",
    search: true,
    hide: true,
    searchPlaceholder: "请选择回复时间范围"
  },
  // 显示列
  {
    title: "订单详情",
    dataIndex: "orderInfo",
    width: 280,
    slot: true,
    search: false
  },
  {
    title: "商品详情",
    dataIndex: "productInfo",
    width: 350,
    slot: true,
    search: false
  },
  {
    title: "售后信息",
    dataIndex: "afterSalesInfo",
    width: 350,
    slot: true,
    search: false
  },
  {
    title: "是否退货",
    dataIndex: "returnInfo",
    width: 250,
    slot: true,
    search: false
  },
  {
    title: "客户维护",
    dataIndex: "customerMaintenance",
    width: 350,
    slot: true,
    search: false
  },
  {
    title: "ERP操作系统",
    dataIndex: "erpSystem",
    width: 120,
    align: "center",
    slot: true,
    search: false
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: 200,
    align: "center",
    slot: true,
    search: false
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 180,
    fixed: "right",
    slot: true,
    search: false
  }
]

// 事件处理
const handleDetail = (record) => {
  console.log('点击详情按钮:', record)
  emit('detail', record)
}

const handleReply = (record) => {
  console.log('点击回复按钮:', record)
  emit('reply', record)
}

const handleRemark = (record) => {
  console.log('点击备注按钮:', record)
  emit('remark', record)
}

const handleHistory = (record) => {
  console.log('点击历史按钮:', record)
  emit('history', record)
}

const handleChangeStaff = (record) => {
  console.log('点击更换售后员按钮:', record)
  emit('change-staff', record)
}

const handleCopyIssue = (record) => {
  console.log('点击复制问题按钮:', record)
  emit('copy-issue', record)
}

// 处理售后状态变更
const handleStatusChange = async (record, newStatus) => {
  if (record.afterSalesStatus === newStatus) {
    return // 状态没有变化，不需要更新
  }

  const oldStatus = record.afterSalesStatus // 保存原状态

  try {
    console.log('更新售后状态:', record.id, newStatus)

    // 调用真实的API接口
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')
    const response = await afterSalesApi.updateAfterSalesStatusOnly(record.id, newStatus)

    if (response.code === 200) {
      // 更新本地数据
      record.afterSalesStatus = newStatus

      // 触发外部事件，通知父组件状态已更改
      emit('status-change', {
        record,
        oldStatus,
        newStatus
      })

      const statusText = getStatusText('afterSalesStatus', newStatus)
      Message.success(`售后状态已更新为"${statusText}"`)
    } else {
      Message.error(response.message || '状态更新失败')
    }
  } catch (error) {
    console.error('更新售后状态失败:', error)
    Message.error('状态更新失败，请稍后重试')
  }
}

// 处理售后进度变更
const handleProgressChange = async (record, newProgress) => {
  if (record.afterSalesProgress === newProgress) {
    return // 进度没有变化，不需要更新
  }

  try {
    console.log('更新售后进度:', record.id, newProgress)

    // 调用真实的API接口
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')
    const response = await afterSalesApi.updateAfterSalesProgressOnly(record.id, newProgress)

    if (response.code === 200) {
      // 更新本地数据
      record.afterSalesProgress = newProgress

      const progressText = getStatusText('afterSalesProgress', newProgress)
      Message.success(`售后进度已更新为"${progressText}"`)
    } else {
      Message.error(response.message || '进度更新失败')
    }
  } catch (error) {
    console.error('更新售后进度失败:', error)
    Message.error('进度更新失败，请稍后重试')
  }
}

// 处理ERP系统状态变更
const handleErpSystemChange = async (record, newErpStatus) => {
  if (record.erpStatus === newErpStatus) {
    return // ERP状态没有变化，不需要更新
  }

  try {
    console.log('更新ERP系统状态:', record.id, newErpStatus)

    // 调用真实的API接口
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')
    const response = await afterSalesApi.updateErpStatus(record.id, { erpStatus: newErpStatus })

    if (response.code === 200) {
      // 更新本地数据
      record.erpStatus = newErpStatus

      const erpStatusText = getStatusText('erpStatus', newErpStatus)
      Message.success(`ERP系统状态已更新为"${erpStatusText}"`)
    } else {
      Message.error(response.message || 'ERP状态更新失败')
    }
  } catch (error) {
    console.error('更新ERP系统状态失败:', error)
    Message.error('ERP状态更新失败，请稍后重试')
  }
}

// 处理退货地址输入（实时更新本地数据）
const handleReturnAddressInput = (record, newAddress) => {
  // 实时更新本地数据，不调用API
  record.returnAddress = newAddress
}

// 处理退货地址变更（失焦时保存到服务器）
const handleReturnAddressChange = async (record, newAddress) => {
  // 这里可以添加防抖逻辑，避免频繁调用API
  const trimmedAddress = newAddress?.trim() || ''

  try {
    console.log('保存退货地址:', record.id, trimmedAddress)

    // 调用真实的API接口
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')
    const response = await afterSalesApi.updateReturnAddress(record.id, { returnAddress: trimmedAddress })

    if (response.code === 200) {
      // 确保本地数据是最新的
      record.returnAddress = trimmedAddress

      Message.success('退货地址已保存')
    } else {
      Message.error(response.message || '退货地址保存失败')
    }
  } catch (error) {
    console.error('保存退货地址失败:', error)
    Message.error('退货地址保存失败，请稍后重试')
  }
}

// 处理退货单号输入（实时更新本地数据）
const handleReturnNumberInput = (record, newNumber) => {
  // 实时更新本地数据，不调用API
  record.returnNumber = newNumber
}

// 处理退货单号变更（失焦时保存到服务器）
const handleReturnNumberChange = async (record, newNumber) => {
  // 这里可以添加防抖逻辑，避免频繁调用API
  const trimmedNumber = newNumber?.trim() || ''

  try {
    console.log('保存退货单号:', record.id, trimmedNumber)

    // 调用真实的API接口
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')
    const response = await afterSalesApi.updateReturnNumber(record.id, { returnNumber: trimmedNumber })

    if (response.code === 200) {
      // 确保本地数据是最新的
      record.returnNumber = trimmedNumber

      Message.success('退货单号已保存')
    } else {
      Message.error(response.message || '退货单号保存失败')
    }
  } catch (error) {
    console.error('保存退货单号失败:', error)
    Message.error('退货单号保存失败，请稍后重试')
  }
}



// 处理附件查看
const handleViewAttachments = (record, attachmentType) => {
  console.log('查看附件:', record.id, attachmentType)

  currentAttachmentType.value = attachmentType
  currentAttachments.value = record[attachmentType] || []
  attachmentModalVisible.value = true
}

// 获取文件名
const getFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  return attachment.fileName || attachment.name || '未知文件'
}

// 获取文件大小
const getFileSize = (attachment) => {
  if (typeof attachment === 'object' && attachment.fileSize) {
    const size = parseInt(attachment.fileSize)
    if (isNaN(size) || size === 0) return ''
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    return `${(size / (1024 * 1024)).toFixed(1)}MB`
  }
  return ''
}

// 处理单个附件查看（在新窗口打开）
const handleViewAttachment = (attachment) => {
  console.log('查看附件:', attachment)

  let url = ''

  if (typeof attachment === 'string') {
    url = attachment
  } else {
    // 适配新的附件格式
    url = attachment.fileUrl || attachment.url || attachment.path || ''
  }

  if (url) {
    try {
      // 在新窗口打开附件
      window.open(url, '_blank')
      Message.success('正在打开附件')
    } catch (error) {
      console.error('打开附件失败:', error)
      Message.error('打开附件失败')
    }
  } else {
    Message.error('附件链接无效')
  }
}

// 处理附件下载
const handleDownloadAttachment = async (attachment) => {
  console.log('下载附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    // 适配新的附件格式
    url = attachment.fileUrl || attachment.url || attachment.path || ''
    fileName = attachment.fileName || attachment.name || 'download'
  }

  if (!url) {
    Message.error('无法下载此文件：文件地址不存在')
    return
  }

  try {
    console.log('开始下载文件:', { url, fileName })

    // 方法1：使用 fetch 获取文件并强制下载
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取文件内容
    const blob = await response.blob()

    // 创建 Blob URL
    const blobUrl = window.URL.createObjectURL(blob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = fileName
    link.style.display = 'none'

    // 添加到页面并触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(blobUrl)

    Message.success('文件下载成功')

  } catch (error) {
    console.error('Fetch下载失败，尝试备用方法:', error)

    try {
      // 方法2：备用下载方法
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.target = '_self' // 改为当前窗口
      link.style.display = 'none'

      // 设置强制下载的属性
      link.setAttribute('download', fileName)
      link.setAttribute('type', 'application/octet-stream')

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      Message.success('开始下载文件')

    } catch (fallbackError) {
      console.error('备用下载方法也失败:', fallbackError)

      // 方法3：最后的备用方案 - 在新窗口打开并提示用户手动下载
      const newWindow = window.open(url, '_blank')
      if (newWindow) {
        Message.info('已在新窗口打开文件，请手动保存')
      } else {
        Message.error('下载失败，请检查浏览器设置')
      }
    }
  }
}

// 图片错误处理
const handleImageError = (event) => {
  event.target.src = '/not-image.png'
}

// 处理商品图片点击
const handleProductImageClick = (productId) => {
  console.log('商品图片点击:', productId)
  emit('view-product-detail', productId)
}

// 检查是否有备注附件
const hasRemarkAttachments = (record) => {
  if (!record.attachments || !Array.isArray(record.attachments)) {
    return false
  }
  return record.attachments.some(attachment => attachment.attachmentType === 'remark')
}

// 处理查看备注附件
const handleViewRemarkAttachments = (record) => {
  console.log('查看备注附件:', record.id)

  if (record.attachments && Array.isArray(record.attachments)) {
    // 筛选出备注附件
    currentRemarkAttachments.value = record.attachments.filter(
      attachment => attachment.attachmentType === 'remark'
    )
    console.log('筛选出的备注附件:', currentRemarkAttachments.value)
    remarkAttachmentModalVisible.value = true
  } else {
    Message.warning('没有找到备注附件')
  }
}

// 获取备注附件文件名
const getRemarkFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  // 优先使用 fileName，然后是 name，最后从 fileUrl 中提取
  return attachment.fileName || attachment.name ||
    (attachment.fileUrl ? attachment.fileUrl.split('/').pop() : '未知文件')
}

// 获取备注附件文件大小
const getRemarkFileSize = (attachment) => {
  if (typeof attachment === 'object') {
    // 优先使用 fileSize，然后是 size
    const sizeValue = attachment.fileSize || attachment.size
    if (sizeValue) {
      const size = parseInt(sizeValue)
      if (isNaN(size) || size === 0) return ''
      if (size < 1024) return `${size}B`
      if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
      return `${(size / (1024 * 1024)).toFixed(1)}MB`
    }
  }
  return ''
}

// 处理查看备注附件
const handleViewRemarkAttachment = (attachment) => {
  console.log('查看备注附件:', attachment)

  let url = ''
  if (typeof attachment === 'string') {
    url = attachment
  } else {
    // 优先使用 fileUrl，然后是 url，最后是 path
    url = attachment.fileUrl || attachment.url || attachment.path || ''
  }

  if (url) {
    try {
      // 在新窗口打开附件
      window.open(url, '_blank')
      Message.success('正在打开附件')
    } catch (error) {
      console.error('打开附件失败:', error)
      Message.error('打开附件失败')
    }
  } else {
    Message.error('附件链接无效')
  }
}

// 处理下载备注附件
const handleDownloadRemarkAttachment = (attachment) => {
  console.log('下载备注附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    // 优先使用 fileUrl，然后是 url，最后是 path
    url = attachment.fileUrl || attachment.url || attachment.path || ''
    // 优先使用 fileName，然后是 name，最后从 URL 中提取
    fileName = attachment.fileName || attachment.name ||
      (url ? url.split('/').pop() : 'download')
  }

  if (url) {
    try {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      Message.success('开始下载文件')
    } catch (error) {
      console.error('下载失败:', error)
      Message.error('下载失败')
    }
  } else {
    Message.error('附件链接无效')
  }
}

// 监听销售部门数据变化
watch(salesDepartments, (newSalesDepartments) => {
  console.log('销售部门数据发生变化:', newSalesDepartments)

  if (newSalesDepartments && newSalesDepartments.length > 1) {
    // 延迟执行，确保 columns 已经初始化
    nextTick(() => {
      updateSalesDepartmentDict()
    })
  }
}, { deep: true, immediate: false })

// 组件挂载时获取数据
onMounted(() => {
  console.log('售后服务表格组件已挂载，开始获取数据...')
  fetchSalesDepartments()
  fetchAfterSalesTypes()
})
</script>

<style scoped>
.simple-table {
  background-color: #ffffff;
  height: 100%;
}

/* 固定表格高度相关样式 */
/* 优化固定表格的滚动条样式 */
:deep(.arco-table-body) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

:deep(.arco-table-body::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.arco-table-body::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.arco-table-body::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.arco-table-body::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 确保表格容器高度正确 */
:deep(.ma-crud .arco-table-container) {
  height: 100%;
}

:deep(.ma-crud .arco-table) {
  height: 100%;
}

/* 订单信息样式 */
.order-info-card,
.product-info-card,
.after-sales-info-card,
.return-info-card,
.customer-maintenance-card {
  padding: 8px;
  font-size: 12px;
  line-height: 1.4;
}

.info-row,
.detail-item,
.info-item,
.return-item,
.maintenance-item {
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;
}

.label,
.detail-label,
.info-label,
.return-label,
.maintenance-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

/* 商品相关样式 */
.multiple-products,
.single-product {
  width: 100%;
}

.product-item {
  margin-bottom: 12px;
}

.product-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.product-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
  margin-right: 10px;
  flex-shrink: 0;
}

.clickable-image {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.product-basic-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.3;
  word-break: break-all;
  font-size: 12px;
}

.product-code {
  font-size: 11px;
  color: #666;
}

.product-details {
  font-size: 11px;
  margin-left: 60px;
  /* 对齐图片右侧 */
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.price {
  font-weight: 500;
  color: #f53f3f;
}

.product-divider {
  height: 1px;
  background-color: #e5e6eb;
  margin: 8px 0;
}

.delivery-address {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f2f3f5;
  font-size: 11px;
  width: 100%;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}

.address-label {
  font-weight: 500;
  color: #666;
  display: inline-block;
  margin-right: 4px;
  flex-shrink: 0;
}

.detail-label {
  min-width: 70px;
}

.info-label {
  min-width: 70px;
}

.return-label {
  min-width: 90px;
}

.maintenance-label {
  min-width: 70px;
}

/* 附件相关样式 */
.attachment-btn {
  color: #165dff;
  padding: 2px 6px;
}

.attachment-btn:hover {
  background-color: #f2f3f5;
}

.attachment-list {
  max-height: 400px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  color: #86909c;
  margin-right: 8px;
  flex-shrink: 0;
}

.file-name {
  font-weight: 500;
  color: #1d2129;
  margin-right: 8px;
  word-break: break-all;
  flex: 1;
}

.file-size {
  color: #86909c;
  font-size: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

.attachment-actions {
  flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {

  .order-info-card,
  .product-info-card,
  .after-sales-info-card {
    font-size: 11px;
  }

  .label,
  .detail-label,
  .info-label {
    min-width: 60px;
  }
}
</style>
