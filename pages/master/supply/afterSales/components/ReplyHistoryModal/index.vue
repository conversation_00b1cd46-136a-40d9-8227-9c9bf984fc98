<template>
  <a-modal
    v-model:visible="visible"
    title="回复历史记录"
    width="1200px"
    :footer="false"
    :mask-closable="false"
  >
    <div class="reply-history-content">
      <!-- 表格展示回复历史 -->
      <a-table
        :columns="columns"
        :data="historyList"
        :pagination="paginationConfig"
        :loading="loading"
        row-key="id"
        :scroll="{ x: 1000 }"
      >
        <!-- 售后单号列 (仅在采购订单类型时显示) -->
        <template #afterSalesNumber="{ record }">
          <span>{{ record.afterSalesNumber || '-' }}</span>
        </template>

        <!-- 回复时间列 -->
        <template #createdAt="{ record }">
          <span>{{ record.createdAt }}</span>
        </template>

        <!-- 回复人列 -->
        <template #replierName="{ record }">
          <span>{{ record.replierName }}</span>
        </template>

        <!-- 回复内容列 -->
        <template #replyContent="{ record }">
          <div class="reply-content-cell">
            <a-tooltip :content="record.replyContent" position="top">
              <div class="content-text">{{ record.replyContent }}</div>
            </a-tooltip>
          </div>
        </template>

        <!-- 责任归属列 -->
        <template #responsibility="{ record }">
          <span>{{ getResponsibilityText(record.responsibility) }}</span>
        </template>

        <!-- 我司售后费用列 -->
        <template #companyAmount="{ record }">
          <div class="cost-info">
            <span class="amount-text">{{ record.companyCostAmount || '-' }}</span>
          </div>
        </template>
        <!-- 我司售后费用列 -->
        <template #companyAmountType="{ record }">
          <div class="cost-info">
            <div class="cost-type-text">{{ record.companyCostTypeText || getCostTypeText(record.companyCostType) }}</div>
          </div>
        </template>
        <!-- 供应商赔偿用列 -->
        <template #supplierCostAmount="{ record }">
          <div class="cost-info">
            <span class="amount-text" style="color: red;" >{{ record.supplierCostAmount || '-' }}</span>
          </div>
        </template>
                <!-- 供应商赔偿用列 -->
        <template #supplierCompensationType="{ record }">
          <div class="cost-info">
            <div class="cost-type-text">{{ record.supplierCostTypeText || getCostTypeText(record.supplierCostType) }}</div>

          </div>
        </template>

        <!-- 售后总费用列 -->
        <template #totalAmount="{ record }">
          <span class="amount-text total-amount">{{ record.totalCostAmount || '-' }}</span>
        </template>

        <!-- 是否退货列 -->
        <template #isReturn="{ record }">
          <a-tag :color="record.isReturn === '是' ? 'green' : 'gray'">
            {{ record.isReturn }}
          </a-tag>
        </template>

        <!-- 回复附件列 -->
        <template #replyAttachments="{ record }">
          <div v-if="record.replyAttachments && record.replyAttachments.length > 0">
            <a-button
              type="text"
              size="small"
              @click="handleViewAttachments(record.replyAttachments)"
            >
              <template #icon><icon-file /></template>
              {{ record.replyAttachments.length }}个文件
            </a-button>
          </div>
          <span v-else>-</span>
        </template>
      </a-table>

      <!-- 空状态 -->
      <!-- <div v-if="historyList.length === 0 && !loading" class="empty-state">
        <a-empty description="暂无回复历史记录" />
      </div> -->
    </div>

    <!-- 附件查看弹窗 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      title="查看附件"
      :width="600"
      :footer="false"
    >
      <div class="attachment-list">
        <div
          v-for="(attachment, index) in currentAttachments"
          :key="index"
          class="attachment-item"
        >
          <div class="attachment-info">
            <icon-file class="file-icon" />
            <span class="file-name">{{ getFileName(attachment) }}</span>
            <span class="file-size">{{ getFileSize(attachment) }}</span>
          </div>
          <div class="attachment-actions">
            <a-button
              type="text"
              size="small"
              @click="handlePreviewAttachment(attachment)"
            >
              <template #icon><icon-eye /></template>
              预览
            </a-button>
            <a-button
              type="text"
              size="small"
              @click="handleDownloadAttachment(attachment)"
            >
              <template #icon><icon-download /></template>
              下载
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'

// 状态映射配置
const responsibilityMappings = {
  6: '客户问题',
  1: '供应商问题',
  2: '物流问题',
  3: '销售员问题',
  4: '采购问题',
  5: '产品问题'
}

const costTypeMappings = {
  0: '快递费',
  1: '安装费',
  2: '其他费用'
}

// 获取责任归属文本
const getResponsibilityText = (value) => {
  return responsibilityMappings[value] || value || '-'
}

// 获取费用类型文本
const getCostTypeText = (value) => {
  return costTypeMappings[value] || value || '-'
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  },
  // 新增类型参数，用于区分不同的使用场景
  type: {
    type: String,
    default: 'afterSales', // 默认为售后场景，兼容原有逻辑
    validator: (value) => ['afterSales', 'purchaseOrder'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const loading = ref(false)
const attachmentModalVisible = ref(false)
const currentAttachments = ref([])
const historyList = ref([])

// 表格列配置 - 根据类型动态生成
const columns = computed(() => {
  const baseColumns = [
    {
      title: '回复时间',
      dataIndex: 'createdAt',
      width: 150,
      slotName: 'createdAt'
    },
    {
      title: '回复人',
      dataIndex: 'replierName',
      width: 100,
      slotName: 'replierName'
    },
    {
      title: '回复内容',
      dataIndex: 'replyContent',
      width: 200,
      slotName: 'replyContent'
    },
    {
      title: '责任归属',
      dataIndex: 'responsibility',
      width: 100,
      slotName: 'responsibility'
    },
    {
      title: '公司承担售后金额',
      dataIndex: 'companyCostAmount',
      width: 140,
      slotName: 'companyAmount'
    },
    {
      title: '我司售后费用',
      dataIndex: 'companyCostTypeText',
      width: 140,
      slotName: 'companyCostTypeText'
    },
    {
      title: '供应商承担售后金额',
      dataIndex: 'supplierCostAmount',
      width: 140,
      slotName: 'supplierCostAmount'
    },
    {
      title: '供应商售后费用',
      dataIndex: 'supplierCostTypeText',
      width: 140,
      slotName: 'supplierCostTypeText'
    },
    {
      title: '售后总费用',
      dataIndex: 'totalCostAmount',
      width: 100,
      slotName: 'totalAmount'
    },
    {
      title: '是否退货',
      dataIndex: 'isReturn',
      width: 80,
      slotName: 'isReturn'
    },
    {
      title: '回复附件',
      dataIndex: 'replyAttachments',
      width: 100,
      slotName: 'replyAttachments'
    }
  ]

  // 如果是采购订单类型，在最前面添加售后单号列
  if (props.type === 'purchaseOrder') {
    return [
      {
        title: '售后单号',
        dataIndex: 'afterSalesNumber',
        width: 180,
        slotName: 'afterSalesNumber'
      },
      ...baseColumns
    ]
  }

  return baseColumns
})

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50'],
  onChange: (page) => {
    paginationConfig.current = page
    loadReplyHistory()
  },
  onPageSizeChange: (pageSize) => {
    paginationConfig.pageSize = pageSize
    paginationConfig.current = 1
    loadReplyHistory()
  }
})

// 加载回复历史数据
const loadReplyHistory = async () => {
  if (!props.afterSalesData?.id) {
    historyList.value = []
    paginationConfig.total = 0
    return
  }

  try {
    loading.value = true
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')

    const params = {
      page: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }

    let response
    if (props.type === 'purchaseOrder') {
      // 采购订单场景：调用订单回复历史接口
      // 接口示例：/api/v1/master/csm/after-sales/order/reply-history?page=1&pagesize=10&order_id=200876911671513088
      response = await afterSalesApi.getOrderReplyHistory(props.afterSalesData.id, params)
    } else {
      // 默认售后场景：调用原有的售后回复历史接口
      response = await afterSalesApi.getReplyHistory(props.afterSalesData.id, params)
    }

    if (response.code === 200) {
      // 直接使用接口返回的数据，其中已包含 afterSalesNumber 字段
      historyList.value = response.data.items || []
      paginationConfig.total = response.data.total || 0
      console.log('回复历史记录加载成功:', response.data)
    } else {
      Message.error(response.message || '加载回复历史记录失败')
      historyList.value = []
      paginationConfig.total = 0
    }
  } catch (error) {
    console.error('加载回复历史记录失败:', error)
    Message.error('加载回复历史记录失败，请稍后重试')
    historyList.value = []
    paginationConfig.total = 0
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    // 重置分页到第一页并加载数据
    paginationConfig.current = 1
    loadReplyHistory()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 查看附件
const handleViewAttachments = (attachments) => {
  currentAttachments.value = attachments || []
  attachmentModalVisible.value = true
}

// 获取文件名
const getFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  return attachment.name || attachment.fileName || '未知文件'
}

// 获取文件大小
const getFileSize = (attachment) => {
  if (typeof attachment === 'object' && attachment.size) {
    const size = attachment.size
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    return `${(size / (1024 * 1024)).toFixed(1)}MB`
  }
  return ''
}

// 预览附件
const handlePreviewAttachment = (attachment) => {
  console.log('预览附件:', attachment)

  let url = ''
  if (typeof attachment === 'string') {
    url = attachment
  } else if (attachment.url) {
    url = attachment.url
  } else if (attachment.path) {
    url = attachment.path
  }

  if (url) {
    // 判断文件类型
    const fileExtension = url.split('.').pop().toLowerCase()
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
    const pdfTypes = ['pdf']

    if (imageTypes.includes(fileExtension)) {
      // 图片预览
      window.open(url, '_blank')
    } else if (pdfTypes.includes(fileExtension)) {
      // PDF预览
      window.open(url, '_blank')
    } else {
      // 其他文件类型直接下载
      handleDownloadAttachment(attachment)
    }
  } else {
    Message.warning('无法预览此文件')
  }
}

// 下载附件
const handleDownloadAttachment = (attachment) => {
  console.log('下载附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    url = attachment.url || attachment.path || ''
    fileName = attachment.name || attachment.fileName || 'download'
  }

  if (url) {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    Message.success('开始下载文件')
  } else {
    Message.error('无法下载此文件')
  }
}
</script>

<style scoped>
.reply-history-content {
  background-color: #ffffff;
}

.reply-content-cell {
  max-width: 200px;
}

.content-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  font-size: 14px;
  color: #333;
}

.amount-text {
  font-weight: 500;
  color: #f53f3f;
}

.cost-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cost-type-text {
  font-size: 12px;
  color: #86909c;
  font-weight: normal;
}

.total-amount {
  font-weight: 600;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

/* 附件相关样式 */
.attachment-list {
  max-height: 400px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e5e6eb;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.file-icon {
  color: #666;
  font-size: 16px;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.attachment-actions .arco-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

:deep(.arco-table-td) {
  padding: 12px 8px;
}

:deep(.arco-table-tbody .arco-table-tr:hover) {
  background-color: #f8f9fa;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .attachment-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .file-name {
    max-width: 100%;
  }
}
</style>
