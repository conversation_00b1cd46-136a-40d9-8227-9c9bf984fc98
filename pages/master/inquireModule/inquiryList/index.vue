<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 询价时间 -->
      <template #inquiryTime="{ record }">
        <div v-time="record.inquiryTime"></div>
      </template>

      <template #operationBeforeExtend="{ record }">
        <a-link @click="editItem(record)">询价详情</a-link>
        <a-link @click="editItem(record)">询价审核</a-link>
        <a-link @click="downloadQuote(record)">下载报价单</a-link>
      </template>
    </MaCrud>

    <!-- 引入报价单下载组件 -->
    <QuotationDownload ref="quotationDownloadRef" />
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import {
  IconEdit,
  IconDelete,
  IconPlus,
  IconEye,
} from "@arco-design/web-vue/es/icon";
import { useRouter } from "vue-router";
import QuotationDownload from "./components/QuotationDownload.vue";
import { inquiryApi } from "@/api/master/csm/inquiry.js";

definePageMeta({
  name: "master-inquireModule-inquiryList",
  path: "/master/inquireModule/inquiryList",
});

const router = useRouter();
const crudRef = ref();
const quotationDownloadRef = ref();

// 查看详情
const viewDetail = (record) => {
  // 跳转到详情页
  import("@/utils/common").then((module) => {
    module.navigateWithTag(
      router,
      `/master/inquireModule/inquiryDetails/${record.id}`
    );
  });
};

// 编辑项目
const editItem = (record) => {
  // 编辑逻辑
  import("@/utils/common").then((module) => {
    // 分离路径和查询参数，避免创建两个路由页面
    module.navigateWithTag(
      router,
      `/master/inquireModule/inquiryDetails/${record.id}`,
      { mode: "edit" }
    );
  });
};

// 下载报价单
const downloadQuote = (record) => {
  // 打开报价单下载弹窗
  quotationDownloadRef.value.open();
};

// 状态处理函数
const getStatusText = (status) => {
  const statusMap = {
    0: "待询价",
    1: "询价中",
    2: "已询价",
    3: "已取消",
    4: "已过期",
  };
  return statusMap[status] || "未知";
};

const getStatusColor = (status) => {
  const colorMap = {
    0: "orange",
    1: "blue",
    2: "green",
    3: "red",
    4: "gray",
  };
  return colorMap[status] || "gray";
};

// 获取询价单列表数据
const getInquiryList = async (params) => {
  console.log("查询参数:", params);

  try {
    // 调用真实API
    const result = await inquiryApi.getInquiryList({
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      inquiryNo: params.inquiryNo,
      customerId: params.customerId,
      startDate: params.startDate,
      endDate: params.endDate,
      // 新增筛选参数
      salesman: params.salesman,
      customer: params.customer,
      customerCode: params.customerCode,
      merchandiser: params.merchandiser,
      inquirer: params.inquirer,
    });

    if (result.code === 200) {
      // 直接使用API返回的完整数据，不需要额外的API调用
      const processedItems = result.data.items.map((item) => {
        // 计算待询价项数（状态为0的询价详情数量）
        const pendingItemCount = item.inquiryDetails
          ? item.inquiryDetails.filter((detail) => detail.quoteStatus === 0)
              .length
          : 0;

        return {
          id: item.id,
          inquiryNo: item.inquiryNo,
          inquiryTime: parseInt(item.createdAt), // 转换为时间戳
          salesman:
            item.salesman?.nickname || item.salesman?.username || "未知",
          customer: item.customer?.name || "未知客户",
          customerCode: item.customerId,
          customerAddress: item.customerAddress || "",
          inquiryItemCount: item.totalItems || 0,
          pendingItemCount: pendingItemCount,
          merchandiser:
            item.salesman?.nickname || item.salesman?.username || "未知", // 暂时用业务员代替跟单员
          inquirer:
            item.inquirer?.nickname || item.inquirer?.username || "未知",
          // 保留原始数据
          originalData: item,
        };
      });

      return {
        data: {
          items: processedItems,
          pageInfo: {
            total: result.data.total,
            page: result.data.page,
            pageSize: result.data.pageSize,
            totalPages: result.data.totalPages,
          },
        },
        code: 200,
        message: "获取成功",
      };
    } else {
      Message.error(result.message || "获取询价单列表失败");
      return {
        data: { items: [], pageInfo: { total: 0 } },
        code: result.code,
        message: result.message,
      };
    }
  } catch (error) {
    console.error("获取询价单列表失败:", error);
    Message.error("获取询价单列表失败，请稍后重试");
    return {
      data: { items: [], pageInfo: { total: 0 } },
      code: 500,
      message: "网络错误",
    };
  }
};

// 新增询价单
const handleAdd = () => {
  import("@/utils/common").then((module) => {
    module.navigateWithTag(router, "/master/inquireModule/inquiryDetails/add");
  });
};
const handleDelete = () => {
  console.log("删除询价单");
};

// CRUD 配置
const crud = reactive({
  showTools: false,
  api: getInquiryList, // 使用本地mock数据函数
  pageLayout: "fixed",
  operationColumn: true,
  operationColumnWidth: 250,
  searchLabelWidth: "100px",
  add: {
    show: false,
    text: "新增",
    action: handleAdd,
  },
  edit: {
    show: false,
    action: editItem,
  },
  delete: {
    show: false,
    action: handleDelete,
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有询价时间参数，转换为毫秒级时间戳
    if (params.inquiryTime && params.inquiryTime.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.inquiryTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startDate = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.inquiryTime[1]);
      endDate.setHours(23, 59, 59, 999);
      params.endDate = endDate.getTime();

      delete params.inquiryTime;
    } else {
      delete params.startDate;
      delete params.endDate;
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "询价单号",
    dataIndex: "inquiryNo",
    search: true,
    width: 150,
  },
  {
    title: "询价时间",
    dataIndex: "inquiryTime",
    formType: "range",
    search: true,
    width: 180,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "业务员",
    dataIndex: "salesman",
    search: true,
    width: 120,
  },
  {
    title: "客户",
    dataIndex: "customer",
    search: true,
    width: 150,
  },
  {
    title: "客户编码",
    dataIndex: "customerCode",
    search: true,
    width: 120,
  },
  {
    title: "客户地址",
    dataIndex: "customerAddress",
    search: false,
    ellipsis: true,
    width: 200,
  },
  {
    title: "询价项数",
    dataIndex: "inquiryItemCount",
    search: false,
    width: 100,
  },
  {
    title: "待询价项数",
    dataIndex: "pendingItemCount",
    search: false,
    width: 100,
  },
  {
    title: "跟单员",
    dataIndex: "merchandiser",
    search: true,
    width: 120,
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    search: true,
    width: 120,
  },
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>
