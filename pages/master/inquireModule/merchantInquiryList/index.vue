<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 询价时间 -->
      <template #inquiryTime="{ record }">
        <div v-time="record.inquiryTime"></div>
      </template>

      <template #operationBeforeExtend="{ record }">
        <a-button type="text" size="small" @click="viewDetail(record)">
          <icon-eye />详情
        </a-button>
        <a-button type="text" size="small" @click="openSalesPriceModal(record)">
          <icon-check />确认销售价
        </a-button>
        <a-button
          type="text"
          size="small"
          @click="openQuotationDownload(record)"
        >
          <icon-to-bottom />下载报价单
        </a-button>
        <a-button type="text" size="small" @click="openApplyForShelves(record)">
          <icon-to-top />申请上架
        </a-button>
      </template>
    </MaCrud>

    <!-- 确认销售价弹窗 -->
    <DetermineTheSalesPrice ref="determineSalesPriceRef" />

    <!-- 下载报价单弹窗 -->
    <QuotationDownload ref="quotationDownloadRef" />

    <!-- 申请上架弹窗 -->
    <ApplyForShelves ref="applyForShelvesRef" />
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import {
  IconEdit,
  IconDelete,
  IconPlus,
  IconEye,
} from "@arco-design/web-vue/es/icon";
import DetermineTheSalesPrice from "./components/DetermineTheSalesPrice.vue";
import QuotationDownload from "./components/QuotationDownload.vue";
import ApplyForShelves from "./components/ApplyForShelves.vue";
import { inquiryApi } from "@/api/master/csm/inquiry.js";
definePageMeta({
  name: "master-inquireModule-merchantInquiryList",
  path: "/master/inquireModule/merchantInquiryList",
});

const crudRef = ref();
const determineSalesPriceRef = ref();
const quotationDownloadRef = ref();
const applyForShelvesRef = ref();

// 状态处理函数
const getStatusText = (status) => {
  const statusMap = {
    0: "待询价",
    1: "询价中",
    2: "已询价",
    3: "已取消",
    4: "已过期",
  };
  return statusMap[status] || "未知";
};

const getStatusColor = (status) => {
  const colorMap = {
    0: "orange",
    1: "blue",
    2: "green",
    3: "red",
    4: "gray",
  };
  return colorMap[status] || "gray";
};

// 查看详情
const viewDetail = (record) => {
  // 跳转到详情页或打开详情抽屉
  // Message.info(`查看询价单: ${record.inquiryNo}`);
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(
      router,
      `/master/inquireModule/merchantInquiryDetails/${record.id}`
    );
  });
};

// 编辑项目
const editItem = (record) => {
  // 编辑逻辑
  Message.info(`编辑询价单: ${record.inquiryNo}`);
};

// 打开确认销售价弹窗
const openSalesPriceModal = (record) => {
  // 打开确认销售价弹窗
  determineSalesPriceRef.value.open(record.id);
};

// 打开下载报价单弹窗
const openQuotationDownload = (record) => {
  // 打开下载报价单弹窗
  quotationDownloadRef.value.open();
  // 可以在这里添加传递询价单ID的逻辑
  Message.info(`准备下载询价单: ${record.inquiryNo}的报价单`);
};

// 打开申请上架弹窗
const openApplyForShelves = (record) => {
  // 打开申请上架弹窗
  applyForShelvesRef.value.open();
  // 可以在这里添加传递询价单ID的逻辑
  Message.info(`准备申请上架询价单: ${record.inquiryNo}`);
};

// 用户映射缓存
const userCache = ref(new Map());
const channelCache = ref(new Map());
const customerCache = ref(new Map());

// 获取用户信息（缓存）
const getUserInfo = async (userId) => {
  if (!userId) return null;

  if (userCache.value.has(userId)) {
    return userCache.value.get(userId);
  }

  try {
    const result = await inquiryApi.getSystemUsers({ id: userId });
    if (result.code === 200 && result.data.items.length > 0) {
      const user = result.data.items[0];
      userCache.value.set(userId, user);
      return user;
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
  }
  return null;
};

// 获取渠道信息（缓存）
const getChannelInfo = async (channelId) => {
  if (!channelId) return null;

  if (channelCache.value.has(channelId)) {
    return channelCache.value.get(channelId);
  }

  try {
    const result = await inquiryApi.getPlatformChannels({ id: channelId });
    if (result.code === 200 && result.data.items.length > 0) {
      const channel = result.data.items[0];
      channelCache.value.set(channelId, channel);
      return channel;
    }
  } catch (error) {
    console.error("获取渠道信息失败:", error);
  }
  return null;
};

// 获取客户信息（缓存）
const getCustomerInfo = async (customerId) => {
  if (!customerId) return null;

  if (customerCache.value.has(customerId)) {
    return customerCache.value.get(customerId);
  }

  try {
    const result = await inquiryApi.getCustomerOptions({ id: customerId });
    if (result.code === 200 && result.data.items.length > 0) {
      const customer = result.data.items[0];
      customerCache.value.set(customerId, customer);
      return customer;
    }
  } catch (error) {
    console.error("获取客户信息失败:", error);
  }
  return null;
};

// 获取询价单列表数据
const getInquiryList = async (params) => {
  console.log("查询参数:", params);

  try {
    // 调用真实API
    const result = await inquiryApi.getInquiryList({
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      inquiryNo: params.inquiryNo,
      customerId: params.customerId,
      startDate: params.startDate,
      endDate: params.endDate,
      // 新增筛选参数
      salesman: params.salesman,
      customer: params.customer,
      customerCode: params.customerCode,
      merchandiser: params.merchandiser,
      inquirer: params.inquirer,
    });

    if (result.code === 200) {
      // 直接使用API返回的完整数据，不需要额外的API调用
      const processedItems = result.data.items.map((item) => {
        return {
          id: item.id,
          inquiryNo: item.inquiryNo,
          inquiryTime: parseInt(item.createdAt), // 转换为时间戳
          salesman:
            item.salesman?.nickname || item.salesman?.username || "未知",
          customer: item.customer?.name || "未知客户",
          customerCode: item.customerId,
          customerAddress: item.customerAddress || "",
          inquiryItemCount: item.totalItems || 0,
          pendingItemCount: 0, // 待询价项数稍后补充
          merchandiser:
            item.salesman?.nickname || item.salesman?.username || "未知", // 暂时用业务员代替跟单员
          inquirer:
            item.inquirer?.nickname || item.inquirer?.username || "未知",

          // 保留原始数据
          originalData: item,
        };
      });

      return {
        data: {
          items: processedItems,
          pageInfo: {
            total: result.data.total,
            page: result.data.page,
            pageSize: result.data.pageSize,
            totalPages: result.data.totalPages,
          },
        },
        code: 200,
        message: "获取成功",
      };
    } else {
      Message.error(result.message || "获取询价单列表失败");
      return {
        data: { items: [], pageInfo: { total: 0 } },
        code: result.code,
        message: result.message,
      };
    }
  } catch (error) {
    console.error("获取询价单列表失败:", error);
    Message.error("获取询价单列表失败");
    return {
      data: { items: [], pageInfo: { total: 0 } },
      code: 500,
      message: "网络错误",
    };
  }
};

// 发起询价
const handleInquiry = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(
      router,
      "/master/inquireModule/merchantInitiateInquiry"
    );
  });
};
// CRUD 配置
const crud = reactive({
  showTools: false,
  api: getInquiryList, // 使用真实API函数
  pageLayout: "fixed",
  operationColumn: true,
  operationColumnWidth: 480,
  searchLabelWidth: "100px",
  add: {
    show: true,
    text: "发起询价",
    action: handleInquiry,
  },

  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有询价时间参数，转换为毫秒级时间戳
    if (params.inquiryTime && params.inquiryTime.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.inquiryTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startDate = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.inquiryTime[1]);
      endDate.setHours(23, 59, 59, 999);
      params.endDate = endDate.getTime();

      delete params.inquiryTime;
    } else {
      delete params.startDate;
      delete params.endDate;
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "询价单号",
    dataIndex: "inquiryNo",
    search: true,
    width: 150,
  },
  {
    title: "询价时间",
    dataIndex: "inquiryTime",
    formType: "range",
    search: true,
    width: 180,
    addDisplay: false,
    editDisplay: false,
  },

  {
    title: "业务员",
    dataIndex: "salesman",
    search: true,
    width: 120,
  },
  {
    title: "客户",
    dataIndex: "customer",
    search: true,
    width: 150,
  },
  {
    title: "客户编码",
    dataIndex: "customerCode",
    search: true,
    width: 120,
  },
  {
    title: "客户地址",
    dataIndex: "customerAddress",
    search: false,
    ellipsis: true,
    width: 200,
  },
  {
    title: "询价项数",
    dataIndex: "inquiryItemCount",
    search: false,
    width: 100,
  },
  {
    title: "待询价项数",
    dataIndex: "pendingItemCount",
    search: false,
    width: 100,
  },
  {
    title: "跟单员",
    dataIndex: "merchandiser",
    search: true,
    width: 120,
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    search: true,
    width: 120,
  },
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>
