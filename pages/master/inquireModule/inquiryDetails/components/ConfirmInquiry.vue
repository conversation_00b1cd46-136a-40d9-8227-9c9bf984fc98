<template>
  <a-drawer
    :visible="visible"
    title="确认询价"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:visible="(val) => (visible = val)"
  >
    <div class="inquiry-table-container">
      <a-table
        :data="tableData"
        :pagination="pagination"
        :loading="loading"
        @page-change="onPageChange"
        stripe
        border
        scroll-x="2000"
      >
        <template #columns>
          <a-table-column
            title="实际产品名称"
            data-index="actualProductName"
            :width="150"
          />
          <a-table-column
            title="实际品牌"
            data-index="actualBrand"
            :width="100"
          />
          <a-table-column
            title="实际型号"
            data-index="actualModel"
            :width="100"
          />
          <a-table-column
            title="实际规格描述"
            data-index="actualSpecDesc"
            :width="150"
          />
          <a-table-column
            title="包装量"
            data-index="packageAmount"
            :width="80"
          />
          <a-table-column
            title="询价产品编码"
            data-index="inquiryProductCode"
            :width="120"
          />
          <a-table-column
            title="产品编码"
            data-index="productCode"
            :width="100"
          />
          <a-table-column title="成本价" data-index="costPrice" :width="80" />
          <a-table-column
            title="询价单位"
            data-index="inquiryUnit"
            :width="80"
          />
          <a-table-column
            title="含税运情况"
            data-index="taxIncludedShipping"
            :width="100"
          />
          <a-table-column title="运费" data-index="freightCost" :width="80" />
          <a-table-column
            title="货期"
            data-index="deliveryPeriod"
            :width="80"
          />
          <a-table-column
            title="附加费"
            data-index="additionalFee"
            :width="80"
          />
          <a-table-column
            title="销售价"
            data-index="sellingPrice"
            :width="80"
          />
          <a-table-column
            title="实际询价备注"
            data-index="actualInquiryRemark"
            :width="150"
          />
          <a-table-column title="供应商" data-index="supplier" :width="120" />
          <a-table-column
            title="上架资料链接"
            data-index="listingInfoLink"
            :width="120"
          >
            <template #cell="{ record }">
              <a-link
                v-if="record.listingInfoLink"
                :href="record.listingInfoLink"
                target="_blank"
                >查看</a-link
              >
              <span v-else>-</span>
            </template>
          </a-table-column>
          <a-table-column
            title="询价员"
            data-index="inquiryStaff"
            :width="100"
          />
          <a-table-column
            title="询价状态"
            data-index="inquiryStatus"
            :width="100"
          >
            <template #cell="{ record }">
              <a-tag :color="getStatusColor(record.inquiryStatus)">{{
                record.inquiryStatus
              }}</a-tag>
            </template>
          </a-table-column>
          <a-table-column
            title="确认状态"
            data-index="isConfirmed"
            :width="100"
          >
            <template #cell="{ record }">
              <a-tag :color="record.isConfirmed ? 'green' : 'orange'">
                {{ record.isConfirmed ? "已确认" : "待确认" }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="操作" :width="160" fixed="right">
            <template #cell="{ record }">
              <a-space>
                <a-button
                  v-if="!record.isConfirmed"
                  type="primary"
                  size="small"
                  @click="confirmItem(record)"
                  >确认询价项</a-button
                >
                <a-button
                  v-if="record.isConfirmed"
                  type="outline"
                  status="danger"
                  size="small"
                  @click="cancelItem(record)"
                  >取消确认</a-button
                >
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineExpose } from "vue";
import { Message } from "@arco-design/web-vue";

// 定义props
const props = defineProps({
  // 可以添加需要的props
});

// 定义事件
const emit = defineEmits(["success"]);

// 弹窗显示状态
const visible = ref(false);

// 表单数据
const formData = ref({
  inquiryNo: "",
  confirmRemark: "",
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
});

// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    待确认: "orange",
    已确认: "green",
    已取消: "red",
    处理中: "blue",
  };
  return statusMap[status] || "gray";
};

// 当前询价单ID
const currentId = ref("");

/**
 * 打开弹窗方法
 * @param {Object} data 询价单数据
 */
const open = (data) => {
  visible.value = true;
  currentId.value = data.id || "";
  formData.value.inquiryNo = data.inquiryNo || "";
  formData.value.confirmRemark = "";

  // 如果传递了tableData，直接使用传递的数据
  if (data.tableData && data.tableData.length > 0) {
    tableData.value = data.tableData.map((item) => ({
      id: item.id,
      actualProductName: item.productName,
      actualBrand: item.brand,
      actualModel: item.model,
      actualSpecDesc: item.specification,
      packageAmount: item.quantity || "",
      inquiryProductCode: item.inquiryCode || "",
      productCode: item.customerProductCode || "",
      costPrice: item.referencePrice || "",
      inquiryUnit: item.unit || "",
      taxIncludedShipping: "含税含运",
      freightCost: "0.00",
      deliveryPeriod: "7天",
      additionalFee: "0.00",
      sellingPrice: item.referencePrice || "",
      actualInquiryRemark: item.remark || "",
      supplier: "待确认",
      listingInfoLink: item.referenceLink || "",
      inquiryStaff: item.inquirer || "",
      inquiryStatus:
        item.status === "pending"
          ? "待询价"
          : item.status === "processing"
          ? "处理中"
          : item.status === "completed"
          ? "已确认"
          : "待确认",
      // 添加确认状态字段，默认为已确认
      isConfirmed: item.isConfirmed !== undefined ? item.isConfirmed : true,
    }));
    pagination.value.total = data.tableData.length;
    loading.value = false;
  } else {
    // 如果没有传递tableData，则通过API获取
    fetchInquiryDetails(data.id);
  }
};

/**
 * 获取询价单详情数据
 * @param {String} id 询价单ID
 */
const fetchInquiryDetails = async (id) => {
  if (!id) return;
  loading.value = true;
  try {
    // 这里添加获取询价详情的API调用
    // const res = await getInquiryDetails(id);
    // tableData.value = res.data || [];
    // pagination.value.total = res.total || 0;

    // 模拟数据，实际开发中请替换为API调用
    setTimeout(() => {
      tableData.value = [
        {
          id: "1",
          actualProductName: "测试产品1",
          actualBrand: "品牌A",
          actualModel: "A001",
          actualSpecDesc: "规格描述1",
          packageAmount: "100",
          inquiryProductCode: "INQ001",
          productCode: "P001",
          costPrice: "100.00",
          inquiryUnit: "个",
          taxIncludedShipping: "含税",
          freightCost: "10.00",
          deliveryPeriod: "7天",
          additionalFee: "5.00",
          sellingPrice: "150.00",
          actualInquiryRemark: "备注信息1",
          supplier: "供应商A",
          listingInfoLink: "https://example.com/listing/1",
          inquiryStaff: "张三",
          inquiryStatus: "已确认",
          isConfirmed: true,
        },
        {
          id: "2",
          actualProductName: "测试产品2",
          actualBrand: "品牌B",
          actualModel: "B002",
          actualSpecDesc: "规格描述2",
          packageAmount: "50",
          inquiryProductCode: "INQ002",
          productCode: "P002",
          costPrice: "200.00",
          inquiryUnit: "箱",
          taxIncludedShipping: "不含税",
          freightCost: "20.00",
          deliveryPeriod: "15天",
          additionalFee: "8.00",
          sellingPrice: "280.00",
          actualInquiryRemark: "备注信息2",
          supplier: "供应商B",
          listingInfoLink: "https://example.com/listing/2",
          inquiryStaff: "李四",
          inquiryStatus: "已确认",
          isConfirmed: true,
        },
      ];
      pagination.value.total = 2;
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error("获取询价详情失败:", error);
    Message.error("获取询价详情失败");
    loading.value = false;
  }
};

/**
 * 分页变化处理
 */
const onPageChange = (page) => {
  pagination.value.current = page;
  fetchInquiryDetails(currentId.value);
};

/**
 * 确认单个询价项
 */
const confirmItem = async (record) => {
  try {
    // 这里添加确认单个询价项的API调用
    // const res = await confirmInquiryItem({
    //     id: record.id,
    //     confirmRemark: formData.value.confirmRemark
    // });

    // 更新本地数据状态
    const index = tableData.value.findIndex((item) => item.id === record.id);
    if (index !== -1) {
      tableData.value[index].isConfirmed = true;
    }

    Message.success("询价项确认成功");
    // fetchInquiryDetails(currentId.value);
  } catch (error) {
    console.error("确认询价项失败:", error);
    Message.error("确认询价项失败");
  }
};

/**
 * 取消单个询价项
 */
const cancelItem = async (record) => {
  try {
    // 这里添加取消单个询价项的API调用
    // const res = await cancelInquiryItem({
    //     id: record.id,
    //     cancelRemark: formData.value.confirmRemark
    // });

    // 更新本地数据状态
    const index = tableData.value.findIndex((item) => item.id === record.id);
    if (index !== -1) {
      tableData.value[index].isConfirmed = false;
    }

    Message.success("询价项取消确认成功");
    // fetchInquiryDetails(currentId.value);
  } catch (error) {
    console.error("取消询价项失败:", error);
    Message.error("取消询价项失败");
  }
};

/**
 * 确认按钮处理
 */
const handleOk = async () => {
  try {
    // 遍历tableData，获取确认选择的数据ID集合
    const confirmedIds = tableData.value
      .filter((item) => item.isConfirmed)
      .map((item) => item.id);

    console.log("确认选择的询价项ID集合:", confirmedIds);

    // 检查是否有确认的项目
    if (confirmedIds.length === 0) {
      Message.warning("请至少选择一个询价项进行确认");
      return;
    }

    // 这里添加确认询价的API调用
    // const res = await confirmInquiry({
    //     id: currentId.value,
    //     confirmRemark: formData.value.confirmRemark,
    //     confirmedItemIds: confirmedIds
    // });

    Message.success(`询价确认成功，共确认 ${confirmedIds.length} 个询价项`);
    visible.value = false;
    emit("success", { confirmedIds });
  } catch (error) {
    console.error("确认询价失败:", error);
    Message.error("确认询价失败");
  }
};

/**
 * 取消按钮处理
 */
const handleCancel = () => {
  visible.value = false;
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style scoped>
.confirm-inquiry-content {
  padding: 10px;
}

.inquiry-table-container {
  margin-top: 20px;
}
</style>
