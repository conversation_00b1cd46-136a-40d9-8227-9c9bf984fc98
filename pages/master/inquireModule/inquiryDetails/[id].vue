<template>
  <div v-loading="loading">
    <a-card class="mb-4" :bordered="false">
      <a-descriptions :data="customerData" title="客户信息" bordered />
    </a-card>

    <a-card class="mb-4" :bordered="false">
      <template #title>
        <div class="flex items-center justify-between">
          <span>产品询价信息</span>
          <div>
            <a-space>
              <a-button
                type="primary"
                size="small"
                @click="openChangeInquiryClerkModal"
              >
                更改询价员
              </a-button>
              <a-button type="primary" size="small"> 导出 </a-button>
            </a-space>
          </div>
        </div>
      </template>
      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :bordered="true"
        stripe
        :scroll="{ x: '100%' }"
        :row-key="(record) => record.id"
      />

      <!-- 附件链接展示区域 -->
      <div class="mt-4">
        <div class="font-medium text-base mb-2">相关附件</div>
        <div class="grid grid-cols-3 gap-4">
          <div
            v-for="(attachment, index) in attachments"
            :key="index"
            class="border rounded p-3 flex items-center"
          >
            <a
              :href="attachment.url"
              target="_blank"
              class="text-blue-600 hover:underline flex-1 truncate"
              >{{ attachment.name }}</a
            >
          </div>
          <div
            v-if="attachments.length === 0"
            class="col-span-3 text-center text-gray-500 py-4"
          >
            暂无附件
          </div>
        </div>
      </div>
    </a-card>

    <a-card :bordered="false" class="mb-4">
      <template #title>
        <div class="flex items-center justify-between">
          <span>询价结果</span>
          <div>
            <a-space>
              <a-button
                type="primary"
                size="small"
                @click="openQuotationDownloadModal"
              >
                下载报价单
              </a-button>
              <a-button
                type="primary"
                size="small"
                @click="openConfirmInquiryModal"
              >
                确认询价
              </a-button>
              <a-button
                type="primary"
                size="small"
                @click="openInquiryAuditModal"
              >
                询价审核
              </a-button>
              <a-button
                type="primary"
                size="small"
                @click="openInquiryImportModal"
              >
                询价结果导入
              </a-button>
            </a-space>
          </div>
        </div>
      </template>
      <a-table
        :columns="resultColumns"
        :data="resultData"
        :pagination="{ pageSize: 10 }"
        :bordered="true"
        stripe
        :scroll="{ x: '100%' }"
        :row-key="(record) => record.id"
      >
        <!-- 操作列插槽 -->
        <template #operations="{ record }">
          <a-space>
            <a-button
              type="text"
              size="small"
              @click="handleEdit(record, 'edit')"
            >
              编辑
            </a-button>
            <a-button
              type="text"
              size="small"
              @click="handleViewReasonsForInquiry(record)"
            >
              重新询价原因
            </a-button>
            <a-button
              type="text"
              size="small"
              @click="handleEdit(record, 'supplement')"
            >
              补充资料
            </a-button>
            <a-button
              type="text"
              size="small"
              @click="handleEdit(record, 'technical')"
            >
              转技术
            </a-button>
            <a-button type="text" size="small" @click="handleViewAudit(record)">
              审核记录
            </a-button>
            <a-button
              type="text"
              status="danger"
              size="small"
              @click="handleDelete(record)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <a-card :bordered="false">
      <template #title>
        <div class="flex items-center justify-between">
          <span>供应商总运费</span>
        </div>
      </template>
      <a-table
        :columns="summaryColumns"
        :data="summaryData"
        :pagination="false"
        :bordered="true"
        stripe
        :scroll="{ x: '100%' }"
        :row-key="(record) => record.id"
      />
    </a-card>

    <!-- 更改询价员弹窗组件 -->
    <ChangeInquiryClerk
      v-model:visible="changeInquiryClerkVisible"
      :inquiry-products="tableData"
      @ok="handleChangeInquiryClerk"
      ref="changeInquiryClerkRef"
    />

    <!-- 确认询价弹窗组件 -->
    <ConfirmInquiry
      ref="confirmInquiryRef"
      @success="handleConfirmInquirySuccess"
    />

    <!-- 询价表单弹窗组件 -->
    <InquiryFormModal
      ref="inquiryFormModalRef"
      @submit="handleInquiryFormSubmit"
    />

    <!-- 审核记录弹窗组件 -->
    <AuditRecord ref="auditRecordRef" />

    <!-- 重新询价原因弹窗组件 -->
    <ReasonsForInquiry ref="reasonsForInquiryRef" />

    <!-- 报价单下载弹窗组件 -->
    <QuotationDownload ref="quotationDownloadRef" />

    <!-- 询价审核弹窗组件 -->
    <InquiryAudit ref="inquiryAuditRef" />

    <!-- 询价结果导入弹窗组件 -->
    <InquiryImport
      ref="inquiryImportRef"
      @import-success="handleInquiryImportSuccess"
    />
  </div>
</template>

<script setup>
import { ref, h, onMounted } from "vue";
import { Message, InputNumber as AInputNumber } from "@arco-design/web-vue";
import { useRoute } from "vue-router";
import ChangeInquiryClerk from "./components/ChangeInquiryClerk.vue";
import ConfirmInquiry from "./components/ConfirmInquiry.vue";
import InquiryFormModal from "./components/InquiryFormModal.vue";
import AuditRecord from "./components/AuditRecord.vue";
import ReasonsForInquiry from "./components/ReasonsForInquiry.vue";
import QuotationDownload from "./components/QuotationDownload.vue";
import InquiryAudit from "./components/InquiryAudit.vue";
import InquiryImport from "./components/InquiryImport.vue";
import { inquiryApi } from "@/api/master/csm/inquiry.js";
definePageMeta({
  name: "master-inquireModule-inquiryDetails",
  path: "/master/inquireModule/inquiryDetails/:id",
});

const route = useRoute();
const inquiryId = route.params.id;
const mode = route.query.mode || "view"; // 支持 view, edit 模式

// 响应式数据
const loading = ref(false);
const inquiryData = ref(null);

// 控制更改询价员弹窗显示
const changeInquiryClerkVisible = ref(false);
// 创建弹窗组件的引用
const changeInquiryClerkRef = ref(null);

// 创建确认询价弹窗组件的引用
const confirmInquiryRef = ref(null);

// 创建询价表单弹窗组件的引用
const inquiryFormModalRef = ref(null);

// 创建审核记录弹窗组件的引用
const auditRecordRef = ref(null);

// 创建重新询价原因弹窗组件的引用
const reasonsForInquiryRef = ref(null);

// 创建报价单下载弹窗组件的引用
const quotationDownloadRef = ref(null);

// 创建询价审核弹窗组件的引用
const inquiryAuditRef = ref(null);

// 创建询价结果导入弹窗组件的引用
const inquiryImportRef = ref(null);

// 获取询价详情数据
const fetchInquiryDetail = async () => {
  if (!inquiryId) {
    Message.error("询价单ID不能为空");
    return;
  }

  loading.value = true;
  try {
    const result = await inquiryApi.getInquiryDetail(inquiryId);

    if (result.code === 200) {
      inquiryData.value = result.data;

      // 更新客户数据
      updateCustomerData(result.data);

      // 更新产品询价信息
      updateTableData(result.data);

      // 更新附件信息
      updateAttachments(result.data);

      console.log("询价详情数据:", result.data);
    } else {
      Message.error(result.message || "获取询价详情失败");
    }
  } catch (error) {
    console.error("获取询价详情失败:", error);
    Message.error("获取询价详情失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 更新客户数据
const updateCustomerData = (data) => {
  customerData.value = [
    {
      label: "客户",
      value: data.customer?.name || "未知客户",
    },
    {
      label: "客户编码",
      value: data.customerId || "",
    },
    {
      label: "客户地址",
      value: data.customerAddress || "",
    },
    {
      label: "客户联系人",
      value: data.contact?.name || "未知联系人",
    },
    {
      label: "下单平台",
      value: data.channel?.name || "未知渠道",
    },
    {
      label: "业务员",
      value: data.salesman?.nickname || data.salesman?.username || "未知",
    },
  ];
};

// 更新产品询价信息
const updateTableData = (data) => {
  if (data.inquiryDetails && data.inquiryDetails.length > 0) {
    tableData.value = data.inquiryDetails.map((detail) => ({
      id: detail.id,
      productName: detail.productName,
      brand: detail.brand,
      model: detail.model,
      specification: detail.specification,
      inquiryCode: data.inquiryNo,
      referenceLink: detail.referenceLink,
      unit: detail.unit,
      quantity: detail.quantity,
      referencePrice: detail.referencePrice,
      inquirer: data.inquirer?.nickname || data.inquirer?.username || "未知",
      customerProductCode: detail.customerProductCode,
      status: getInquiryStatus(detail.quoteStatus),
      remark: detail.inquiryRemark,
    }));
  } else {
    tableData.value = [];
  }
};

// 更新附件信息
const updateAttachments = (data) => {
  try {
    let attachmentList = [];

    if (data.attachments && data.attachments !== null) {
      // 如果是字符串，尝试解析JSON
      if (typeof data.attachments === "string") {
        try {
          attachmentList = JSON.parse(data.attachments);
        } catch (parseError) {
          console.warn("附件JSON解析失败:", parseError);
          attachmentList = [];
        }
      } else if (Array.isArray(data.attachments)) {
        attachmentList = data.attachments;
      }
    }

    if (Array.isArray(attachmentList) && attachmentList.length > 0) {
      attachments.value = attachmentList.map((attachment) => ({
        name: attachment.fileName || attachment.name || "未知文件",
        url: attachment.fileUrl || attachment.url || "",
        size: attachment.fileSize || attachment.size || 0,
      }));
    } else {
      attachments.value = [];
    }

    console.log("附件信息更新完成:", attachments.value);
  } catch (error) {
    console.error("解析附件信息失败:", error);
    attachments.value = [];
  }
};

// 获取询价状态
const getInquiryStatus = (quoteStatus) => {
  const statusMap = {
    0: "pending",
    1: "processing",
    2: "completed",
    3: "cancelled",
  };
  return statusMap[quoteStatus] || "pending";
};

// 打开更改询价员弹窗
const openChangeInquiryClerkModal = () => {
  // 直接设置 visible 状态
  changeInquiryClerkVisible.value = true;
};

// 打开确认询价弹窗
const openConfirmInquiryModal = () => {
  // 获取当前询价单信息
  const inquiryData = {
    id: "123456", // 这里应该是实际的询价单ID
    inquiryNo: "INQ-" + new Date().getTime(), // 这里应该是实际的询价单号
    tableData: tableData.value, // 传递tableData数据
  };

  // 通过 ref 调用组件的 open 方法
  confirmInquiryRef.value.open(inquiryData);
};

// 处理确认询价成功
const handleConfirmInquirySuccess = () => {
  // 刷新询价结果数据或进行其他操作
  // Message.success("询价确认成功");
};

// 处理询价表单提交
const handleInquiryFormSubmit = (formData) => {
  console.log("询价表单提交数据:", formData);

  // 更新表格数据
  const index = resultData.value.findIndex((item) => item.id === formData.id);
  if (index !== -1) {
    // 更新现有记录
    Object.assign(resultData.value[index], formData);
    Message.success("询价信息更新成功");
  } else {
    // 添加新记录
    formData.id = "r" + (resultData.value.length + 1);
    resultData.value.push(formData);
    Message.success("询价信息添加成功");
  }
};

// 处理更改询价员
const handleChangeInquiryClerk = (data) => {
  // 获取更新后的数据
  const { inquirerId, inquirerName, products } = data;

  // 更新表格数据中的询价员信息
  products.forEach((updatedProduct) => {
    const index = tableData.findIndex((item) => item.id === updatedProduct.id);
    if (index !== -1) {
      tableData[index].inquirer = inquirerName;
    }
  });

  // 显示成功提示
  window.$message.success(
    `已成功将${products.length}个产品的询价员更改为${inquirerName}`
  );
};

// 客户数据（响应式）
const customerData = ref([
  {
    label: "客户",
    value: "加载中...",
  },
  {
    label: "客户编码",
    value: "加载中...",
  },
  {
    label: "客户地址",
    value: "加载中...",
  },
  {
    label: "客户联系人",
    value: "加载中...",
  },
  {
    label: "下单平台",
    value: "加载中...",
  },
  {
    label: "业务员",
    value: "加载中...",
  },
]);

// 表格列定义
const columns = [
  {
    title: "产品名称",
    dataIndex: "productName",
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "品牌",
    dataIndex: "brand",
    width: 100,
  },
  {
    title: "型号",
    dataIndex: "model",
    width: 120,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "规格描述",
    dataIndex: "specification",
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "询价产品编码",
    dataIndex: "inquiryCode",
    width: 140,
  },
  {
    title: "参考链接",
    dataIndex: "referenceLink",
    width: 100,
    render: ({ record }) => {
      return record.referenceLink
        ? h("a", { href: record.referenceLink, target: "_blank" }, "查看链接")
        : "无";
    },
  },
  {
    title: "询价备注",
    dataIndex: "remark",
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "单位",
    dataIndex: "unit",
    width: 80,
  },
  {
    title: "需求数量",
    dataIndex: "quantity",
    width: 90,
    align: "right",
  },
  {
    title: "参考价格",
    dataIndex: "referencePrice",
    width: 100,
    align: "right",
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    width: 100,
  },
  {
    title: "客户产品编码",
    dataIndex: "customerProductCode",
    width: 130,
  },
  {
    title: "询价状态",
    dataIndex: "status",
    width: 100,
    align: "center",
    fixed: "right",
    render: ({ record }) => {
      const statusMap = {
        pending: { text: "未询价", color: "orange" },
        processing: { text: "处理中", color: "blue" },
        completed: { text: "已完成", color: "green" },
        cancelled: { text: "已取消", color: "red" },
      };
      const status = statusMap[record.status] || {
        text: record.status,
        color: "default",
      };
      return h("a-tag", { color: status.color }, status.text);
    },
  },
];

// 获取文件图标源
const getFileIconSrc = (fileName) => {
  const extension = fileName.split(".").pop().toLowerCase();

  // 根据文件类型返回不同的图标
  switch (extension) {
    case "pdf":
      return "/assets/icons/pdf.svg";
    case "xlsx":
    case "xls":
      return "/assets/icons/excel.svg";
    case "doc":
    case "docx":
      return "/assets/icons/word.svg";
    case "ppt":
    case "pptx":
      return "/assets/icons/powerpoint.svg";
    case "jpg":
    case "jpeg":
    case "png":
    case "gif":
      return "/assets/icons/image.svg";
    case "zip":
    case "rar":
      return "/assets/icons/archive.svg";
    case "dwg":
      return "/assets/icons/cad.svg";
    default:
      return "/assets/icons/file.svg";
  }
};

// 附件数据
const attachments = ref([
  {
    name: "产品询价单.xlsx",
    url: "https://example.com/files/产品询价单.xlsx",
    size: "1.2MB",
  },
  {
    name: "技术规格书.pdf",
    url: "https://example.com/files/技术规格书.pdf",
    size: "3.5MB",
  },
  {
    name: "参考设计图.dwg",
    url: "https://example.com/files/参考设计图.dwg",
    size: "5.8MB",
  },
]);

// 表格数据（响应式）
const tableData = ref([]);

// 页面加载时获取数据
onMounted(() => {
  fetchInquiryDetail();
});

// 询价结果表格列定义
const resultColumns = [
  {
    title: "序号",
    slotName: "index",
    width: 70,
    render: ({ rowIndex }) => rowIndex + 1,
  },
  {
    title: "实际产品名称",
    dataIndex: "actualProductName",
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "实际品牌",
    dataIndex: "actualBrand",
    width: 100,
  },
  {
    title: "实际型号",
    dataIndex: "actualModel",
    width: 120,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "实际规格描述",
    dataIndex: "actualSpecification",
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "包装量",
    dataIndex: "packageQuantity",
    width: 80,
    align: "right",
  },
  {
    title: "询价产品编码",
    dataIndex: "inquiryProductCode",
    width: 140,
  },
  {
    title: "产品编码",
    dataIndex: "productCode",
    width: 120,
  },
  {
    title: "成本价",
    dataIndex: "costPrice",
    width: 100,
    align: "right",
  },
  {
    title: "询价单位",
    dataIndex: "inquiryUnit",
    width: 80,
  },
  {
    title: "含税运情况",
    dataIndex: "taxAndShipping",
    width: 120,
  },
  {
    title: "运费",
    dataIndex: "shippingCost",
    width: 80,
    align: "right",
  },
  {
    title: "货期",
    dataIndex: "deliveryTime",
    width: 100,
  },
  {
    title: "附加费",
    dataIndex: "additionalFee",
    width: 80,
    align: "right",
  },
  {
    title: "销售价",
    dataIndex: "sellingPrice",
    width: 100,
    align: "right",
  },
  {
    title: "实际询价备注",
    dataIndex: "actualInquiryRemark",
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "供应商",
    dataIndex: "supplier",
    width: 120,
  },
  {
    title: "上架资料链接",
    dataIndex: "shelfInfoLink",
    width: 120,
    render: ({ record }) => {
      return record.shelfInfoLink
        ? h("a", { href: record.shelfInfoLink, target: "_blank" }, "查看链接")
        : "无";
    },
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    width: 100,
  },
  {
    title: "询价状态",
    dataIndex: "inquiryStatus",
    width: 100,
    align: "center",
    render: ({ record }) => {
      const statusMap = {
        pending: { text: "待处理", color: "orange" },
        processing: { text: "处理中", color: "blue" },
        completed: { text: "已完成", color: "green" },
        cancelled: { text: "已取消", color: "red" },
      };
      const status = statusMap[record.inquiryStatus] || {
        text: record.inquiryStatus,
        color: "default",
      };
      return h("a-tag", { color: status.color }, status.text);
    },
  },
  {
    title: "上架状态",
    dataIndex: "shelfStatus",
    width: 100,
    align: "center",
    render: ({ record }) => {
      const statusMap = {
        pending: { text: "待上架", color: "orange" },
        processing: { text: "上架中", color: "blue" },
        completed: { text: "已上架", color: "green" },
        rejected: { text: "已拒绝", color: "red" },
      };
      const status = statusMap[record.shelfStatus] || {
        text: record.shelfStatus,
        color: "default",
      };
      return h("a-tag", { color: status.color }, status.text);
    },
  },
  {
    title: "商品编码",
    dataIndex: "goodsCode",
    width: 120,
  },
  {
    title: "操作",
    dataIndex: "operations",
    width: 550,
    fixed: "right",
    slotName: "operations",
  },
];

// 询价结果表格数据
const resultData = ref([]);
// 操作函数
const handleEdit = (record, type = "edit") => {
  console.log(`${type}操作`, record);
  // 打开询价表单弹窗并传递当前记录数据和操作类型
  inquiryFormModalRef.value.open(record, type);
};

const handleViewAudit = (record) => {
  // 获取当前记录信息
  const auditData = {
    id: record.id,
    productName: record.actualProductName,
  };

  // 通过 ref 调用组件的 open 方法
  auditRecordRef.value.open(auditData);
};

const handleViewReasonsForInquiry = (record) => {
  reasonsForInquiryRef.value.open(record);
};

const openQuotationDownloadModal = () => {
  // 通过 ref 调用组件的 open 方法
  quotationDownloadRef.value.open();
};

const openInquiryAuditModal = () => {
  // 通过 ref 调用组件的 open 方法
  inquiryAuditRef.value.open();
};

const openInquiryImportModal = () => {
  // 通过 ref 调用组件的 open 方法
  inquiryImportRef.value.open();
};

// 处理询价结果导入成功
const handleInquiryImportSuccess = (importedData) => {
  console.log("导入的询价结果数据:", importedData);

  // 将导入的数据转换为询价结果表格的格式
  const newResultData = importedData.map((item, index) => ({
    id: `imported_${Date.now()}_${index}`,
    actualProductName: item.实际产品名称,
    actualBrand: item.实际品牌,
    actualModel: item.实际型号,
    actualSpecification: item.实际规格描述,
    packageQuantity: item.包装量,
    inquiryProductCode: item.询价产品编码,
    productCode: item.产品编码,
    costPrice: item.成本价,
    inquiryUnit: item.询价单位,
    taxAndShipping: item.含税运情况,
    shippingCost: item.运费,
    deliveryTime: item.货期,
    additionalFee: item.附加费,
    sellingPrice: item.销售价,
    actualInquiryRemark: item.实际询价备注,
    supplier: item.供应商,
    shelfInfoLink: item.上架资料链接 || "",
    inquirer: "系统导入",
    inquiryStatus: "completed",
    shelfStatus: "pending",
    goodsCode: "",
  }));

  // 将新数据添加到现有的询价结果中
  resultData.value = [...resultData.value, ...newResultData];

  // 更新供应商汇总信息
  updateSupplierSummary();

  Message.success(`成功导入 ${importedData.length} 条询价结果`);
};

// 更新供应商汇总信息（过滤重复供应商）
const updateSupplierSummary = () => {
  const supplierMap = new Map();

  // 遍历询价结果数据，按供应商分组
  resultData.value.forEach((item) => {
    const supplier = item.supplier;
    if (!supplier) return;

    if (!supplierMap.has(supplier)) {
      supplierMap.set(supplier, {
        id: `supplier_${supplier}`,
        supplier: supplier,
        totalQuantity: 0,
        shippingCost: 0,
        totalShippingCost: 0,
      });
    }

    const supplierData = supplierMap.get(supplier);
    const quantity = parseFloat(item.packageQuantity) || 0;
    const shippingCost = parseFloat(item.shippingCost) || 0;

    supplierData.totalQuantity += quantity;
    // 使用最新的运费（可以根据业务需求调整逻辑）
    supplierData.shippingCost = shippingCost;
    supplierData.totalShippingCost = (
      supplierData.shippingCost * supplierData.totalQuantity
    ).toFixed(2);
  });

  // 更新供应商汇总数据
  summaryData.value = Array.from(supplierMap.values());
};

const handleDelete = (record) => {
  // 删除操作逻辑
  console.log("删除记录:", record);
};

// 供应商汇总信息表格列定义
const summaryColumns = [
  {
    title: "供应商",
    dataIndex: "supplier",
    width: 150,
  },
  {
    title: "产品总数量",
    dataIndex: "totalQuantity",
    width: 100,
    align: "right",
  },
  {
    title: "运费",
    dataIndex: "shippingCost",
    width: 100,
    align: "right",
    editable: true,
    render: ({ record }) => {
      return h(AInputNumber, {
        modelValue: record.shippingCost,
        "onUpdate:modelValue": (value) => {
          record.shippingCost = value;
          handleShippingCostChange(record);
        },
        mode: "button",
        size: "small",
        min: 0,
        precision: 2,
      });
    },
  },
  // {
  //     title: '供应商总运费',
  //     dataIndex: 'totalShippingCost',
  //     width: 120,
  //     align: 'right',
  // },
];

// 供应商汇总信息表格数据
const summaryData = ref([]);

// 更新总运费计算
const updateTotalShippingCost = () => {
  summaryData.value.forEach((item) => {
    item.totalShippingCost = (item.shippingCost * item.totalQuantity).toFixed(
      2
    );
  });
};

// 处理运费变更
const handleShippingCostChange = (record) => {
  record.totalShippingCost = (
    record.shippingCost * record.totalQuantity
  ).toFixed(2);
  // 这里可以添加保存到后端的逻辑
  Message.success("运费已更新");
};
</script>

<style scoped>
/* 自定义样式 */
</style>
