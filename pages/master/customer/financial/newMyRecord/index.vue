<template>
  <div class="ma-content-block p-4">
     <!-- 状态卡片 -->
    <div class="stats-cards-container">
      <div
        class="stats-scroll-btn stats-scroll-left"
        @click="scrollStatsCards('left')"
      >
        <icon-left />
      </div>
      <div class="stats-cards" ref="statsCardsRef">
        <div
          v-for="card in statusCards"
          :key="card.key"
          class="stats-card"
          :class="{ active: currentStatusCard === card.key }"
          @click="handleStatusCardClick(card.key)"
        >
          <div class="stats-corner-mark" v-if="currentStatusCard === card.key">
            <icon-check class="check-icon" />
          </div>
          <div class="stats-title">{{ card.title }}</div>
          <div class="stats-value">{{ card.count }}</div>
        </div>
      </div>
      <div
        class="stats-scroll-btn stats-scroll-right"
        @click="scrollStatsCards('right')"
      >
        <icon-right />
      </div>
    </div>
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 关联订单 -->
      <template #relatedOrders="{ record }">
        <div v-if="record.relatedOrders && record.relatedOrders.length > 0">
          <a-tag v-for="order in record.relatedOrders.slice(0, 2)" :key="order" size="small">
            {{ order }}
          </a-tag>
          <a-tag v-if="record.relatedOrders.length > 2" size="small">
            +{{ record.relatedOrders.length - 2 }}
          </a-tag>
        </div>
        <span v-else>-</span>
      </template>

      <!-- 金额 -->
      <template #amount="{ record }">
        ¥{{ record.amount.toLocaleString() }}
      </template>

      <!-- 提交日期 -->
      <template #submitDate="{ record }">
        {{ formatDate(record.submitDate) }}
      </template>

      <!-- 审核日期 -->
      <template #auditDate="{ record }">
        {{ formatDate(record.auditDate) || '-' }}
      </template>

      <!-- 状态 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ record.status }}
        </a-tag> 
      </template>

      <!-- 操作列 -->
      <template #operationAfterExtend="{ record }">
        <a-button type="text" size="small" @click="handleViewDetail(record)">
          <template #icon><icon-eye /></template>
          查看
        </a-button>
      </template>
    </ma-crud>

    <!-- 审核记录 -->
    <div class="audit-records-section">
      <a-card title="驳回记录" :bordered="false">
        <div class="audit-record-subtitle">查看最新的审核申请</div>

        <div class="audit-record-item">
          <div class="record-header">
            <span class="record-id">APP-2024-003</span>
            <a-tag color="red">已驳回</a-tag>
          </div>
          <div class="record-details">
            <span class="record-info">审核人：财务部-李主管 | 审核日期：2024-01-15</span>
          </div>
          <div class="record-reason">
            <span class="reason-label">驳回原因：</span>
            <span class="reason-text">付款凭证不清晰，请重新上传</span>
          </div>
          <div class="record-actions">
            <a-button type="text" size="small">
              重新申请
            </a-button>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 详情弹窗 -->
    <RecordDetailModal ref="recordDetailModalRef" @success="handleModalSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import RecordDetailModal from "./components/RecordDetailModal.vue";
import paymentVoucherApi from "~/api/finance/paymentVoucher";
// 状态卡片数据（不调用统计）
const currentStatusCard = ref("all");
const statusCards = computed(() => {
  return [
    { key: "all", title: "全部", count: 0 },
    { key: "pending_audit", title: "待审核", count: 0 },
    { key: "rejected", title: "审核通过", count: 0 },
      { key: "rejected", title: "审核驳回", count: 0 },
  ];
});
// 定义页面路由元信息
definePageMeta({
  name: "customer-financial-newMyRecord",
  path: "/master/customer/financial/newMyRecord",
});

// 响应式数据
const crudRef = ref();
const recordDetailModalRef = ref();

// CRUD 配置
const crud = reactive({
  api: paymentVoucherApi.getPaymentRecognitionApplication,
  showIndex: false,
  pageLayout: "fixed",
  operationColumn: true,
  operationColumnWidth: 120,
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
});

// 表格列定义
const columns = reactive([
  {
    title: '申请编号',
    dataIndex: 'applicationNo',
    width: 120,
    search: true,
    slotName: 'applicationNo'
  },
  {
    title: '关联订单',
    dataIndex: 'relatedOrders',
    width: 150,
    slotName: 'relatedOrders',
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 120,
    slotName: 'amount',
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '提交日期',
    dataIndex: 'submitDate',
    width: 120,
    slotName: 'submitDate',
    search: true,
    formType: 'range',
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '审核日期',
    dataIndex: 'auditDate',
    width: 120,
    slotName: 'auditDate',
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '审核人',
    dataIndex: 'auditor',
    width: 120,
    search: true,
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status',
    search: true,
    formType: 'select',
    dict: {
      data: [
        { label: '待审核', value: '待审核' },
        { label: '已通过', value: '已通过' },
        { label: '已驳回', value: '已驳回' }
      ]
    },
    addDisplay: false,
    editDisplay: false
  }
]);

// 处理状态卡片点击
const handleStatusCardClick = (key) => {
  currentStatusCard.value = key;

  // 根据选中的状态卡片过滤数据
  if (crudRef.value) {
    if (key === "all") {
      crudRef.value.search({ recognitionStatus: "" });
    } else {
      // 将状态key转换为对应的数字状态值
      const statusMap = {
        pending_audit: RecognitionStatus.PENDING_AUDIT,
        approved: RecognitionStatus.AUDIT_APPROVED,
        rejected: RecognitionStatus.AUDIT_REJECTED,
        exception: 4, // 如果有异常状态的话
      };
      crudRef.value.search({ recognitionStatus: statusMap[key] });
    }
  }
};
// 方法
// 格式化日期
const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('zh-CN');
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '待审核': '#faad14',
    '已通过': '#52c41a',
    '已驳回': '#ff4d4f'
  };
  return colorMap[status] || '#d9d9d9';
};

// 查看详情
const handleViewDetail = (record) => {
  recordDetailModalRef.value?.open(record);
};

// 处理弹窗成功
const handleModalSuccess = (record) => {
  Message.success('操作成功');

  // 刷新表格数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.subtitle {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.amount-text {
  font-weight: 600;
  color: #1890ff;
}

.audit-records-section {
  margin-top: 24px;
}

.audit-record-subtitle {
  color: #8c8c8c;
  font-size: 12px;
  margin-bottom: 16px;
}

.audit-record-item {
  background: #FEF2F2;
  border-radius: 8px;
  padding: 16px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-id {
  font-weight: 600;
  font-size: 16px;
  color: #262626;
}

.record-details {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.record-reason {
  margin-bottom: 12px;
  font-size: 14px;
}

.reason-label {
  color: #666;
}

.reason-text {
  color: #262626;
}

.record-actions {
  text-align: right;
}

:deep(.arco-card-header) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.arco-table-th) {
  background-color: #f7f8fa;
}
/* 状态卡片样式 */
.stats-cards-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stats-cards {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  background-color: #fff;
  border-radius: 4px;
  flex: 1;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.stats-cards::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.stats-card {
  flex: 0 0 auto;
  width: 250px;
  margin: 5px;
  padding: 18px;
  border-radius: 4px;
  text-align: left;
  border: 1px solid #e5e6eb;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.stats-card:hover {
  border-color: #d9e1ff;
  background-color: #f9fafc;
}

.stats-card.active {
  border: 1px solid #165dff;
  background-color: #f0f6ff;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.stats-card.active .stats-title {
  color: #165dff;
  font-weight: 500;
}

.stats-card.active .stats-value {
  color: black;
  font-weight: 500;
}

.stats-corner-mark {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 24px 24px 0;
  border-color: transparent #165dff transparent transparent;
}

.check-icon {
  position: absolute;
  top: 1px;
  right: -23px;
  color: white;
  font-size: 14px;
}

.stats-title {
  font-size: 12px;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 20px;
  font-weight: 500;
  color: #1d2129;
}

.stats-scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 1;
}

.stats-scroll-btn:hover {
  background-color: #f2f3f5;
}

.stats-scroll-btn.stats-scroll-left {
  margin-right: 8px;
}

.stats-scroll-btn.stats-scroll-right {
  margin-left: 8px;
}

</style>