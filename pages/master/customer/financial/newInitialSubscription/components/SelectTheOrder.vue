<template>
    <a-modal :visible="visible" title="选择订单" :width="1200" :mask-closable="false" :esc-to-close="false"
        @cancel="handleCancel" @close="visible = false">
        <div class="system-payment-record-dialog">
            <!-- 搜索条件 -->
            <div class="search-form">
                <a-form :model="searchForm" layout="inline">
                    <a-form-item label="订单ID">
                        <a-input v-model="searchForm.orderId" placeholder="请输入订单ID" style="width: 213px" />
                    </a-form-item>
                    <a-form-item label="订单编号">
                        <a-input v-model="searchForm.thirdPartyOrderSn" placeholder="请输入订单编号" style="width: 213px" />
                    </a-form-item>
                    <a-form-item label="收货人姓名">
                        <a-input v-model="searchForm.recipientName" placeholder="请输入收货人姓名" style="width: 213px" />
                    </a-form-item>

                    <a-form-item label="时间范围">
                        <a-range-picker v-model="searchForm.dateRange" @change="onChange" @select="onSelect" style="width: 300px; marginBottom: 20px;" />
                    </a-form-item>
                     <a-form-item label="金额范围">
                        <a-input v-model="searchForm.recipientName" style="width: 100px" />-
                         <a-input v-model="searchForm.recipientName" style="width: 100px" />
                    </a-form-item>

                    <a-form-item>
                        <a-space>
                            <a-button type="primary" @click="handleSearch">查询</a-button>
                            <a-button @click="handleReset">重置</a-button>
                        </a-space>
                    </a-form-item>
                </a-form>
            </div>

            <!-- 收款记录表格 -->
            <a-table :columns="columns" :data="tableData" :pagination="pagination" :loading="loading" row-key="id"
                :row-selection="rowSelection" v-model:selectedKeys="selectedKeys" bordered @page-change="handlePageChange"
                @page-size-change="handlePageSizeChange">
                <template #paymentDate="{ record }">
                    {{ formatDateTime(record.orderCreatedAt) }}
                </template>

                <template #amount="{ record }">
                    ¥{{ Number(record.totalAmount || 0).toFixed(2) }}
                </template>

                <template #status="{ record }">
                    <a-tag :color="getOrderStatusColor(record.orderStatusText)">
                        {{ record.orderStatusText }}
                    </a-tag>
                </template>
            </a-table>
        </div>

        <template #footer>
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" :disabled="totalSelectedCount === 0" @click="handleConfirm">
                    确认选择 ({{ totalSelectedCount }})
                </a-button>
            </a-space>
        </template>
    </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import paymentVoucherApi from '~/api/finance/paymentVoucher'

// 定义事件
const emit = defineEmits(['success', 'cancel'])

// 弹窗显示状态
const visible = ref(false)
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
    orderId: '',
    thirdPartyOrderSn: '',
    recipientName: '',
    dateRange: null,
    sortField: 'assignedAt',
    sortOrder: 'desc'
})

// 选中的行 - 按照示例的方式
const selectedKeys = ref([])

// 存储所有已选择的订单数据（跨分页保持）
const selectedOrdersData = ref([])

// 计算总的选择数量（包含当前页面的选择）
const totalSelectedCount = computed(() => {
    // 先保存当前页面的选择状态（但不修改 selectedOrdersData）
    const currentSelectedData = tableData.value.filter(item => selectedKeys.value.includes(item.id))

    // 计算所有已保存的选择 + 当前页面的新选择（去重）
    const allSelectedIds = new Set([
        ...selectedOrdersData.value.map(item => item.id),
        ...currentSelectedData.map(item => item.id)
    ])

    return allSelectedIds.size
})

// 表格列定义
const columns = ref([
    {
        title: '订单编号',
        dataIndex: 'orderId',
        key: 'orderId',
        width: 180
    },
    {
        title: '收货人',
        dataIndex: 'recipientName',
        key: 'recipientName',
        width: 150
    },
    {
        title: '订单金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        width: 120,
        slotName: 'amount',
    },
    {
        title: '订单创建时间',
        dataIndex: 'orderCreatedAt',
        key: 'orderCreatedAt',
        width: 160,
        slotName: 'paymentDate'
    },
    {
        title: '渠道名称',
        dataIndex: 'channelName',
        key: 'channelName',
        width: 100
    },
    {
        title: '订单状态',
        dataIndex: 'orderStatusText',
        key: 'orderStatusText',
        width: 100,
        slotName: 'status'
    },
])

// 表格数据
const tableData = ref([])

// 分页配置
const pagination = reactive({
    current: 1,
    pageSize: 1,
    total: 0,
    showTotal: true,
    showPageSize: true
})

// 行选择配置 - 完全按照示例
const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
})

// 打开弹窗
const open = (preSelectedOrders = []) => {
    console.log('SystemPaymentRecordDialog open 方法被调用，预选订单:', preSelectedOrders)

    // 如果有预选订单，不清空选择数据
    const hasPreSelected = preSelectedOrders && preSelectedOrders.length > 0
    resetForm(!hasPreSelected)

    // 设置预选的订单数据
    if (hasPreSelected) {
        selectedOrdersData.value = [...preSelectedOrders]
        console.log('设置预选订单数据:', selectedOrdersData.value)
    }

    loadData()
    visible.value = true

    console.log('弹窗打开，visible:', visible.value)
    console.log('初始数据:', tableData.value)
    console.log('初始选中keys:', selectedKeys.value)
}

// 关闭弹窗
const handleCancel = () => {
    visible.value = false
    resetForm()
    emit("cancel")
}

// 确认选择
const handleConfirm = () => {
    // 先保存当前页面的选择状态
    saveCurrentPageSelection()

    // 获取所有页面的最终选择结果
    const finalSelectedData = [...selectedOrdersData.value]

    if (finalSelectedData.length === 0) {
        Message.warning('请选择订单')
        return
    }

    console.log('最终确认的所有订单:', finalSelectedData)

    // 返回最终选择的订单数据（包含用户的取消勾选操作）
    emit('success', finalSelectedData)
    visible.value = false
}

// 搜索
const handleSearch = () => {
    // 在搜索前保存当前页面的选择状态
    saveCurrentPageSelection()

    pagination.current = 1
    loadData()
}

// 重置
const handleReset = () => {
    // 在重置前保存当前页面的选择状态
    saveCurrentPageSelection()

    searchForm.orderId = ''
    searchForm.thirdPartyOrderSn = ''
    searchForm.recipientName = ''
    searchForm.dateRange = null
    pagination.current = 1
    loadData()
}

// 重置表单
const resetForm = (clearSelection = true) => {
    searchForm.orderId = ''
    searchForm.thirdPartyOrderSn = ''
    searchForm.recipientName = ''
    searchForm.dateRange = null
    selectedKeys.value = []

    // 只有在明确要求清空选择时才清空
    if (clearSelection) {
        selectedOrdersData.value = []
    }

    pagination.current = 1
}

// 加载数据
const loadData = async () => {
    loading.value = true
    try {
        // 构造查询参数
        const params = {
            page: pagination.current,
            pageSize: pagination.pageSize,
            sortField: searchForm.sortField,
            sortOrder: searchForm.sortOrder
        }

        // 添加可选的搜索条件
        if (searchForm.orderId) {
            params.orderId = searchForm.orderId
        }
        if (searchForm.thirdPartyOrderSn) {
            params.thirdPartyOrderSn = searchForm.thirdPartyOrderSn
        }
        if (searchForm.recipientName) {
            params.recipientName = searchForm.recipientName
        }

        // 处理日期范围 - 转换为毫秒时间戳
        if (searchForm.dateRange && searchForm.dateRange.length === 2) {
            // 确保日期格式正确转换
            const startDate = new Date(searchForm.dateRange[0])
            const endDate = new Date(searchForm.dateRange[1])

            // 设置开始时间为当天的00:00:00
            startDate.setHours(0, 0, 0, 0)
            params.startTime = startDate.getTime()

            // 设置结束时间为当天的23:59:59
            endDate.setHours(23, 59, 59, 999)
            params.endTime = endDate.getTime()

            console.log('日期范围:', {
                start: searchForm.dateRange[0],
                end: searchForm.dateRange[1],
                startTime: params.startTime,
                endTime: params.endTime
            })
        }

        console.log('请求参数:', params)

        // 调用接口获取数据
        const response = await paymentVoucherApi.getPendingRecognitionOrders(params)

        if (response.code === 200 && response.data) {
            tableData.value = response.data.items || []

            // 更新分页信息 - 根据实际返回的数据结构
            pagination.total = response.data.total || 0
            pagination.current = response.data.page || 1

            // 恢复当前页面的选择状态
            restoreCurrentPageSelection()

            console.log('获取到的订单数据:', tableData.value)
            console.log('分页信息:', { total: pagination.total, current: pagination.current })
        } else {
            Message.error(response.message || '获取订单数据失败')
            tableData.value = []
        }
    } catch (error) {
        console.error('获取订单数据失败:', error)
        Message.error('获取订单数据失败')
        tableData.value = []
    } finally {
        loading.value = false
    }
}

// 保存当前页面的选择状态
const saveCurrentPageSelection = () => {
    // 获取当前页面的所有数据ID
    const currentPageIds = tableData.value.map(item => item.id)

    // 获取当前页面选中的数据
    const currentSelectedData = tableData.value.filter(item => selectedKeys.value.includes(item.id))

    // 移除当前页面的所有数据（包括之前选中但现在取消的）
    selectedOrdersData.value = selectedOrdersData.value.filter(item =>
        !currentPageIds.includes(item.id)
    )

    // 添加当前页面新选中的数据
    selectedOrdersData.value.push(...currentSelectedData)

    console.log('当前页面所有数据ID:', currentPageIds)
    console.log('当前页面选中的数据:', currentSelectedData)
    console.log('保存后的所有选择:', selectedOrdersData.value)
}

// 恢复当前页面的选择状态
const restoreCurrentPageSelection = () => {
    // 找出当前页面中已经被选择的订单ID
    const currentPageSelectedIds = tableData.value
        .filter(item => selectedOrdersData.value.some(selected => selected.id === item.id))
        .map(item => item.id)

    selectedKeys.value = currentPageSelectedIds

    console.log('恢复当前页选择:', currentPageSelectedIds)
}

// 分页处理
const handlePageChange = (page) => {
    // 在切换页面前保存当前页面的选择状态
    saveCurrentPageSelection()

    pagination.current = page
    loadData()
}

const handlePageSizeChange = (pageSize) => {
    // 在改变页面大小前保存当前页面的选择状态
    saveCurrentPageSelection()

    pagination.pageSize = pageSize
    pagination.current = 1
    loadData()
}

// 格式化日期
const formatDate = (date) => {
    return date
}

// 格式化日期时间 - 将时间戳转换为可读格式
const formatDateTime = (timestamp) => {
    if (!timestamp) return '-'
    return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取状态颜色
const getStatusColor = (status) => {
    const colors = {
        completed: '#00b42a',  // 已完成 - 绿色
        pending: 'orange',     // 处理中 - 橙色
        failed: '#f53f3f'      // 失败 - 红色
    }
    return colors[status] || 'gray'
}

// 获取状态文本
const getStatusText = (status) => {
    const texts = {
        completed: '已完成',
        pending: '待确认',
        confirmed: '已确认',
        shipped: '已发货',
        cancelled: '已取消'
    }
    return texts[status] || '未知'
}
// 获取订单状态颜色
const getOrderStatusColor = (statusText) => {
    const colors = {
        '已发货': '#1890ff',    // 蓝色
        '已完成': '#00b42a',    // 绿色
        '待确认': 'orange',     // 橙色
        '已确认': '#722ed1',    // 紫色
        '已取消': '#ff4d4f'     // 红色
    }
    return colors[statusText] || '#d9d9d9'
}

// 日期选择器事件
const onChange = (dates, dateStrings) => {
    console.log('日期变化:', dates, dateStrings)
}

const onSelect = (dates, dateStrings) => {
    console.log('日期选择:', dates, dateStrings)
}

// 暴露方法给父组件
defineExpose({
    open
})
</script>

<style scoped>
.system-payment-record-dialog {
    padding: 16px 0;
}

.search-form {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 6px;
}

:deep(.arco-table-th) {
    background-color: #f7f8fa;
}
</style>
