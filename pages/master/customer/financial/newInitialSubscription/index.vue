<template>
  <div class="p-5 bg-white rounded">
    <!-- 发起认款申请标题 -->
    <div class="page-header">
      <h2>发起认款申请</h2>
      <p class="subtitle">选择订单并申请认款</p>
    </div>

    <!-- 选择订单 -->
    <div class="form-section">
      <div class="section-header">
        <h3 class="section-title">选择订单
          <div v-if="selectedOrders.length > 0" style="font-size: 12px;color: #8c8c8c;padding:5px 0px">
            已选择 {{ selectedOrders.length }} 个订单，总金额 ¥{{ selectedOrdersTotalAmount.toLocaleString() }}
          </div>
        </h3>
        <a-button type="primary" @click="handleSelectOrder">
          选择订单
        </a-button>
      </div>

      <!-- 已选择的订单表格 -->
      <a-table
        :columns="orderColumns"
        :data="selectedOrders"
        :pagination="false"
        :summary="orderSummary"
        bordered
        row-key="id"
      >
        <template #orderDate="{ record }">
          {{ formatDate(record.orderCreatedAt || record.orderDate) }}
        </template>

        <template #orderAmount="{ record }">
          {{ console.log('订单金额模板 - 记录:', record) }}
          ¥{{ Number(record.totalAmount || record.orderAmount || 0).toFixed(2) }}
        </template>

        <template #status="{ record }">
          <a-tag :color="getOrderStatusColor(record.orderStatusText)">
            {{ record.orderStatusText}}
          </a-tag>
        </template>

        <template #operation="{ record, rowIndex }">
          <a-button
            type="text"
            status="danger"
            size="small"
            @click="handleDeleteOrder(rowIndex)"
          >
            删除
          </a-button>
        </template>

        <template #summary-cell="{ column, record }">
          <div :style="getOrderSummaryStyle(column, record)">
            {{ formatOrderSummaryValue(column, record) }}
          </div>
        </template>
      </a-table>
    </div>

    <!-- 认款凭证 -->
    <div class="form-section">
      <h3 class="section-title">认款凭证</h3>
      <div class="voucher-note">
          选择凭证类型并上传相关文件（可多选）
        </div>
      <div class="voucher-section">
        <div class="voucher-type">
          <a-checkbox v-model="voucherForm.hasReceipt">
            付款凭证
          </a-checkbox>

          <!-- 选择付款凭证下拉框 -->
          <div v-if="voucherForm.hasReceipt" class="voucher-dropdown">
            <a-select
              v-model="voucherForm.selectedVoucherId"
              placeholder="请选择付款凭证"
              multiple
              filterable
              :loading="paymentVoucherLoading"
              :options="paymentVoucherOptions"
              style="width: 400px; margin-top: 12px;"
              allow-clear
              @search="handlePaymentVoucherSearch"
              @focus="handlePaymentVoucherFocus"
            />
            <div class="voucher-tips">
              <div class="tip-item">
                <span class="tip-icon">💡</span>
                <span>提示：可选择多个付款凭证进行认款</span>
              </div>
              <div class="tip-item">
                <span>• 请选择与本次认款相关的付款凭证</span>
              </div>
              <div class="tip-item">
                <span>• 确保选择的凭证金额与认款金额匹配</span>
              </div>
            </div>
          </div>
        </div>

        <div class="voucher-type">
          <a-checkbox v-model="voucherForm.hasContract">
            附件凭证
          </a-checkbox>

          <!-- 上传附件凭证 -->
          <div v-if="voucherForm.hasContract" class="voucher-upload">
            <div class="upload-section">
              <ma-upload
                v-model="voucherForm.attachments"
                type="file"
                :multiple="true"
                :draggable="true"
                :accept="'.pdf,.png,.jpg,.jpeg,.doc,.docx'"
                :size="10 * 1024 * 1024"
                style="margin-top: 12px;"
                :tip="'支持上传 pdf/png/jpg/jpeg/doc/docx 等格式文件，单个文件不超过 10MB'"
                return-type="url"
                title="点击上传附件凭证"
              >
                <template #default>
                  <div class="upload-area">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                      <div>点击上传文件 或将文件拖拽到此处</div>
                      <div class="upload-tips">
                        支持扩展名：.jpg、.jpeg、.png、.pdf、.doc、.docx，单个文件最大 10MB
                      </div>
                    </div>
                  </div>
                </template>
              </ma-upload>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 付款信息 -->
    <div class="form-section">
      <h3 class="section-title">付款信息</h3>
      <a-form :model="paymentForm" layout="vertical">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="付款金额" required>
              <a-input-number
                v-model="paymentForm.amount"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <!-- <div class="amount-note">最大可认款金额：¥50,000.00 (付款金额不能超过订单总金额)</div> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="付款日期" required>
              <a-date-picker
                v-model="paymentForm.paymentDate"
                placeholder="年/月/日"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="备注信息">
          <a-textarea
            v-model="paymentForm.description"
            placeholder="请输入备注信息（可选）"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <a-space>
        <a-button @click="handleCancel">
          取消申请
        </a-button>
        <a-button type="primary" @click="handleSubmit">
          提交申请
        </a-button>
      </a-space>
    </div>

    <!-- 选择订单弹窗 -->
    <SelectTheOrder ref="selectOrderDialogRef" @success="handleOrderSelectSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { Message } from "@arco-design/web-vue";
import SelectTheOrder from "./components/SelectTheOrder.vue";
import paymentVoucherApi from '~/api/finance/paymentVoucher';

// 定义页面路由元信息
definePageMeta({
  name: "customer-financial-newInitialSubscription",
  path: "/master/customer/financial/newInitialSubscription",
});

const router = useRouter();

// 弹窗引用
const selectOrderDialogRef = ref();

// 已选择的订单
const selectedOrders = ref([]);

// 付款凭证数据
const paymentVoucherOptions = ref([]);
const paymentVoucherLoading = ref(false);

// 页面加载状态
const loading = ref(false);

// 计算选择订单的总金额
const selectedOrdersTotalAmount = computed(() => {
  return selectedOrders.value.reduce((sum, order) => sum + (order.totalAmount || order.orderAmount || 0), 0);
});

// 认款凭证表单
const voucherForm = reactive({
  hasReceipt: false,
  hasContract: false,
  selectedVoucherId: [],
  attachments: []
});

// 付款信息表单
const paymentForm = reactive({
  amount: null,
  paymentDate: null,
  note: ''
});

// 订单表格列定义 - 与 SelectTheOrder.vue 保持一致
const orderColumns = ref([
  {
    title: '订单编号',
    dataIndex: 'orderId',
    width: 180
  },
  {
    title: '收货人',
    dataIndex: 'recipientName',
    width: 150
  },
  {
    title: '订单金额',
    dataIndex: 'totalAmount',
    slotName: 'orderAmount',
    width: 120
  },
  {
    title: '订单创建时间',
    dataIndex: 'orderCreatedAt',
    slotName: 'orderDate',
    width: 160
  },
  {
    title: '渠道名称',
    dataIndex: 'channelName',
    width: 100
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatusText',
    slotName: 'status',
    width: 100
  },
  {
    title: '操作',
    slotName: 'operation',
    width: 80,
    align: 'center'
  }
]);

// 订单汇总计算
const orderSummary = computed(() => {
  return (data) => {
    console.log('汇总函数被调用，接收到的数据:', data);
    console.log('selectedOrders 当前值:', selectedOrders.value);

    // 直接使用 selectedOrders 而不是传入的 data
    const actualData = selectedOrders.value;

    // 确保 actualData 是数组
    if (!Array.isArray(actualData) || actualData.length === 0) {
      console.log('没有订单数据，返回空汇总');
      return [
        {
          orderId: '订单汇总',
          recipientName: '',
          totalAmount: 0,
          orderCreatedAt: '',
          channelName: '',
          orderStatusText: '',
          operation: ''
        }
      ];
    }

    const totalAmount = actualData.reduce((sum, item) => {
      const amount = Number(item.totalAmount || item.orderAmount || 0);
      console.log('汇总计算 - 订单:', item.orderId || item.orderNo, '金额:', amount, '原始数据:', item);
      return sum + amount;
    }, 0);
    const totalCount = actualData.length;

    console.log('汇总计算结果 - 总金额:', totalAmount, '订单数量:', totalCount);

    const summaryRow = {
      orderId: '订单汇总',
      recipientName: '',
      totalAmount: totalAmount,
      orderCreatedAt: '',
      channelName: '',
      orderStatusText: '',
      operation: ''
    };

    console.log('返回的汇总行数据:', summaryRow);

    return [summaryRow];
  };
});

// 打开选择订单弹窗
const handleSelectOrder = () => {
  // 将已选择的订单数据传递给选择组件
  selectOrderDialogRef.value?.open(selectedOrders.value);
};

// 处理订单选择成功
const handleOrderSelectSuccess = (orders) => {
  // 直接替换选择的订单，而不是追加
  selectedOrders.value = [...orders];
  Message.success(`已选择 ${orders.length} 个订单`);
};

// 获取付款凭证列表
const getPaymentVoucherList = async (searchText = '') => {
  paymentVoucherLoading.value = true;
  try {
    const params = {
      page: 1,
      pageSize: 100, // 获取更多数据供选择
      status: 0, // 只获取未关联的凭证
    };

    // 如果有搜索文本，添加搜索参数
    if (searchText) {
      params.payerName = searchText;
    }

    console.log('获取付款凭证参数:', params);

    const response = await paymentVoucherApi.getList(params);

    if (response.code === 200 && response.data) {
      // 转换数据格式为下拉框需要的格式
      paymentVoucherOptions.value = response.data.items.map(item => ({
        label: `${item.voucherNumber} - ${item.payerName} - ¥${Number(item.amount).toLocaleString()}`,
        value: item.id,
        ...item // 保留原始数据
      }));

      console.log('付款凭证选项:', paymentVoucherOptions.value);
    } else {
      Message.error(response.message || '获取付款凭证列表失败');
      paymentVoucherOptions.value = [];
    }
  } catch (error) {
    console.error('获取付款凭证列表失败:', error);
    Message.error('获取付款凭证列表失败');
    paymentVoucherOptions.value = [];
  } finally {
    paymentVoucherLoading.value = false;
  }
};

// 处理付款凭证搜索
const handlePaymentVoucherSearch = (searchText) => {
  console.log('搜索付款凭证:', searchText);
  getPaymentVoucherList(searchText);
};

// 处理付款凭证下拉框获得焦点
const handlePaymentVoucherFocus = () => {
  // 如果还没有加载过数据，则加载
  if (paymentVoucherOptions.value.length === 0) {
    getPaymentVoucherList();
  }
};

// 删除订单
const handleDeleteOrder = (index) => {
  selectedOrders.value.splice(index, 1);
  Message.success('订单已删除');
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('zh-CN');
};

// 获取订单状态颜色 - 与 SelectTheOrder.vue 保持一致
const getOrderStatusColor = (statusText) => {
  const colors = {
    '已发货': '#1890ff',    // 蓝色
    '已完成': '#00b42a',    // 绿色
    '待确认': 'orange',     // 橙色
    '已确认': '#722ed1',    // 紫色
    '已取消': '#ff4d4f'     // 红色
  };
  return colors[statusText] || '#d9d9d9';
};



// 订单汇总样式
const getOrderSummaryStyle = (column, record) => {
  if (column.dataIndex === 'orderId' || column.dataIndex === 'recipientName') {
    return { fontWeight: 'bold' };
  }
  if (column.dataIndex === 'totalAmount') {
    return { fontWeight: 'bold', };
  }
  return {};
};

// 格式化订单汇总值
const formatOrderSummaryValue = (column, record) => {
  if (column.dataIndex === 'totalAmount') {
    return `¥${Number(record.totalAmount || 0).toFixed(2)}`;
  }
  return record[column.dataIndex] || '';
};

// 取消申请
const handleCancel = () => {
  router.back();
};

// 提交申请
const handleSubmit = async () => {
  // 验证表单
  if (selectedOrders.value.length === 0) {
    Message.error('请选择至少一个订单');
    return;
  }

  if (!voucherForm.hasReceipt && !voucherForm.hasContract) {
    Message.error('请选择至少一种认款凭证');
    return;
  }

  // 验证收款凭证
  if (voucherForm.hasReceipt && (!voucherForm.selectedVoucherId || voucherForm.selectedVoucherId.length === 0)) {
    Message.error('请选择付款凭证');
    return;
  }

  // 附件凭证
  if (voucherForm.hasContract && (!voucherForm.attachments || voucherForm.attachments.length === 0)) {
    Message.error('请上传附件凭证文件');
    return;
  }

  if (!paymentForm.amount) {
    Message.error('请输入付款金额');
    return;
  }

  if (!paymentForm.paymentDate) {
    Message.error('请选择付款日期');
    return;
  }

  // 验证付款金额不能超过订单总金额
  const totalOrderAmount = selectedOrders.value.reduce((sum, order) => sum + (order.totalAmount || order.orderAmount || 0), 0);
  if (paymentForm.amount > totalOrderAmount) {
    Message.error('付款金额不能超过订单总金额');
    return;
  }

  try {
    loading.value = true;

    // 构造请求参数
    const params = {
      // 订单ID列表（必需）
      orderIds: selectedOrders.value.map(order =>order.orderId),

      // 申请认款总金额（必需）
      totalAmount: Number(paymentForm.amount),

      // 付款日期（必需，转换为毫秒时间戳）
      paymentDate: new Date(paymentForm.paymentDate).getTime(),

      // 备注说明（可选）
      description: paymentForm.description || undefined,

      // 选择的付款凭证ID列表（可选）
      selectedVoucherIds: voucherForm.hasReceipt ? voucherForm.selectedVoucherId : undefined,

      // 附件凭证URL列表（可选）
      attachments: voucherForm.hasContract ? voucherForm.attachments : undefined
    };

    // 移除 undefined 值
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key];
      }
    });

    console.log('提交认款申请参数:', params);

    // 调用认款申请接口
    const response = await paymentVoucherApi.paymentRecognitionApplication(params);

    if (response.code === 200) {
      Message.success('认款申请提交成功');
      router.push('/master/customer/financial/newMyRecord');
    } else {
      Message.error(response.message || '提交失败，请重试');
    }
  } catch (error) {
    console.error('提交认款申请失败:', error);
    Message.error(error.message || '提交失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  console.log('页面初始化，加载付款凭证数据');
  // 初始化时不加载数据，等用户点击下拉框时再加载
  // getPaymentVoucherList();
});
</script>

<style scoped>


.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.subtitle {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.form-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.voucher-section {
  padding: 16px;

  border-radius: 6px;
}

.voucher-type {
  margin-bottom: 12px;
}

.voucher-note {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 8px;
}

.amount-note {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

.form-actions {
  text-align: center;
  padding: 24px;
}

.voucher-dropdown {
  margin-left: 24px;
}

.voucher-tips {
  margin-top: 8px;
  padding: 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  font-size: 12px;
  color: #52c41a;
}

.tip-item {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  margin-right: 4px;
}

.voucher-upload {
  margin-left: 24px;
}

.upload-section {
  margin-top: 12px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-text > div:first-child {
  font-size: 16px;
  color: #262626;
  margin-bottom: 8px;
}

.upload-tips {
  font-size: 12px;
  color: #8c8c8c;
}
</style>