<template>
  <div class="send-goods-page">
    <!-- 固定顶部日期和返回按钮 -->
    <a-affix :offset-top="0" class="fixed-header">
      <div class="header-bar">
        <a-button type="text" class="back-btn" @click="goBack">
          <icon-left />
        </a-button>
        <div class="date-header">
          {{ formatDate(new Date()) }}
        </div>
        <div class="placeholder"></div>
      </div>
    </a-affix>

    <!-- 订单信息 -->
    <div class="order-info">
      <div class="order-number">
        当前发货订单: {{ orderData.orderNo }}
      </div>
      <div v-if="orderData.originalOrderNumber || orderData.purchaseOrderNumber || orderData.splitOrderNumber" class="order-details">
        <div v-if="orderData.originalOrderNumber" class="order-detail-item">
          <span class="detail-label">原订单号:</span>
          <span class="detail-value">{{ orderData.originalOrderNumber }}</span>
        </div>
        <div v-if="orderData.purchaseOrderNumber" class="order-detail-item">
          <span class="detail-label">采购订单号:</span>
          <span class="detail-value">{{ orderData.purchaseOrderNumber }}</span>
        </div>
        <div v-if="orderData.splitOrderNumber" class="order-detail-item">
          <span class="detail-label">拆分订单号:</span>
          <span class="detail-value">{{ orderData.splitOrderNumber }}</span>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="content">
      <!-- 发货信息填写 -->
      <div class="section">
        <h3 class="section-title">发货信息填写:</h3>

        <!-- 配送方式 -->
        <div class="form-item">
          <div class="label-with-switch">
            <span class="label required">配送方式:</span>
            <div class="force-match-switch">
              <a-switch
                v-model="forceMatchMode"
                size="small"
                @change="onForceMatchChange"
              />
              <span class="switch-label">仅渠道专用</span>
            </div>
          </div>
          <div class="input-wrapper" @click="showDeliveryModal = true">
            <input
              type="text"
              :value="selectedDelivery"
              placeholder="请选择配送方式"
              readonly
              class="form-input"
            />
            <icon-right class="arrow-icon" />
          </div>
        </div>

        <!-- 配送公司 -->
        <div class="form-item">
          <span class="label required">配送公司:</span>
          <div class="input-wrapper" @click="showCompanyModal = true">
            <input
              type="text"
              :value="selectedCompany"
              placeholder="请选择配送公司"
              readonly
              class="form-input"
            />
            <icon-right class="arrow-icon" />
          </div>
        </div>

        <!-- 快递单号 -->
        <div class="form-item">
          <span class="label required">快递单号:</span>
          <div class="input-wrapper">
            <input
              type="text"
              v-model="formData.trackingNumber"
              placeholder="请填写快递单号"
              class="form-input"
            />
          </div>
        </div>

        <!-- 发货地址 -->
        <div class="form-item">
          <span class="label required">发货地址:</span>
          <div class="input-wrapper">
            <input
              type="text"
              v-model="formData.shippingLocation"
              placeholder="请填写发货地址"
              class="form-input"
            />
          </div>
        </div>

        <!-- 业务联系人 -->
        <div class="form-item">
          <span class="label required">业务联系人:</span>
          <div class="input-wrapper">
            <input
              type="text"
              v-model="formData.businessContact"
              placeholder="请填写业务联系人姓名"
              class="form-input"
            />
          </div>
        </div>

        <!-- 业务联系电话 -->
        <div class="form-item">
          <span class="label required">业务联系电话:</span>
          <div class="input-wrapper">
            <input
              type="tel"
              v-model="formData.businessPhone"
              placeholder="请填写业务联系电话"
              class="form-input"
            />
          </div>
        </div>

        <!-- 配送运单照片 -->
        <div class="form-item photo-item">
          <span class="label">配送运单照片:</span>
          <div class="upload-wrapper">
            <ma-upload
              v-model="formData.waybillPhoto"
              type="image"
              :multiple="false"
              :limit="1"
              accept=".jpg,.jpeg,.png"
              title="请拍照或选择照片"
              returnType="url"
            />
          </div>
        </div>

        <!-- 包裹货物照片 -->
        <div class="form-item photo-item">
          <span class="label">包裹货物照片:</span>
          <div class="upload-wrapper">
            <ma-upload
              v-model="formData.packagePhoto"
              type="image"
              :multiple="false"
              :limit="1"
              accept=".jpg,.jpeg,.png"
              title="请拍照或选择照片"
              returnType="url"
            />
          </div>
        </div>
       <div style="font-size: 12px; padding: 10px 10px; color: #999;">
         说明：仅支持jpg、png格式，单张大小不超过1M,若为厂直发货，
        需一并上传物流单，若不上传物流单，审核可能不通过，发货不成功!
       </div>
        <!-- 备注 -->
        <div class="form-item">
          <span class="label">备注:</span>
          <div class="input-wrapper">
            <textarea
              v-model="formData.remark"
              placeholder="请填写备注"
              class="form-textarea"
              rows="3"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="bottom-actions">
        <a-button type="primary" size="large" block class="confirm-btn" @click="submitForm">
          提交
        </a-button>
      </div>
    </div>

    <!-- 配送方式选择弹窗 -->
    <a-modal
      :visible="showDeliveryModal"
      :footer="false"
      width="320px"
      @cancel="showDeliveryModal = false"
      @update:visible="showDeliveryModal = $event"
      class="delivery-modal"
    >
      <div class="delivery-options">
        <!-- 加载状态 -->
        <div v-if="loadingDeliveryMethods" class="loading-container">
          <a-spin size="small" />
          <span style="margin-left: 8px;">加载配送方式中...</span>
        </div>

        <!-- 配送方式列表 -->
        <div v-else>
          <div
            v-for="option in deliveryOptions"
            :key="option.value"
            class="delivery-option"
            @click="selectDelivery(option)"
          >
            <div>
              <span>{{ option.label }}</span>
              <div v-if="option.description" class="option-description">{{ option.description }}</div>
            </div>
            <icon-check v-if="selectedDelivery === option.label" class="check-icon" />
          </div>

          <!-- 无数据提示 -->
          <div v-if="deliveryOptions.length === 0" class="no-data">
            暂无可用的配送方式
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 配送公司选择弹窗 -->
    <a-modal
      :visible="showCompanyModal"
      :footer="false"
      width="320px"
      @cancel="showCompanyModal = false"
      @update:visible="showCompanyModal = $event"
      class="company-modal"
    >
      <template #title>
        <div class="modal-title">选择配送公司</div>
      </template>

      <!-- 搜索框 -->
      <div class="search-wrapper">
        <a-input
          v-model="searchKeyword"
          placeholder="搜索配送公司"
          allow-clear
          class="search-input"
        >
          <template #prefix>
            <icon-search />
          </template>
        </a-input>
      </div>

      <!-- 配送公司列表 -->
      <div class="company-options">
        <div
          v-for="company in filteredCompanyOptions"
          :key="company.value"
          class="company-option"
          @click="selectCompany(company)"
        >
          <span>{{ company.label }}</span>
          <icon-check v-if="selectedCompany === company.label" class="check-icon" />
        </div>

        <!-- 无搜索结果 -->
        <div v-if="filteredCompanyOptions.length === 0" class="no-results">
          暂无匹配的配送公司
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconLeft, IconRight, IconCheck, IconSearch } from '@arco-design/web-vue/es/icon'
import { shippingApi } from '@/api/master/csm/shipping.js'

definePageMeta({
  layout: 'false',
  name: 'h5-SupplyChain-SendGoods',
  path: '/h5/SupplyChain/SendGoods'
})

const router = useRouter()
const route = useRoute()

// 订单数据
const orderData = ref({
  orderNo: getOrderNumber(),
  originalOrderNumber: route.query.originalOrderNumber || null,
  purchaseOrderNumber: route.query.purchaseOrderNumber || null,
  splitOrderNumber: route.query.splitOrderNumber || null
})

// 获取订单号的优先级逻辑
function getOrderNumber() {
  // 优先级：拆分订单号 > 采购订单号 > 原订单号 > URL参数 > 默认值
  if (route.query.splitOrderNumber) {
    return route.query.splitOrderNumber
  }
  if (route.query.purchaseOrderNumber) {
    return route.query.purchaseOrderNumber
  }
  if (route.query.originalOrderNumber) {
    return route.query.originalOrderNumber
  }
  if (route.params.orderid) {
    return route.params.orderid
  }
  return '313300020216' // 默认值
}

// 表单数据
const formData = reactive({
  deliveryMethod: '',
  deliveryMethodId: '',
  deliveryMethodCode: '',
  trackingNumber: '',
  deliveryTime: '',
  shippingLocation: '',
  waybillPhoto: '',
  packagePhoto: '',
  businessContact: '',
  businessPhone: '',
  remark: ''
})

// 配送方式选择
const showDeliveryModal = ref(false)
const selectedDelivery = ref('')
const deliveryOptions = ref([])
const loadingDeliveryMethods = ref(false)
const forceMatchMode = ref(false) // 强制匹配模式

// 配送公司选择
const showCompanyModal = ref(false)
const selectedCompany = ref('')
const searchKeyword = ref('')

// 配送公司选项
const companyOptions = [
  { label: '顺丰速运', value: 'sf' },
  { label: '京东物流', value: 'jd' },
  { label: '圆通速递', value: 'yt' },
  { label: '中通快递', value: 'zt' },
  { label: '申通快递', value: 'st' },
  { label: '韵达速递', value: 'yd' },
  { label: '百世快递', value: 'bs' },
  { label: '德邦快递', value: 'db' },
  { label: '天天快递', value: 'tt' },
  { label: '宅急送', value: 'zjs' },
  { label: '邮政EMS', value: 'ems' },
  { label: '安能物流', value: 'an' },
  { label: '壹米滴答', value: 'ymdd' },
  { label: '优速快递', value: 'ys' },
  { label: '国通快递', value: 'gt' }
]

// 过滤后的配送公司列表
const filteredCompanyOptions = computed(() => {
  if (!searchKeyword.value) {
    return companyOptions
  }
  return companyOptions.filter(company =>
    company.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 格式化日期
const formatDate = (date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

// 获取配送方式列表
const getDeliveryMethods = async (forceMatch = false) => {
  if (!orderData.value.orderNo) {
    Message.error('订单号不能为空')
    return
  }

  try {
    loadingDeliveryMethods.value = true

    // 构建查询参数
    const params = {}
    if (forceMatch) {
      params.forceMatch = true
    }

    const response = await shippingApi.getDeliveryMethodsByOrderNo(orderData.value.orderNo, params)

    if (response.code === 200 && response.data) {
      // 转换API返回的数据格式为组件需要的格式
      deliveryOptions.value = response.data.deliveryMethods.map(method => ({
        label: method.methodName,
        value: method.id,
        code: method.methodCode,
        description: method.description,
        isChannelSpecific: method.isChannelSpecific
      }))

      // 更新强制匹配模式状态
      forceMatchMode.value = response.data.forceMatch || false

      console.log('获取配送方式成功:', {
        orderNo: response.data.orderNo,
        channelId: response.data.channelId,
        forceMatch: response.data.forceMatch,
        count: deliveryOptions.value.length,
        methods: deliveryOptions.value
      })
    } else {
      Message.error(response.message || '获取配送方式失败')
    }
  } catch (error) {
    console.error('获取配送方式失败:', error)
    Message.error('获取配送方式失败，请稍后重试')

    // 如果API调用失败，使用默认的配送方式
    deliveryOptions.value = [
      { label: '京东大件物流', value: 'jd_large', code: 'JD_LARGE' },
      { label: '京东快运', value: 'jd_express', code: 'JD_EXPRESS' },
      { label: '京东快递', value: 'jd_courier', code: 'JD_COURIER' },
      { label: '德邦物流', value: 'deppon', code: 'DEPPON' },
      { label: '德邦快递', value: 'deppon_express', code: 'DEPPON_EXPRESS' },
      { label: '德邦快运', value: 'deppon_freight', code: 'DEPPON_FREIGHT' },
      { label: '百世快递', value: 'best_express', code: 'BEST_EXPRESS' },
      { label: '其他', value: 'other', code: 'OTHER' }
    ]
  } finally {
    loadingDeliveryMethods.value = false
  }
}

// 强制匹配模式切换处理
const onForceMatchChange = (value) => {
  console.log('强制匹配模式切换:', value)
  // 重新获取配送方式列表
  getDeliveryMethods(value)
  // 清空已选择的配送方式
  selectedDelivery.value = ''
  formData.deliveryMethod = ''
  formData.deliveryMethodId = ''
  formData.deliveryMethodCode = ''
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 选择配送方式
const selectDelivery = (option) => {
  selectedDelivery.value = option.label
  formData.deliveryMethod = option.value
  formData.deliveryMethodId = option.value
  formData.deliveryMethodCode = option.code
  showDeliveryModal.value = false
}

// 选择配送公司
const selectCompany = (company) => {
  selectedCompany.value = company.label
  formData.deliveryCompany = company.value
  showCompanyModal.value = false
  searchKeyword.value = '' // 清空搜索关键词
}

// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!formData.deliveryMethodId) {
    Message.error('请选择配送方式')
    return
  }
  if (!formData.trackingNumber) {
    Message.error('请填写快递单号')
    return
  }
  if (!formData.shippingLocation) {
    Message.error('请填写发货地址')
    return
  }
  if (!formData.businessContact) {
    Message.error('请填写业务联系人')
    return
  }
  if (!formData.businessPhone) {
    Message.error('请填写业务联系电话')
    return
  }

  try {
    // 构建提交数据
    const submitData = {
      orderNo: orderData.value.orderNo,
      orderType: 1, // 采购订单
      deliveryMethodId: formData.deliveryMethodId,
      expressCompanyName: selectedCompany.value || '其他',
      expressCompanyId: formData.deliveryMethodCode || 'OTHER',
      trackingNo: formData.trackingNumber,
      shippingLocation: formData.shippingLocation,
      attachment: null,
      deliveryListPhoto: formData.waybillPhoto || null,
      packagePhoto: formData.packagePhoto || null,
      businessContact: formData.businessContact,
      businessPhone: formData.businessPhone,
      remarks: formData.remark || null
    }

    console.log('提交的表单数据:', submitData)

    // 调用API提交数据
    const response = await shippingApi.createShippingInfo(submitData)

    if (response.code === 200) {
      Message.success('发货信息提交成功')
      // 可以跳转到成功页面或返回上一页
      setTimeout(() => {
        router.back()
      }, 1500)
    } else {
      Message.error(response.message || '提交失败')
    }
  } catch (error) {
    console.error('提交发货信息失败:', error)
    Message.error('提交失败，请稍后重试')
  }
}

// 组件挂载时初始化
onMounted(() => {
  console.log('SendGoods组件挂载')
  console.log('路由参数:', route.params)
  console.log('查询参数:', route.query)
  console.log('订单数据:', orderData.value)
  console.log('使用的订单号:', orderData.value.orderNo)

  // 获取配送方式列表
  getDeliveryMethods()
})
</script>

<style lang="less">
.send-goods-page {
  background: #fff;
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden;
  touch-action: manipulation;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;

  // 固定头部样式
  .fixed-header {
    z-index: 100;

    .header-bar {
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;

      .back-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        font-size: 18px;
        color: #333;
      }

      .date-header {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        flex: 1;
        text-align: center;
      }

      .placeholder {
        width: 32px;
      }
    }
  }

  // 订单信息
  .order-info {
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e8e8e8;

    .order-number {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      text-align: center;
      margin-bottom: 12px;
    }

    .order-details {
      .order-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        font-size: 12px;

        .detail-label {
          color: #666;
          min-width: 80px;
        }

        .detail-value {
          color: #333;
          font-weight: 500;
          flex: 1;
          text-align: right;
        }
      }
    }
  }

  // 页面内容
  .content {
    padding: 0 16px 80px;

    // 区块样式
    .section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    // 表单项样式
    .form-item {
      margin-bottom: 16px;

      .label {
        display: block;
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4px;
        }
      }

      .label-with-switch {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .force-match-switch {
          display: flex;
          align-items: center;
          gap: 6px;

          .switch-label {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .input-wrapper {
        position: relative;

        .form-input {
          width: 100%;
          height: 40px;
          padding: 0 12px;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          font-size: 14px;
          background: #fff;

          &::placeholder {
            color: #bfbfbf;
          }

          &:focus {
            border-color: #1890ff;
            outline: none;
          }

          &[readonly] {
            background: #f5f5f5;
            cursor: pointer;
          }
        }

        .form-textarea {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          font-size: 14px;
          resize: vertical;
          min-height: 80px;

          &::placeholder {
            color: #bfbfbf;
          }

          &:focus {
            border-color: #1890ff;
            outline: none;
          }
        }

        .arrow-icon {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: #bfbfbf;
          font-size: 12px;
        }
      }
    }

    // 照片上传样式
    .photo-item {
      .upload-wrapper {
        :deep(.ma-upload) {
          .arco-upload-list {
            margin-top: 8px;
          }

          .arco-upload-list-item {
            border-radius: 6px;
          }

          .arco-upload-trigger {
            width: 100px;
            height: 100px;
            border-radius: 6px;
            border: 1px dashed #d9d9d9;
            background: #fafafa;

            &:hover {
              border-color: #1890ff;
            }
          }
        }
      }
    }

    // 底部按钮
    .bottom-actions {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0;
      background: #1989FA;
      z-index: 99;

      .confirm-btn {
        height: 60px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 0;
        background: #1989FA;
        border: none;
        color: #fff;
        width: 100%;

        &:hover {
          background: #0F7AE5;
        }

        &:focus {
          background: #1989FA;
        }
      }
    }
  }
}

// 配送方式弹窗样式
.delivery-modal {
  :deep(.arco-modal-header) {
    display: none;
  }

  :deep(.arco-modal-body) {
    padding: 0;
  }

  .delivery-options {
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #666;
      font-size: 14px;
    }

    .delivery-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:hover {
        background: #f5f5f5;
      }

      &:last-child {
        border-bottom: none;
      }

      > div:first-child {
        flex: 1;
      }

      span {
        font-size: 14px;
        color: #333;
      }

      .option-description {
        font-size: 12px;
        color: #999;
        margin-top: 4px;
      }

      .check-icon {
        color: #1890ff;
        font-size: 16px;
      }
    }

    .no-data {
      padding: 40px 20px;
      text-align: center;
      color: #999;
      font-size: 14px;
    }
  }
}

// 配送公司弹窗样式
.company-modal {
  :deep(.arco-modal-header) {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;

    .modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  :deep(.arco-modal-body) {
    padding: 0;
    max-height: 400px;
    overflow: hidden;
  }

  .search-wrapper {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;

    .search-input {
      :deep(.arco-input) {
        border-radius: 20px;
        background: #f5f5f5;
        border: 1px solid #e8e8e8;

        &:focus {
          background: #fff;
          border-color: #1890ff;
        }
      }
    }
  }

  .company-options {
    max-height: 300px;
    overflow-y: auto;

    .company-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #f5f5f5;
      }

      &:last-child {
        border-bottom: none;
      }

      span {
        font-size: 14px;
        color: #333;
      }

      .check-icon {
        color: #1890ff;
        font-size: 16px;
      }
    }

    .no-results {
      padding: 40px 20px;
      text-align: center;
      color: #999;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (min-width: 500px) {
  .send-goods-page {
    margin: 0 auto;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;

    .content .bottom-actions {
      left: 50%;
      transform: translateX(-50%);
      max-width: 500px;
      width: 500px;
    }
  }
}
</style>