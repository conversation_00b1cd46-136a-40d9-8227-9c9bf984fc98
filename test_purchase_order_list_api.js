/**
 * 测试采购订单列表API中的货期时间字段
 * 验证 expected_delivery_time 字段是否正确返回
 */

// 模拟返回的采购订单列表数据结构
const mockPurchaseOrderListResponse = {
  code: 200,
  message: "获取采购订单列表成功",
  data: {
    items: [
      {
        id: 77,
        orderNumber: "PO202407090001",
        originalOrderNumber: "ORD202407090001",
        thirdPartyOrderSn: "TP202407090001",
        purchaseTime: "2024-07-09T10:00:00.000Z",
        orderTime: "2024-07-09T09:00:00.000Z",
        orderSource: "商城",
        storeName: "测试店铺",
        deliveryAddress: "北京市朝阳区测试地址",
        follower: "张三",
        purchaser: "李四",
        orderType: "商城自然单",
        products: [
          {
            id: 123,
            productImage: "https://example.com/product.jpg",
            productName: "测试商品",
            sku: "SKU001",
            productCode: "PROD001",
            specification: "规格1",
            taxCategory: "税收分类1",
            unitPrice: "100.00",
            quantity: 2,
            totalAmount: "200.00",
            actualCost: "80.00",
            inspectionCost: "5.00",
            profitRate: "15.0%",
            productLineExpireTime: "2024-12-31T23:59:59.000Z",
            actualSupplier: "供应商A",
            actualSupplierId: 456,
            suggestedSupplier: "供应商B",
            suggestedSupplierId: 789,
            purchaseProgress: "货期",
            costTotal: "85.00",
            lossReason: null,
            // 新增的商品项货期时间字段
            expectedDeliveryTime: "2024-08-15T10:00:00.000Z",
            deliveryTime: ************* // 13位时间戳
          }
        ],
        freight: "10.00",
        totalQuantity: 2,
        totalAmount: 210.00,
        grossProfitRate: "14.3%",
        costTotal: "95.00",
        buyerAccount: "<EMAIL>",
        businessRemark: "业务备注",
        logisticsInfo: "物流信息",
        purchaseProgress: "货期",
        purchaseRemark: "采购备注",
        channelId: "1",
        channelName: "渠道名称",
        actualSupplier: null,
        actualSupplierId: null,
        suggestedSupplier: null,
        allItemsHaveTaxCategory: true,
        erpStatus: "待同步",
        logisticsNumber: "LOG123456",
        splitOrderStatus: "未拆分",
        auditStatus: "待审核",
        orderStatus: "待发货",
        purchaseStatus: "已分配",
        cancelStatus: null,
        pendingCancelStatus: null,
        linkStatus: "未链接",
        recipientName: "收货人",
        contactPhone: "***********",
        orderAddress: "收货地址",
        actualReceiver: "实际收货人",
        actualPhone: "***********",
        actualAddress: "实际收货地址",
        isLoss: "否",
        lossReason: null,
        linkGenerated: false,
        hasExpenseOrders: false,
        expenseOrderCount: 0,
        originOrderInfo: {
          orderStatus: 0,
          paymentStatus: 1,
          shippingStatus: 0,
          invoiceStatus: 0,
          orderSource: "商城",
          orderType: "商城自然单",
          createdAt: "2024-07-09T09:00:00.000Z",
          updatedAt: "2024-07-09T10:00:00.000Z"
        },
        // 新增的订单级别货期时间字段
        expectedDeliveryTime: "2024-08-15T10:00:00.000Z",
        deliveryTime: ************* // 13位时间戳
      }
    ],
    pageInfo: {
      total: 1,
      currentPage: 1,
      pageSize: 10,
      totalPage: 1
    }
  }
};

/**
 * 验证采购订单列表响应数据结构
 * @param {Object} response - API响应数据
 * @returns {Object} - 验证结果
 */
function validatePurchaseOrderListResponse(response) {
  const errors = [];
  const warnings = [];

  // 检查基本响应结构
  if (!response.data || !response.data.items) {
    errors.push("缺少 data.items 字段");
    return { isValid: false, errors, warnings };
  }

  const items = response.data.items;
  
  // 检查每个订单项
  items.forEach((order, index) => {
    // 检查订单级别的货期时间字段
    if (!order.hasOwnProperty('expectedDeliveryTime')) {
      errors.push(`订单 ${index + 1} 缺少 expectedDeliveryTime 字段`);
    }

    if (!order.hasOwnProperty('deliveryTime')) {
      errors.push(`订单 ${index + 1} 缺少 deliveryTime 字段`);
    }

    // 验证时间戳格式
    if (order.deliveryTime !== null && order.deliveryTime !== undefined) {
      if (typeof order.deliveryTime !== 'number' || order.deliveryTime.toString().length !== 13) {
        errors.push(`订单 ${index + 1} 的 deliveryTime 不是有效的13位时间戳`);
      }
    }

    // 检查商品项的货期时间字段
    if (order.products && Array.isArray(order.products)) {
      order.products.forEach((product, productIndex) => {
        if (!product.hasOwnProperty('expectedDeliveryTime')) {
          warnings.push(`订单 ${index + 1} 商品 ${productIndex + 1} 缺少 expectedDeliveryTime 字段`);
        }

        if (!product.hasOwnProperty('deliveryTime')) {
          warnings.push(`订单 ${index + 1} 商品 ${productIndex + 1} 缺少 deliveryTime 字段`);
        }

        // 验证商品项时间戳格式
        if (product.deliveryTime !== null && product.deliveryTime !== undefined) {
          if (typeof product.deliveryTime !== 'number' || product.deliveryTime.toString().length !== 13) {
            warnings.push(`订单 ${index + 1} 商品 ${productIndex + 1} 的 deliveryTime 不是有效的13位时间戳`);
          }
        }
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 运行验证测试
 */
function runValidationTests() {
  console.log("🔍 开始验证采购订单列表API货期时间字段...\n");

  // 测试完整的响应数据
  console.log("📋 测试1: 完整的采购订单列表响应数据");
  const result = validatePurchaseOrderListResponse(mockPurchaseOrderListResponse);
  
  if (result.isValid) {
    console.log("✅ 采购订单列表数据结构验证通过");
  } else {
    console.log("❌ 采购订单列表数据结构验证失败:");
    result.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (result.warnings.length > 0) {
    console.log("⚠️  警告:");
    result.warnings.forEach(warning => console.log(`   - ${warning}`));
  }

  console.log("─".repeat(50));

  // 测试缺少字段的情况
  console.log("📋 测试2: 缺少货期时间字段的数据");
  const incompleteResponse = {
    ...mockPurchaseOrderListResponse,
    data: {
      ...mockPurchaseOrderListResponse.data,
      items: [
        {
          ...mockPurchaseOrderListResponse.data.items[0],
          // 删除货期时间字段
          expectedDeliveryTime: undefined,
          deliveryTime: undefined,
          products: [
            {
              ...mockPurchaseOrderListResponse.data.items[0].products[0],
              expectedDeliveryTime: undefined,
              deliveryTime: undefined
            }
          ]
        }
      ]
    }
  };

  const incompleteResult = validatePurchaseOrderListResponse(incompleteResponse);
  
  if (incompleteResult.isValid) {
    console.log("✅ 不完整数据验证通过（不应该发生）");
  } else {
    console.log("❌ 不完整数据验证失败（预期结果）:");
    incompleteResult.errors.forEach(error => console.log(`   - ${error}`));
  }

  console.log("\n🎉 验证测试完成!");
}

/**
 * 生成API调用示例
 */
function generateApiExamples() {
  console.log("\n📝 API调用示例:\n");

  console.log("1. 获取采购订单列表:");
  console.log(`
GET /v1/master/csm/purchase-orders?page=1&pageSize=10
Authorization: Bearer YOUR_JWT_TOKEN
`);

  console.log("2. 响应数据中的货期时间字段:");
  console.log(`
{
  "code": 200,
  "message": "获取采购订单列表成功",
  "data": {
    "items": [
      {
        "id": 77,
        "orderNumber": "PO202407090001",
        // ... 其他字段
        "expectedDeliveryTime": "2024-08-15T10:00:00.000Z", // 订单级别货期时间
        "deliveryTime": *************, // 13位时间戳
        "products": [
          {
            "id": 123,
            "productName": "测试商品",
            // ... 其他字段
            "expectedDeliveryTime": "2024-08-15T10:00:00.000Z", // 商品项级别货期时间
            "deliveryTime": ************* // 13位时间戳
          }
        ]
      }
    ]
  }
}
`);

  console.log("3. 前端使用示例:");
  console.log(`
// 获取列表数据
const response = await fetch('/v1/master/csm/purchase-orders?page=1&pageSize=10');
const data = await response.json();

// 使用货期时间数据
data.data.items.forEach(order => {
  console.log('订单货期时间:', order.expectedDeliveryTime);
  console.log('订单货期时间戳:', order.deliveryTime);
  
  order.products.forEach(product => {
    console.log('商品货期时间:', product.expectedDeliveryTime);
    console.log('商品货期时间戳:', product.deliveryTime);
  });
});
`);
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runValidationTests();
  generateApiExamples();
}

module.exports = {
  validatePurchaseOrderListResponse,
  runValidationTests,
  generateApiExamples,
  mockPurchaseOrderListResponse
};
